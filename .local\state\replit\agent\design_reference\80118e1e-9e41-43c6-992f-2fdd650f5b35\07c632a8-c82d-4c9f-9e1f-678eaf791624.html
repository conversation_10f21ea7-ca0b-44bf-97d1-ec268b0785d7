<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QaaqConnect - Maritime Community Network</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'navy': '#1e3a8a',
                        'ocean-teal': '#0891b2',
                        'seafoam': '#22d3ee',
                    },
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                    }
                }
            }
        };
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-50 font-inter">
    <!-- @COMPONENT: SplashScreen [Initial app loading screen] -->
    <div id="splash-screen" class="fixed inset-0 bg-gradient-to-br from-navy to-ocean-teal flex items-center justify-center z-50">
        <div class="text-center text-white">
            <div class="mb-8">
                <i class="fas fa-anchor text-6xl mb-4 animate-pulse"></i>
                <h1 class="text-4xl font-bold mb-2">QaaqConnect</h1>
                <p class="text-xl opacity-90">Maritime Community Network</p>
            </div>
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto"></div>
        </div>
    </div>
    <!-- @END_COMPONENT: SplashScreen -->

    <!-- @COMPONENT: OnboardingFlow [User type selection and setup] -->
    <div id="onboarding-flow" class="hidden">
        <!-- Welcome Screen -->
        <div id="welcome-screen" class="min-h-screen bg-gradient-to-br from-navy to-ocean-teal px-6 py-12">
            <div class="max-w-md mx-auto text-center text-white">
                <div class="mb-12">
                    <i class="fas fa-anchor text-5xl mb-6"></i>
                    <h1 class="text-3xl font-bold mb-4">Welcome to QaaqConnect</h1>
                    <p class="text-lg opacity-90 leading-relaxed">Connect sailors and locals worldwide. Build friendships, share cultures, and create unforgettable port experiences.</p>
                </div>
                
                <!-- A diverse group of sailors and locals socializing at a harbor -->
                <img src="https://images.unsplash.com/photo-1544551763-46a013bb70d5?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&h=600" alt="Sailors and locals socializing at harbor" class="rounded-xl shadow-lg w-full h-48 object-cover mb-8" />
                
                <button class="w-full bg-white text-navy py-4 rounded-xl font-semibold text-lg shadow-lg hover:shadow-xl transition-shadow" onclick="showUserTypeSelection()">
                    Get Started
                </button>
            </div>
        </div>

        <!-- User Type Selection -->
        <div id="user-type-screen" class="hidden min-h-screen bg-gray-50 px-6 py-12">
            <div class="max-w-md mx-auto">
                <div class="text-center mb-12">
                    <h2 class="text-2xl font-bold text-gray-900 mb-4">Choose Your Profile</h2>
                    <p class="text-gray-600">How would you like to connect with others?</p>
                </div>

                <div class="space-y-6">
                    <!-- Sailor Option -->
                    <div class="bg-white rounded-xl p-6 shadow-lg border-2 border-transparent hover:border-ocean-teal transition-colors cursor-pointer" onclick="selectUserType('sailor')">
                        <div class="flex items-center space-x-4">
                            <div class="w-16 h-16 bg-navy rounded-full flex items-center justify-center text-white text-2xl">
                                🚢
                            </div>
                            <div class="flex-1">
                                <h3 class="text-xl font-semibold text-gray-900 mb-2">I'm a Sailor</h3>
                                <p class="text-gray-600">Explore ports, meet locals, and discover hidden gems in every city you visit.</p>
                            </div>
                        </div>
                    </div>

                    <!-- Local Option -->
                    <div class="bg-white rounded-xl p-6 shadow-lg border-2 border-transparent hover:border-ocean-teal transition-colors cursor-pointer" onclick="selectUserType('local')">
                        <div class="flex items-center space-x-4">
                            <div class="w-16 h-16 bg-ocean-teal rounded-full flex items-center justify-center text-white text-2xl">
                                🏠
                            </div>
                            <div class="flex-1">
                                <h3 class="text-xl font-semibold text-gray-900 mb-2">I'm a Local</h3>
                                <p class="text-gray-600">Welcome sailors to your city and share your favorite local spots.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-12 text-center">
                    <p class="text-sm text-gray-500">Part of the QAAQ Maritime Engineering Community</p>
                </div>
            </div>
        </div>
    </div>
    <!-- @END_COMPONENT: OnboardingFlow -->

    <!-- @COMPONENT: MainApp [Core application interface] -->
    <div id="main-app" class="hidden">
        <!-- Top Navigation -->
        <nav class="bg-white shadow-sm border-b px-4 py-3">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <i class="fas fa-anchor text-navy text-xl"></i>
                    <h1 class="text-xl font-bold text-gray-900">QaaqConnect</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="relative">
                        <i class="fas fa-bell text-gray-600 text-lg"></i>
                        <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">3</span>
                    </div>
                    <div class="w-8 h-8 bg-navy rounded-full flex items-center justify-center text-white text-sm">
                        PG
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content Container -->
        <div class="pb-20">
            <!-- Discovery Section -->
            <div id="discovery-section" class="p-4">
                <!-- Context-Aware Header -->
                <div class="bg-gradient-to-r from-navy to-ocean-teal rounded-xl p-6 mb-6 text-white">
                    <div class="flex items-center justify-between mb-4">
                        <div>
                            <h2 class="text-xl font-semibold mb-2" data-sailor="Let's plan your next shore leave! 🏖️" data-local="Welcome sailors to your city! 🚢">Let's plan your next shore leave! 🏖️</h2>
                            <p class="opacity-90" data-sailor="Meet friendly locals who know the area 🤝" data-local="Share your local favorites 🌟">Meet friendly locals who know the area 🤝</p>
                        </div>
                        <div class="text-4xl">
                            <span data-sailor="⚓" data-local="🌟">⚓</span>
                        </div>
                    </div>
                    
                    <!-- Quick Stats -->
                    <div class="flex space-x-4 text-sm">
                        <div class="bg-white/20 rounded-lg px-3 py-1">
                            <span data-bind="nearbyCount">12</span> nearby
                        </div>
                        <div class="bg-white/20 rounded-lg px-3 py-1">
                            <span data-bind="onlineCount">5</span> online now
                        </div>
                    </div>
                </div>

                <!-- Search and Filters -->
                <div class="mb-6">
                    <div class="relative mb-4">
                        <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                        <input type="text" placeholder="Search for people, places, or activities..." class="w-full pl-10 pr-4 py-3 bg-white rounded-xl border border-gray-200 focus:border-ocean-teal focus:ring-2 focus:ring-ocean-teal/20 focus:outline-none">
                    </div>
                    
                    <!-- Filter Pills -->
                    <div class="flex space-x-2 overflow-x-auto pb-2">
                        <button class="bg-navy text-white px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap">All</button>
                        <button class="bg-gray-100 text-gray-700 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap hover:bg-gray-200">Sailors Only 🚢</button>
                        <button class="bg-gray-100 text-gray-700 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap hover:bg-gray-200">Locals Only 🏠</button>
                        <button class="bg-gray-100 text-gray-700 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap hover:bg-gray-200">Same Language</button>
                        <button class="bg-gray-100 text-gray-700 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap hover:bg-gray-200">Verified 🛡️</button>
                    </div>
                </div>

                <!-- People Cards -->
                <div class="space-y-4">
                    <!-- @MAP: nearbyUsers.map(user => ( -->
                    <!-- Sailor Profile Card -->
                    <div class="bg-white rounded-xl shadow-sm border p-4 hover:shadow-md transition-shadow">
                        <div class="flex items-start space-x-4">
                            <div class="relative">
                                <!-- Professional sailor in uniform at ship deck -->
                                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=150&h=150" alt="Sailor profile" class="w-16 h-16 rounded-full object-cover" />
                                <div class="absolute -bottom-1 -right-1 w-6 h-6 bg-navy rounded-full flex items-center justify-center text-white text-xs">
                                    🚢
                                </div>
                                <div class="absolute -top-1 -left-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
                            </div>
                            <div class="flex-1">
                                <div class="flex items-center justify-between mb-2">
                                    <h3 class="font-semibold text-gray-900" data-bind="user.name">Captain James Rodriguez</h3>
                                    <span class="text-xs text-gray-500" data-bind="user.distance">0.8 km away</span>
                                </div>
                                <p class="text-sm text-gray-600 mb-2" data-bind="user.vessel">Chief Engineer • MT Solar Claire</p>
                                <p class="text-sm text-gray-700 mb-3" data-bind="user.bio">21+ years sailing experience. Love exploring local cultures and trying authentic food. Currently in port for 3 days.</p>
                                <div class="flex items-center justify-between">
                                    <div class="flex space-x-2">
                                        <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">English</span>
                                        <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">Spanish</span>
                                    </div>
                                    <button class="bg-ocean-teal text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-ocean-teal/90 transition-colors">
                                        Connect
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Local Profile Card -->
                    <div class="bg-white rounded-xl shadow-sm border p-4 hover:shadow-md transition-shadow">
                        <div class="flex items-start space-x-4">
                            <div class="relative">
                                <!-- Friendly local tour guide in cultural setting -->
                                <img src="https://pixabay.com/get/g2fbb992ebf1cca230fb6b6069303a918f14d436cf5d1091776279cfeca220850f871644a94972aa91ab57767b79cdff1df09d0b7e0166927435d4c0c92e2e562_1280.jpg" alt="Local guide profile" class="w-16 h-16 rounded-full object-cover" />
                                <div class="absolute -bottom-1 -right-1 w-6 h-6 bg-ocean-teal rounded-full flex items-center justify-center text-white text-xs">
                                    🏠
                                </div>
                                <div class="absolute -top-1 -left-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
                            </div>
                            <div class="flex-1">
                                <div class="flex items-center justify-between mb-2">
                                    <h3 class="font-semibold text-gray-900" data-bind="user.name">Maria Santos</h3>
                                    <span class="text-xs text-gray-500" data-bind="user.distance">1.2 km away</span>
                                </div>
                                <p class="text-sm text-gray-600 mb-2" data-bind="user.role">Local Guide & Restaurant Owner</p>
                                <p class="text-sm text-gray-700 mb-3" data-bind="user.bio">Born and raised in Istanbul. I love showing sailors the best local spots, authentic Turkish cuisine, and hidden gems tourists never find!</p>
                                <div class="flex items-center justify-between">
                                    <div class="flex space-x-2">
                                        <span class="bg-red-100 text-red-800 text-xs px-2 py-1 rounded-full">Turkish</span>
                                        <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">English</span>
                                        <span class="bg-purple-100 text-purple-800 text-xs px-2 py-1 rounded-full">🛡️ Verified</span>
                                    </div>
                                    <button class="bg-ocean-teal text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-ocean-teal/90 transition-colors">
                                        Connect
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Group Activity Card -->
                    <div class="bg-gradient-to-r from-seafoam/10 to-ocean-teal/10 rounded-xl border-2 border-dashed border-ocean-teal/30 p-4">
                        <div class="text-center">
                            <div class="w-12 h-12 bg-ocean-teal rounded-full flex items-center justify-center text-white text-xl mx-auto mb-3">
                                <i class="fas fa-users"></i>
                            </div>
                            <h3 class="font-semibold text-gray-900 mb-2">Join Local Cultural Exchange</h3>
                            <p class="text-sm text-gray-600 mb-4">Every Thursday 7PM • Traditional Turkish Cooking Class</p>
                            <div class="flex items-center justify-center space-x-2 mb-4">
                                <!-- Group of sailors and locals cooking together -->
                                <img src="https://pixabay.com/get/g58640a42cfc0861cd883c3dd6cc2990ffa6514fcaff7fcd327d95b79200344a2d031a1e9ee8341bbe041ba769612420d1560c2ee083dd2f94caa86329d10214e_1280.jpg" alt="Cultural cooking class" class="w-12 h-8 rounded object-cover" />
                                <span class="text-sm text-gray-600">+8 others joining</span>
                            </div>
                            <button class="bg-ocean-teal text-white px-6 py-2 rounded-lg text-sm font-medium hover:bg-ocean-teal/90 transition-colors">
                                Join Event
                            </button>
                        </div>
                    </div>
                    <!-- @END_MAP )) -->
                </div>
            </div>

            <!-- Map Section -->
            <div id="map-section" class="hidden p-4">
                <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                    <div class="h-96 bg-gray-200 relative">
                        <!-- Map placeholder with pins -->
                        <div class="absolute inset-0 bg-gradient-to-br from-blue-100 to-teal-100 flex items-center justify-center">
                            <div class="text-center">
                                <i class="fas fa-map-marked-alt text-4xl text-gray-400 mb-4"></i>
                                <p class="text-gray-600">Interactive Map Loading...</p>
                                <p class="text-sm text-gray-500 mt-2">Showing sailors (🚢) and locals (🏠) nearby</p>
                            </div>
                        </div>
                        
                        <!-- Sample Pins -->
                        <div class="absolute top-20 left-16 w-8 h-8 bg-navy rounded-full flex items-center justify-center text-white text-xs shadow-lg animate-pulse">
                            🚢
                        </div>
                        <div class="absolute top-32 right-20 w-8 h-8 bg-ocean-teal rounded-full flex items-center justify-center text-white text-xs shadow-lg animate-pulse">
                            🏠
                        </div>
                        <div class="absolute bottom-24 left-24 w-8 h-8 bg-navy rounded-full flex items-center justify-center text-white text-xs shadow-lg animate-pulse">
                            🚢
                        </div>
                        <div class="absolute bottom-20 right-16 w-8 h-8 bg-ocean-teal rounded-full flex items-center justify-center text-white text-xs shadow-lg animate-pulse">
                            🏠
                        </div>
                    </div>
                    
                    <!-- Map Controls -->
                    <div class="p-4 border-t">
                        <div class="flex items-center justify-between">
                            <div class="flex space-x-4">
                                <div class="flex items-center space-x-2">
                                    <div class="w-4 h-4 bg-navy rounded-full"></div>
                                    <span class="text-sm text-gray-600">Sailors</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <div class="w-4 h-4 bg-ocean-teal rounded-full"></div>
                                    <span class="text-sm text-gray-600">Locals</span>
                                </div>
                            </div>
                            <button class="bg-gray-100 text-gray-700 px-3 py-1 rounded-lg text-sm hover:bg-gray-200">
                                <i class="fas fa-layer-group mr-1"></i>
                                Filters
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Messages Section -->
            <div id="messages-section" class="hidden p-4">
                <div class="space-y-4">
                    <!-- @MAP: conversations.map(conversation => ( -->
                    <div class="bg-white rounded-xl shadow-sm border p-4 hover:shadow-md transition-shadow cursor-pointer">
                        <div class="flex items-center space-x-4">
                            <div class="relative">
                                <!-- Sailor messaging interface -->
                                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=150&h=150" alt="Chat contact" class="w-12 h-12 rounded-full object-cover" />
                                <div class="absolute -bottom-1 -right-1 w-5 h-5 bg-ocean-teal rounded-full flex items-center justify-center text-white text-xs">
                                    🏠
                                </div>
                                <div class="absolute -top-1 -left-1 w-3 h-3 bg-green-500 rounded-full border border-white"></div>
                            </div>
                            <div class="flex-1">
                                <div class="flex items-center justify-between mb-1">
                                    <h3 class="font-semibold text-gray-900" data-bind="contact.name">Ahmed Hassan</h3>
                                    <span class="text-xs text-gray-500" data-bind="message.time">2 min ago</span>
                                </div>
                                <p class="text-sm text-gray-600" data-bind="lastMessage">Great! I'll meet you at the harbor café at 6 PM. I know the perfect place for authentic seafood! 🦐</p>
                            </div>
                            <div class="w-2 h-2 bg-ocean-teal rounded-full"></div>
                        </div>
                    </div>

                    <div class="bg-white rounded-xl shadow-sm border p-4 hover:shadow-md transition-shadow cursor-pointer">
                        <div class="flex items-center space-x-4">
                            <div class="relative">
                                <!-- Local host conversation -->
                                <img src="https://images.unsplash.com/photo-1438761681033-6461ffad8d80?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=150&h=150" alt="Chat contact" class="w-12 h-12 rounded-full object-cover" />
                                <div class="absolute -bottom-1 -right-1 w-5 h-5 bg-navy rounded-full flex items-center justify-center text-white text-xs">
                                    🚢
                                </div>
                            </div>
                            <div class="flex-1">
                                <div class="flex items-center justify-between mb-1">
                                    <h3 class="font-semibold text-gray-900" data-bind="contact.name">Captain Lisa Chen</h3>
                                    <span class="text-xs text-gray-500" data-bind="message.time">1 hour ago</span>
                                </div>
                                <p class="text-sm text-gray-600" data-bind="lastMessage">Thanks for the city tour recommendation! The spice market was incredible. Our crew loved it!</p>
                            </div>
                        </div>
                    </div>
                    <!-- @END_MAP )) -->
                </div>
            </div>

            <!-- Profile Section -->
            <div id="profile-section" class="hidden p-4">
                <div class="bg-white rounded-xl shadow-sm border p-6">
                    <!-- Profile Header -->
                    <div class="text-center mb-6">
                        <!-- Maritime professional profile photo -->
                        <img src="https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=150&h=150" alt="Profile photo" class="w-24 h-24 rounded-full object-cover mx-auto mb-4" />
                        <h2 class="text-xl font-bold text-gray-900 mb-1" data-bind="user.name">Piyush Gupta</h2>
                        <p class="text-gray-600 mb-2" data-bind="user.title">Marine Engineer • 21+ years experience</p>
                        <div class="flex items-center justify-center space-x-2">
                            <div class="w-6 h-6 bg-navy rounded-full flex items-center justify-center text-white text-xs">
                                🚢
                            </div>
                            <span class="text-sm text-gray-600">Sailor Profile</span>
                        </div>
                    </div>

                    <!-- Profile Stats -->
                    <div class="grid grid-cols-3 gap-4 mb-6">
                        <div class="text-center">
                            <div class="text-2xl font-bold text-navy" data-bind="stats.connections">47</div>
                            <div class="text-sm text-gray-600">Connections</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-ocean-teal" data-bind="stats.ports">23</div>
                            <div class="text-sm text-gray-600">Ports Visited</div>
                        </div>
                        <div class="text-center">
                            <div class="text-2xl font-bold text-seafoam" data-bind="stats.rating">4.9</div>
                            <div class="text-sm text-gray-600">Rating</div>
                        </div>
                    </div>

                    <!-- About Section -->
                    <div class="mb-6">
                        <h3 class="font-semibold text-gray-900 mb-2">About</h3>
                        <p class="text-gray-600 text-sm leading-relaxed" data-bind="user.bio">Marine Engineer with 21+ years of Industry expertise. Love connecting with locals in every port, learning about different cultures, and sharing maritime stories. Currently sailing on chemical tankers.</p>
                    </div>

                    <!-- Current Location -->
                    <div class="mb-6">
                        <h3 class="font-semibold text-gray-900 mb-2">Current Location</h3>
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-map-marker-alt text-ocean-teal"></i>
                            <span class="text-gray-600" data-bind="user.location">Istanbul, Turkey</span>
                            <span class="text-sm text-gray-500">• In port for 3 days</span>
                        </div>
                    </div>

                    <!-- Languages -->
                    <div class="mb-6">
                        <h3 class="font-semibold text-gray-900 mb-2">Languages</h3>
                        <div class="flex flex-wrap gap-2">
                            <span class="bg-blue-100 text-blue-800 text-sm px-3 py-1 rounded-full">English</span>
                            <span class="bg-green-100 text-green-800 text-sm px-3 py-1 rounded-full">Hindi</span>
                            <span class="bg-purple-100 text-purple-800 text-sm px-3 py-1 rounded-full">Basic Turkish</span>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="space-y-3">
                        <button class="w-full bg-navy text-white py-3 rounded-xl font-semibold hover:bg-navy/90 transition-colors">
                            Edit Profile
                        </button>
                        <button class="w-full bg-gray-100 text-gray-700 py-3 rounded-xl font-semibold hover:bg-gray-200 transition-colors">
                            Settings
                        </button>
                    </div>
                </div>

                <!-- Recent Activity -->
                <div class="mt-6 bg-white rounded-xl shadow-sm border p-6">
                    <h3 class="font-semibold text-gray-900 mb-4">Recent Activity</h3>
                    <div class="space-y-4">
                        <div class="flex items-start space-x-3">
                            <div class="w-8 h-8 bg-ocean-teal rounded-full flex items-center justify-center text-white text-xs">
                                🌟
                            </div>
                            <div class="flex-1">
                                <p class="text-sm text-gray-700">Connected with <strong>Maria Santos</strong> in Istanbul</p>
                                <p class="text-xs text-gray-500">2 hours ago</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="w-8 h-8 bg-navy rounded-full flex items-center justify-center text-white text-xs">
                                📍
                            </div>
                            <div class="flex-1">
                                <p class="text-sm text-gray-700">Checked in at Istanbul Port</p>
                                <p class="text-xs text-gray-500">1 day ago</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bottom Navigation -->
        <nav class="fixed bottom-0 left-0 right-0 bg-white border-t shadow-lg">
            <div class="flex items-center justify-around py-2">
                <button class="flex flex-col items-center py-2 px-3 text-ocean-teal" onclick="showSection('discovery')">
                    <i class="fas fa-compass text-xl mb-1"></i>
                    <span class="text-xs font-medium">Discover</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3 text-gray-500" onclick="showSection('map')">
                    <i class="fas fa-map text-xl mb-1"></i>
                    <span class="text-xs font-medium">Map</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3 text-gray-500 relative" onclick="showSection('messages')">
                    <i class="fas fa-comments text-xl mb-1"></i>
                    <span class="text-xs font-medium">Messages</span>
                    <div class="absolute -top-1 -right-1 w-5 h-5 bg-red-500 rounded-full flex items-center justify-center">
                        <span class="text-white text-xs">2</span>
                    </div>
                </button>
                <button class="flex flex-col items-center py-2 px-3 text-gray-500" onclick="showSection('profile')">
                    <i class="fas fa-user text-xl mb-1"></i>
                    <span class="text-xs font-medium">Profile</span>
                </button>
            </div>
        </nav>
    </div>
    <!-- @END_COMPONENT: MainApp -->

    <!-- Floating Action Button -->
    <button id="quick-connect-fab" class="hidden fixed bottom-24 right-6 w-14 h-14 bg-ocean-teal text-white rounded-full shadow-lg hover:shadow-xl transition-shadow flex items-center justify-center text-xl">
        <i class="fas fa-plus"></i>
    </button>

    <script>
        // TODO: Implement proper app state management and routing
        (function() {
            let currentUserType = 'sailor';
            let currentSection = 'discovery';

            // Initialize app
            function initApp() {
                // Hide splash screen after 2 seconds
                setTimeout(() => {
                    document.getElementById('splash-screen').classList.add('hidden');
                    document.getElementById('onboarding-flow').classList.remove('hidden');
                }, 2000);
            }

            // Show user type selection
            window.showUserTypeSelection = function() {
                document.getElementById('welcome-screen').classList.add('hidden');
                document.getElementById('user-type-screen').classList.remove('hidden');
            };

            // Select user type and proceed to main app
            window.selectUserType = function(type) {
                currentUserType = type;
                document.getElementById('onboarding-flow').classList.add('hidden');
                document.getElementById('main-app').classList.remove('hidden');
                document.getElementById('quick-connect-fab').classList.remove('hidden');
                
                updateContextualContent();
            };

            // Update content based on user type
            function updateContextualContent() {
                const elements = document.querySelectorAll('[data-sailor][data-local]');
                elements.forEach(el => {
                    el.textContent = el.getAttribute(`data-${currentUserType}`);
                });
            }

            // Show different sections
            window.showSection = function(section) {
                // Hide all sections
                const sections = ['discovery', 'map', 'messages', 'profile'];
                sections.forEach(s => {
                    document.getElementById(`${s}-section`).classList.add('hidden');
                });
                
                // Show selected section
                document.getElementById(`${section}-section`).classList.remove('hidden');
                currentSection = section;
                
                // Update navigation
                updateNavigation(section);
            };

            // Update navigation active state
            function updateNavigation(activeSection) {
                const navButtons = document.querySelectorAll('nav button');
                navButtons.forEach((button, index) => {
                    const sections = ['discovery', 'map', 'messages', 'profile'];
                    if (sections[index] === activeSection) {
                        button.classList.remove('text-gray-500');
                        button.classList.add('text-ocean-teal');
                    } else {
                        button.classList.remove('text-ocean-teal');
                        button.classList.add('text-gray-500');
                    }
                });
            }

            // TODO: Implement real-time messaging
            // TODO: Implement map integration with geolocation
            // TODO: Implement user authentication and profile management
            // TODO: Implement push notifications for new connections

            // Start the app
            initApp();
        })();
    </script>
</body>
</html>