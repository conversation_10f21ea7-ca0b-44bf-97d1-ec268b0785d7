<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>KOI - Maritime Community Events</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'navy': '#1B365D',
                        'ocean': '#0891B2',
                        'ocean-light': '#06B6D4',
                        'maritime-gold': '#F59E0B',
                        'teal-50': '#F0FDFA',
                        'teal-100': '#CCFBF1'
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif']
                    }
                }
            }
        };
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-50 font-sans">
    <!-- @COMPONENT: NavigationHeader -->
    <header class="bg-navy shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <i class="fas fa-fish text-ocean-light text-2xl mr-2"></i>
                        <span class="text-white text-xl font-bold">KOI</span>
                        <span class="text-ocean-light text-sm ml-2 hidden sm:block">Maritime Events</span>
                    </div>
                </div>
                
                <nav class="hidden md:flex space-x-8">
                    <a href="#" class="text-ocean-light hover:text-white px-3 py-2 text-sm font-medium transition-colors">
                        <i class="fas fa-calendar-alt mr-1"></i>Events
                    </a>
                    <a href="#" class="text-gray-300 hover:text-white px-3 py-2 text-sm font-medium transition-colors">
                        <i class="fas fa-map-marker-alt mr-1"></i>Nearby
                    </a>
                    <a href="#" class="text-gray-300 hover:text-white px-3 py-2 text-sm font-medium transition-colors">
                        <i class="fas fa-users mr-1"></i>My Groups
                    </a>
                </nav>

                <div class="flex items-center space-x-4">
                    <!-- WhatsApp Integration Indicator -->
                    <div class="hidden sm:flex items-center bg-green-600 text-white px-3 py-1 rounded-full text-xs">
                        <i class="fab fa-whatsapp mr-1"></i>
                        <span>Connected</span>
                    </div>
                    
                    <!-- QAAQ Profile Integration -->
                    <div class="flex items-center">
                        <img class="h-8 w-8 rounded-full border-2 border-ocean-light" src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=64&h=64" alt="Profile" data-mock="true">
                        <span class="text-white text-sm ml-2 hidden lg:block" data-bind="user.rank">Chief Engineer</span>
                    </div>
                    
                    <button class="md:hidden text-white">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>
    <!-- @END_COMPONENT: NavigationHeader -->

    <!-- @COMPONENT: HeroSection -->
    <section class="bg-gradient-to-br from-navy via-ocean to-ocean-light text-white py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h1 class="text-4xl md:text-6xl font-bold mb-6">
                Connect with Maritime<br>
                <span class="text-maritime-gold">Community Events</span>
            </h1>
            <p class="text-xl md:text-2xl mb-8 text-ocean-light max-w-3xl mx-auto">
                Join shore leave activities, group tours, and professional meetups with verified maritime professionals worldwide
            </p>
            
            <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
                <button class="bg-maritime-gold hover:bg-yellow-500 text-navy px-8 py-4 rounded-lg font-semibold text-lg transition-colors shadow-lg" data-event="click:createEvent">
                    <i class="fas fa-plus mr-2"></i>Create Event
                </button>
                <button class="border-2 border-white text-white hover:bg-white hover:text-navy px-8 py-4 rounded-lg font-semibold text-lg transition-colors" data-event="click:browseEvents">
                    <i class="fas fa-search mr-2"></i>Browse Events
                </button>
            </div>

            <!-- Integration Badge -->
            <div class="mt-8 flex items-center justify-center">
                <div class="bg-white/10 backdrop-blur-sm rounded-lg px-4 py-2 flex items-center space-x-2">
                    <i class="fas fa-anchor text-maritime-gold"></i>
                    <span class="text-sm">Powered by QAAQ Maritime Network</span>
                </div>
            </div>
        </div>
    </section>
    <!-- @END_COMPONENT: HeroSection -->

    <!-- @COMPONENT: FilterSection -->
    <section class="bg-white shadow-md sticky top-16 z-40">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
            <div class="flex flex-col lg:flex-row gap-4 items-center">
                <!-- Location Filter -->
                <div class="flex items-center space-x-2">
                    <i class="fas fa-map-marker-alt text-ocean"></i>
                    <select class="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-ocean focus:border-transparent" data-bind="selectedLocation">
                        <option>Singapore</option>
                        <option>Rotterdam</option>
                        <option>Dubai</option>
                        <option>Hamburg</option>
                    </select>
                    <span class="text-sm text-gray-500">within</span>
                    <select class="border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-ocean focus:border-transparent" data-bind="selectedRadius">
                        <option>5 km</option>
                        <option>10 km</option>
                        <option>25 km</option>
                        <option>50 km</option>
                    </select>
                </div>

                <!-- Category Filters -->
                <div class="flex flex-wrap gap-2">
                    <button class="bg-ocean text-white px-4 py-2 rounded-full text-sm font-medium" data-category="shore-leave">
                        <i class="fas fa-anchor mr-1"></i>Shore Leave
                    </button>
                    <button class="bg-gray-200 hover:bg-ocean hover:text-white px-4 py-2 rounded-full text-sm font-medium transition-colors" data-category="city-tour">
                        <i class="fas fa-walking mr-1"></i>City Tours
                    </button>
                    <button class="bg-gray-200 hover:bg-ocean hover:text-white px-4 py-2 rounded-full text-sm font-medium transition-colors" data-category="taxi-share">
                        <i class="fas fa-taxi mr-1"></i>Share Taxi
                    </button>
                    <button class="bg-gray-200 hover:bg-ocean hover:text-white px-4 py-2 rounded-full text-sm font-medium transition-colors" data-category="networking">
                        <i class="fas fa-users mr-1"></i>Networking
                    </button>
                </div>

                <div class="ml-auto">
                    <button class="text-ocean hover:text-navy" data-event="click:toggleMapView">
                        <i class="fas fa-map text-xl"></i>
                    </button>
                </div>
            </div>
        </div>
    </section>
    <!-- @END_COMPONENT: FilterSection -->

    <!-- @COMPONENT: MainContent -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            
            <!-- Events List -->
            <div class="lg:col-span-2">
                <div class="flex justify-between items-center mb-6">
                    <h2 class="text-2xl font-bold text-gray-900">Upcoming Events</h2>
                    <span class="text-gray-500" data-bind="eventCount">24 events found</span>
                </div>

                <!-- @MAP: events.map(event => ( -->
                <div class="space-y-6" data-mock="true">
                    <!-- Featured Event Card -->
                    <div class="bg-white rounded-xl shadow-lg overflow-hidden border-l-4 border-maritime-gold">
                        <div class="relative">
                            <!-- Group of sailors enjoying shore leave in a port city with historic buildings -->
                            <img src="https://images.unsplash.com/photo-1516541196182-6bdb0516ed27?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=300" alt="Shore leave group activity" class="w-full h-48 object-cover">
                            <div class="absolute top-4 left-4">
                                <span class="bg-maritime-gold text-navy px-3 py-1 rounded-full text-sm font-semibold">Featured</span>
                            </div>
                            <div class="absolute top-4 right-4">
                                <span class="bg-green-600 text-white px-3 py-1 rounded-full text-sm flex items-center">
                                    <i class="fab fa-whatsapp mr-1"></i>Auto-Group
                                </span>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-2">
                                <h3 class="text-xl font-bold text-gray-900" data-bind="event.title">Singapore Historical District Walking Tour</h3>
                                <span class="text-sm text-gray-500" data-bind="event.distance">1.2 km away</span>
                            </div>
                            
                            <div class="flex items-center space-x-4 text-sm text-gray-600 mb-4">
                                <div class="flex items-center">
                                    <i class="fas fa-calendar-alt mr-1 text-ocean"></i>
                                    <span data-bind="event.date">Today, 2:00 PM</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-users mr-1 text-ocean"></i>
                                    <span data-bind="event.participants">8/12 joined</span>
                                </div>
                                <div class="flex items-center">
                                    <i class="fas fa-clock mr-1 text-ocean"></i>
                                    <span data-bind="event.duration">3 hours</span>
                                </div>
                            </div>

                            <p class="text-gray-700 mb-4" data-bind="event.description">
                                Explore Singapore's historic Chinatown and Little India with fellow maritime professionals. Perfect for shore leave activities!
                            </p>

                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <img class="w-8 h-8 rounded-full border-2 border-ocean" src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=32&h=32" alt="Organizer" data-mock="true">
                                    <div>
                                        <p class="text-sm font-medium text-gray-900" data-bind="event.organizer.name">Captain Rodriguez</p>
                                        <p class="text-xs text-gray-500" data-bind="event.organizer.rank">Master Mariner</p>
                                    </div>
                                </div>
                                <button class="bg-ocean hover:bg-ocean-light text-white px-6 py-2 rounded-lg font-medium transition-colors" data-event="click:joinEvent">
                                    <i class="fab fa-whatsapp mr-2"></i>Join WhatsApp Group
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Regular Event Cards -->
                    <div class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                        <div class="flex">
                            <div class="w-32 h-32 flex-shrink-0">
                                <!-- Maritime professionals sharing a taxi ride in an urban setting -->
                                <img src="https://images.unsplash.com/photo-1449824913935-59a10b8d2000?ixlib=rb-4.0.3&auto=format&fit=crop&w=128&h=128" alt="Taxi sharing activity" class="w-full h-full object-cover">
                            </div>
                            <div class="flex-1 p-4">
                                <div class="flex items-center justify-between mb-2">
                                    <h3 class="text-lg font-semibold text-gray-900" data-bind="event.title">Airport Taxi Share</h3>
                                    <span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">2.5 km</span>
                                </div>
                                <div class="flex items-center space-x-3 text-sm text-gray-600 mb-2">
                                    <span data-bind="event.time">6:00 AM tomorrow</span>
                                    <span data-bind="event.participants">2/4 joined</span>
                                </div>
                                <p class="text-sm text-gray-700 mb-3" data-bind="event.description">
                                    Share taxi to Changi Airport for morning flights. Split costs and travel together.
                                </p>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <img class="w-6 h-6 rounded-full" src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=24&h=24" alt="Organizer" data-mock="true">
                                        <span class="text-sm text-gray-600 ml-2" data-bind="event.organizer">2nd Engineer Kim</span>
                                    </div>
                                    <button class="bg-ocean text-white px-4 py-1 rounded text-sm hover:bg-ocean-light transition-colors" data-event="click:joinEvent">
                                        Join
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                        <div class="flex">
                            <div class="w-32 h-32 flex-shrink-0">
                                <!-- Group of people on a guided city tour with local landmarks -->
                                <img src="https://pixabay.com/get/g625a432b97557d919b3491cca368303d110041f1da0a724783f2f54437cccc5d52a0bd79ddf0ac625e360a7f713a7d85be225a70442eb0957996e500707f3ab8_1280.jpg" alt="City tour group" class="w-full h-full object-cover">
                            </div>
                            <div class="flex-1 p-4">
                                <div class="flex items-center justify-between mb-2">
                                    <h3 class="text-lg font-semibold text-gray-900" data-bind="event.title">Marina Bay Sunset Tour</h3>
                                    <span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">3.1 km</span>
                                </div>
                                <div class="flex items-center space-x-3 text-sm text-gray-600 mb-2">
                                    <span data-bind="event.time">Today, 5:30 PM</span>
                                    <span data-bind="event.participants">6/10 joined</span>
                                </div>
                                <p class="text-sm text-gray-700 mb-3" data-bind="event.description">
                                    Watch the sunset from Marina Bay Sands with panoramic city views. Photography welcome!
                                </p>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <img class="w-6 h-6 rounded-full" src="https://pixabay.com/get/gd5d6193224fea38b211f936e25d5491c55745e328f5e4d5c89937560808074c2b47e29cda007d7a6b8425d5a181b9209dfcc0bdb6911b8539e6f8001d864b133_1280.jpg" alt="Organizer" data-mock="true">
                                        <span class="text-sm text-gray-600 ml-2" data-bind="event.organizer">Chief Officer Sarah</span>
                                    </div>
                                    <button class="bg-ocean text-white px-4 py-1 rounded text-sm hover:bg-ocean-light transition-colors" data-event="click:joinEvent">
                                        Join
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-xl shadow-md overflow-hidden hover:shadow-lg transition-shadow">
                        <div class="flex">
                            <div class="w-32 h-32 flex-shrink-0">
                                <!-- Maritime professionals networking at a professional gathering -->
                                <img src="https://images.unsplash.com/photo-1511632765486-a01980e01a18?ixlib=rb-4.0.3&auto=format&fit=crop&w=128&h=128" alt="Professional networking event" class="w-full h-full object-cover">
                            </div>
                            <div class="flex-1 p-4">
                                <div class="flex items-center justify-between mb-2">
                                    <h3 class="text-lg font-semibold text-gray-900" data-bind="event.title">Maritime Professionals Meetup</h3>
                                    <span class="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">1.8 km</span>
                                </div>
                                <div class="flex items-center space-x-3 text-sm text-gray-600 mb-2">
                                    <span data-bind="event.time">Tomorrow, 7:00 PM</span>
                                    <span data-bind="event.participants">12/20 joined</span>
                                </div>
                                <p class="text-sm text-gray-700 mb-3" data-bind="event.description">
                                    Monthly networking dinner for maritime professionals. Share experiences and build connections.
                                </p>
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <img class="w-6 h-6 rounded-full" src="https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&auto=format&fit=crop&w=24&h=24" alt="Organizer" data-mock="true">
                                        <span class="text-sm text-gray-600 ml-2" data-bind="event.organizer">Captain Anderson</span>
                                    </div>
                                    <button class="bg-ocean text-white px-4 py-1 rounded text-sm hover:bg-ocean-light transition-colors" data-event="click:joinEvent">
                                        Join
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- @END_MAP )) -->

                <!-- Load More Button -->
                <div class="text-center mt-8">
                    <button class="bg-gray-100 hover:bg-gray-200 text-gray-700 px-6 py-3 rounded-lg font-medium transition-colors" data-event="click:loadMore">
                        Load More Events
                    </button>
                </div>
            </div>

            <!-- Sidebar -->
            <div class="lg:col-span-1">
                <!-- Quick Create Event -->
                <div class="bg-white rounded-xl shadow-md p-6 mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-plus-circle text-ocean mr-2"></i>Quick Create Event
                    </h3>
                    <div class="space-y-4">
                        <select class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-ocean focus:border-transparent">
                            <option>Shore Leave Activity</option>
                            <option>Group City Tour</option>
                            <option>Share Taxi</option>
                            <option>Professional Meetup</option>
                            <option>Get Together</option>
                        </select>
                        <input type="text" placeholder="Event title..." class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-ocean focus:border-transparent">
                        <button class="w-full bg-ocean hover:bg-ocean-light text-white py-2 rounded-lg font-medium transition-colors" data-event="click:createQuickEvent">
                            Create Event
                        </button>
                    </div>
                </div>

                <!-- WhatsApp Integration Status -->
                <div class="bg-green-50 border border-green-200 rounded-xl p-6 mb-6">
                    <div class="flex items-center mb-3">
                        <i class="fab fa-whatsapp text-green-600 text-2xl mr-3"></i>
                        <div>
                            <h3 class="font-semibold text-green-800">WhatsApp Connected</h3>
                            <p class="text-sm text-green-600" data-bind="user.whatsapp">+65 9xxx xxxx</p>
                        </div>
                    </div>
                    <p class="text-sm text-green-700 mb-3">
                        Auto-join WhatsApp groups when you join events. KOI will be added as co-admin for safety.
                    </p>
                    <button class="text-green-600 text-sm hover:text-green-800 transition-colors" data-event="click:manageWhatsApp">
                        <i class="fas fa-cog mr-1"></i>Manage Settings
                    </button>
                </div>

                <!-- QAAQ Integration -->
                <div class="bg-navy text-white rounded-xl p-6 mb-6">
                    <div class="flex items-center mb-3">
                        <i class="fas fa-anchor text-maritime-gold text-2xl mr-3"></i>
                        <div>
                            <h3 class="font-semibold">QAAQ Verified</h3>
                            <p class="text-sm text-ocean-light" data-bind="user.rank">Chief Engineer</p>
                        </div>
                    </div>
                    <p class="text-sm text-gray-300 mb-3">
                        Your maritime credentials are verified through QAAQ network.
                    </p>
                    <a href="https://qaaqit.replit.app/" class="text-maritime-gold text-sm hover:text-yellow-300 transition-colors">
                        <i class="fas fa-external-link-alt mr-1"></i>Visit QAAQ
                    </a>
                </div>

                <!-- Recent Activity -->
                <div class="bg-white rounded-xl shadow-md p-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-4">
                        <i class="fas fa-clock text-ocean mr-2"></i>Recent Activity
                    </h3>
                    <div class="space-y-3" data-mock="true">
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                            <p class="text-sm text-gray-700">You joined <strong>Marina Bay Tour</strong></p>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 bg-blue-500 rounded-full"></div>
                            <p class="text-sm text-gray-700">New event nearby: <strong>Coffee Meetup</strong></p>
                        </div>
                        <div class="flex items-center space-x-3">
                            <div class="w-2 h-2 bg-yellow-500 rounded-full"></div>
                            <p class="text-sm text-gray-700">Your <strong>Airport Taxi</strong> event starts in 2 hours</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>
    <!-- @END_COMPONENT: MainContent -->

    <!-- @COMPONENT: CreateEventModal -->
    <div class="fixed inset-0 bg-black bg-opacity-50 hidden z-50" id="createEventModal" data-modal="createEvent">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                <div class="p-6 border-b border-gray-200">
                    <div class="flex items-center justify-between">
                        <h2 class="text-2xl font-bold text-gray-900">Create New Event</h2>
                        <button class="text-gray-400 hover:text-gray-600" data-event="click:closeModal">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                </div>
                
                <form class="p-6 space-y-6" data-form="createEvent">
                    <!-- Event Category -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Event Category</label>
                        <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                            <button type="button" class="flex flex-col items-center p-4 border-2 border-gray-200 rounded-lg hover:border-ocean transition-colors" data-category="shore-leave">
                                <i class="fas fa-anchor text-2xl text-ocean mb-2"></i>
                                <span class="text-sm font-medium">Shore Leave</span>
                            </button>
                            <button type="button" class="flex flex-col items-center p-4 border-2 border-gray-200 rounded-lg hover:border-ocean transition-colors" data-category="city-tour">
                                <i class="fas fa-walking text-2xl text-ocean mb-2"></i>
                                <span class="text-sm font-medium">City Tour</span>
                            </button>
                            <button type="button" class="flex flex-col items-center p-4 border-2 border-gray-200 rounded-lg hover:border-ocean transition-colors" data-category="taxi-share">
                                <i class="fas fa-taxi text-2xl text-ocean mb-2"></i>
                                <span class="text-sm font-medium">Share Taxi</span>
                            </button>
                        </div>
                    </div>

                    <!-- Event Details -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Event Title</label>
                            <input type="text" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-ocean focus:border-transparent" placeholder="Enter event title...">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Max Participants</label>
                            <input type="number" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-ocean focus:border-transparent" placeholder="10">
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                        <textarea rows="3" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-ocean focus:border-transparent" placeholder="Describe your event..."></textarea>
                    </div>

                    <!-- Date and Time -->
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Date</label>
                            <input type="date" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-ocean focus:border-transparent">
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Time</label>
                            <input type="time" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-ocean focus:border-transparent">
                        </div>
                    </div>

                    <!-- Location -->
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Meeting Location</label>
                        <input type="text" class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-ocean focus:border-transparent" placeholder="Enter meeting point...">
                    </div>

                    <!-- WhatsApp Group Settings -->
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                        <div class="flex items-center mb-2">
                            <i class="fab fa-whatsapp text-green-600 mr-2"></i>
                            <h3 class="font-medium text-green-800">WhatsApp Group Settings</h3>
                        </div>
                        <p class="text-sm text-green-700 mb-3">
                            A WhatsApp group will be automatically created when someone joins this event.
                        </p>
                        <label class="flex items-center">
                            <input type="checkbox" class="form-checkbox text-green-600" checked>
                            <span class="ml-2 text-sm text-green-700">Add KOI as co-admin for group safety</span>
                        </label>
                    </div>

                    <div class="flex justify-end space-x-4">
                        <button type="button" class="px-6 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors" data-event="click:closeModal">
                            Cancel
                        </button>
                        <button type="submit" class="px-6 py-2 bg-ocean text-white rounded-lg hover:bg-ocean-light transition-colors" data-event="submit:createEvent">
                            <i class="fas fa-plus mr-2"></i>Create Event
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    <!-- @END_COMPONENT: CreateEventModal -->

    <!-- @COMPONENT: Footer -->
    <footer class="bg-navy text-white mt-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div class="col-span-1 md:col-span-2">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-fish text-ocean-light text-3xl mr-3"></i>
                        <div>
                            <h3 class="text-xl font-bold">KOI</h3>
                            <p class="text-ocean-light text-sm">Maritime Community Events</p>
                        </div>
                    </div>
                    <p class="text-gray-300 mb-4 max-w-md">
                        Connecting maritime professionals worldwide through local events, shore leave activities, and professional networking opportunities.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-ocean-light hover:text-white transition-colors">
                            <i class="fab fa-whatsapp text-xl"></i>
                        </a>
                        <a href="#" class="text-ocean-light hover:text-white transition-colors">
                            <i class="fab fa-telegram text-xl"></i>
                        </a>
                    </div>
                </div>
                
                <div>
                    <h4 class="font-semibold mb-4">Platform</h4>
                    <ul class="space-y-2 text-sm text-gray-300">
                        <li><a href="#" class="hover:text-white transition-colors">Browse Events</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Create Event</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">WhatsApp Bot</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Safety Guidelines</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="font-semibold mb-4">Support</h4>
                    <ul class="space-y-2 text-sm text-gray-300">
                        <li><a href="#" class="hover:text-white transition-colors">Help Center</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Contact Us</a></li>
                        <li><a href="https://qaaqit.replit.app/" class="hover:text-white transition-colors">QAAQ Platform</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Privacy Policy</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t border-gray-700 mt-8 pt-8 text-center">
                <p class="text-gray-300 text-sm">
                    © 2024 KOI Maritime Events. Part of the QAAQ maritime ecosystem. 
                    <a href="https://qaaqit.replit.app/" class="text-maritime-gold hover:text-yellow-300 transition-colors">Powered by QAAQ</a>
                </p>
            </div>
        </div>
    </footer>
    <!-- @END_COMPONENT: Footer -->

    <!-- Mobile Bottom Navigation -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 md:hidden z-40">
        <div class="grid grid-cols-4 h-16">
            <button class="flex flex-col items-center justify-center text-ocean" data-nav="events">
                <i class="fas fa-calendar-alt text-xl mb-1"></i>
                <span class="text-xs">Events</span>
            </button>
            <button class="flex flex-col items-center justify-center text-gray-400" data-nav="nearby">
                <i class="fas fa-map-marker-alt text-xl mb-1"></i>
                <span class="text-xs">Nearby</span>
            </button>
            <button class="flex flex-col items-center justify-center text-gray-400" data-nav="groups">
                <i class="fas fa-users text-xl mb-1"></i>
                <span class="text-xs">Groups</span>
            </button>
            <button class="flex flex-col items-center justify-center text-gray-400" data-nav="profile">
                <i class="fas fa-user text-xl mb-1"></i>
                <span class="text-xs">Profile</span>
            </button>
        </div>
    </nav>

    <script>
        (function() {
            // TODO: Implement event management, WhatsApp integration, and location services
            
            // Modal functionality
            const createButtons = document.querySelectorAll('[data-event="click:createEvent"]');
            const modal = document.getElementById('createEventModal');
            const closeButtons = document.querySelectorAll('[data-event="click:closeModal"]');

            createButtons.forEach(button => {
                button.addEventListener('click', () => {
                    modal.classList.remove('hidden');
                });
            });

            closeButtons.forEach(button => {
                button.addEventListener('click', () => {
                    modal.classList.add('hidden');
                });
            });

            // Category selection
            const categoryButtons = document.querySelectorAll('[data-category]');
            categoryButtons.forEach(button => {
                button.addEventListener('click', () => {
                    // Remove active state from all buttons
                    document.querySelectorAll('[data-category]').forEach(btn => {
                        btn.classList.remove('bg-ocean', 'text-white');
                        btn.classList.add('bg-gray-200');
                    });
                    
                    // Add active state to clicked button
                    button.classList.remove('bg-gray-200');
                    button.classList.add('bg-ocean', 'text-white');
                });
            });

            // Form submission simulation
            const forms = document.querySelectorAll('[data-form]');
            forms.forEach(form => {
                form.addEventListener('submit', (e) => {
                    e.preventDefault();
                    // TODO: Implement form submission logic
                    console.log('Form submitted:', form.dataset.form);
                });
            });

            // Join event buttons
            const joinButtons = document.querySelectorAll('[data-event="click:joinEvent"]');
            joinButtons.forEach(button => {
                button.addEventListener('click', () => {
                    // TODO: Implement WhatsApp group joining logic
                    button.innerHTML = '<i class="fas fa-check mr-2"></i>Joined';
                    button.classList.remove('bg-ocean');
                    button.classList.add('bg-green-600');
                    button.disabled = true;
                });
            });
        })();
    </script>
</body>
</html>