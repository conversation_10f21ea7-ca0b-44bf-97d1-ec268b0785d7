<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Koi Maritime - Officer Discovery & Networking</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'maritime-navy': '#1B365D',
                        'ocean-teal': '#2DD4BF',
                        'maritime-blue': '#0F4C75',
                        'light-teal': '#A7F3D0',
                        'gold-accent': '#FCD34D'
                    },
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif']
                    }
                }
            }
        };
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-slate-50 font-inter">
    <!-- Top Navigation -->
    <nav class="bg-maritime-navy text-white px-4 py-3 flex items-center justify-between sticky top-0 z-50 shadow-lg">
        <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-ocean-teal rounded-full flex items-center justify-center">
                <i class="fas fa-anchor text-white text-sm"></i>
            </div>
            <div>
                <h1 class="text-lg font-bold">Koi Maritime</h1>
                <p class="text-xs text-gray-300">Officer Discovery</p>
            </div>
        </div>
        <div class="flex items-center space-x-4">
            <button class="text-ocean-teal">
                <i class="fas fa-search text-lg"></i>
            </button>
            <button class="text-ocean-teal">
                <i class="fas fa-comments text-lg"></i>
            </button>
            <button class="text-ocean-teal">
                <i class="fas fa-user-circle text-lg"></i>
            </button>
        </div>
    </nav>

    <!-- Main Content Container -->
    <div class="max-w-md mx-auto bg-white min-h-screen">
        
        <!-- Quick Stats Banner -->
        <div class="bg-gradient-to-r from-maritime-blue to-ocean-teal text-white p-4 m-4 rounded-xl shadow-lg">
            <div class="flex justify-between items-center">
                <div class="text-center">
                    <p class="text-2xl font-bold">127</p>
                    <p class="text-xs opacity-90">Officers Nearby</p>
                </div>
                <div class="text-center">
                    <p class="text-2xl font-bold">43</p>
                    <p class="text-xs opacity-90">Same Rank</p>
                </div>
                <div class="text-center">
                    <p class="text-2xl font-bold">8</p>
                    <p class="text-xs opacity-90">Your Vessel</p>
                </div>
            </div>
        </div>

        <!-- Filter Tabs -->
        <div class="px-4 py-2">
            <div class="flex space-x-2 bg-gray-100 rounded-lg p-1">
                <button class="flex-1 bg-maritime-navy text-white px-3 py-2 rounded-md text-sm font-medium">
                    <i class="fas fa-users mr-1"></i>Who's There?
                </button>
                <button class="flex-1 px-3 py-2 text-gray-600 text-sm font-medium">
                    <i class="fas fa-ship mr-1"></i>Same Vessel
                </button>
                <button class="flex-1 px-3 py-2 text-gray-600 text-sm font-medium">
                    <i class="fas fa-star mr-1"></i>Same Rank
                </button>
            </div>
        </div>

        <!-- Rank Filter -->
        <div class="px-4 py-2">
            <div class="flex space-x-2 overflow-x-auto">
                <button class="bg-gold-accent text-maritime-navy px-3 py-1 rounded-full text-xs font-medium whitespace-nowrap">All Ranks</button>
                <button class="bg-gray-200 text-gray-700 px-3 py-1 rounded-full text-xs font-medium whitespace-nowrap">Chief Engineer</button>
                <button class="bg-gray-200 text-gray-700 px-3 py-1 rounded-full text-xs font-medium whitespace-nowrap">2nd Engineer</button>
                <button class="bg-gray-200 text-gray-700 px-3 py-1 rounded-full text-xs font-medium whitespace-nowrap">3rd Engineer</button>
            </div>
        </div>

        <!-- Officer Discovery Cards -->
        <div class="px-4 space-y-4 pb-20">
            
            <!-- Officer Card 1 - Chief Engineer -->
            <!-- @COMPONENT: OfficerCard [name, rank, vessel, distance, experience, photo] -->
            <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-4 hover:shadow-xl transition-shadow">
                <div class="flex items-start space-x-4">
                    <!-- Professional maritime officer portrait in uniform -->
                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=150&h=150" alt="Chief Engineer Portrait" class="w-16 h-16 rounded-full object-cover border-2 border-ocean-teal">
                    <div class="flex-1">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-bold text-gray-900" data-bind="officer.name">Piyush Gupta</h3>
                            <span class="bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs font-bold">
                                <i class="fas fa-crown mr-1"></i>Chief Engineer
                            </span>
                        </div>
                        <p class="text-gray-600 text-sm mt-1" data-bind="officer.vessel">MT Solar Claire</p>
                        <div class="flex items-center space-x-4 mt-2">
                            <span class="text-ocean-teal text-sm font-medium">
                                <i class="fas fa-map-marker-alt mr-1"></i>2.3 km away
                            </span>
                            <span class="text-gray-500 text-sm">21+ years exp</span>
                        </div>
                        <div class="flex items-center space-x-2 mt-3">
                            <button class="bg-ocean-teal text-white px-4 py-2 rounded-lg text-sm font-medium flex-1">
                                <i class="fab fa-whatsapp mr-1"></i>Connect
                            </button>
                            <button class="bg-gray-100 text-gray-700 px-3 py-2 rounded-lg text-sm">
                                <i class="fas fa-info-circle"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <!-- @END_COMPONENT: OfficerCard -->

            <!-- Officer Card 2 - 2nd Engineer -->
            <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-4 hover:shadow-xl transition-shadow">
                <div class="flex items-start space-x-4">
                    <!-- Maritime officer in casual uniform setting -->
                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=150&h=150" alt="2nd Engineer Portrait" class="w-16 h-16 rounded-full object-cover border-2 border-ocean-teal">
                    <div class="flex-1">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-bold text-gray-900">Marcus Rodriguez</h3>
                            <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-bold">
                                <i class="fas fa-cog mr-1"></i>2nd Engineer
                            </span>
                        </div>
                        <p class="text-gray-600 text-sm mt-1">MV Northern Star</p>
                        <div class="flex items-center space-x-4 mt-2">
                            <span class="text-ocean-teal text-sm font-medium">
                                <i class="fas fa-map-marker-alt mr-1"></i>5.1 km away
                            </span>
                            <span class="text-gray-500 text-sm">12 years exp</span>
                        </div>
                        <div class="flex items-center space-x-2 mt-3">
                            <button class="bg-ocean-teal text-white px-4 py-2 rounded-lg text-sm font-medium flex-1">
                                <i class="fab fa-whatsapp mr-1"></i>Connect
                            </button>
                            <button class="bg-gray-100 text-gray-700 px-3 py-2 rounded-lg text-sm">
                                <i class="fas fa-info-circle"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Officer Card 3 - Captain -->
            <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-4 hover:shadow-xl transition-shadow">
                <div class="flex items-start space-x-4">
                    <!-- Senior maritime captain in formal uniform -->
                    <img src="https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=150&h=150" alt="Captain Portrait" class="w-16 h-16 rounded-full object-cover border-2 border-ocean-teal">
                    <div class="flex-1">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-bold text-gray-900">Captain Elena Müller</h3>
                            <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-xs font-bold">
                                <i class="fas fa-anchor mr-1"></i>Captain
                            </span>
                        </div>
                        <p class="text-gray-600 text-sm mt-1">MS Baltic Explorer</p>
                        <div class="flex items-center space-x-4 mt-2">
                            <span class="text-ocean-teal text-sm font-medium">
                                <i class="fas fa-map-marker-alt mr-1"></i>1.8 km away
                            </span>
                            <span class="text-gray-500 text-sm">25+ years exp</span>
                        </div>
                        <div class="flex items-center space-x-2 mt-3">
                            <button class="bg-ocean-teal text-white px-4 py-2 rounded-lg text-sm font-medium flex-1">
                                <i class="fab fa-whatsapp mr-1"></i>Connect
                            </button>
                            <button class="bg-gray-100 text-gray-700 px-3 py-2 rounded-lg text-sm">
                                <i class="fas fa-info-circle"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Officer Card 4 - 3rd Engineer -->
            <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-4 hover:shadow-xl transition-shadow">
                <div class="flex items-start space-x-4">
                    <!-- Young maritime engineer in working uniform -->
                    <img src="https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=150&h=150" alt="3rd Engineer Portrait" class="w-16 h-16 rounded-full object-cover border-2 border-ocean-teal">
                    <div class="flex-1">
                        <div class="flex items-center justify-between">
                            <h3 class="text-lg font-bold text-gray-900">James Kim</h3>
                            <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-bold">
                                <i class="fas fa-wrench mr-1"></i>3rd Engineer
                            </span>
                        </div>
                        <p class="text-gray-600 text-sm mt-1">MT Ocean Pearl</p>
                        <div class="flex items-center space-x-4 mt-2">
                            <span class="text-ocean-teal text-sm font-medium">
                                <i class="fas fa-map-marker-alt mr-1"></i>3.7 km away
                            </span>
                            <span class="text-gray-500 text-sm">6 years exp</span>
                        </div>
                        <div class="flex items-center space-x-2 mt-3">
                            <button class="bg-ocean-teal text-white px-4 py-2 rounded-lg text-sm font-medium flex-1">
                                <i class="fab fa-whatsapp mr-1"></i>Connect
                            </button>
                            <button class="bg-gray-100 text-gray-700 px-3 py-2 rounded-lg text-sm">
                                <i class="fas fa-info-circle"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Ice Breaker Suggestions Card -->
            <div class="bg-gradient-to-r from-light-teal to-ocean-teal/20 rounded-xl p-4 border border-ocean-teal/30">
                <div class="flex items-center space-x-3">
                    <i class="fas fa-lightbulb text-ocean-teal text-lg"></i>
                    <div>
                        <h4 class="font-bold text-gray-900">Ice Breaker Suggestion</h4>
                        <p class="text-sm text-gray-700 mt-1">"Fellow Chief Engineer nearby! Ask about MAN B&W engine experience?"</p>
                    </div>
                </div>
            </div>

            <!-- Group Networking Card -->
            <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-4">
                <div class="flex items-start space-x-4">
                    <!-- Maritime officers networking and meeting together -->
                    <img src="https://images.unsplash.com/photo-1521737604893-d14cc237f11d?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=300&h=150" alt="Maritime Officers Meeting" class="w-full h-24 object-cover rounded-lg">
                </div>
                <div class="mt-3">
                    <h4 class="font-bold text-gray-900 flex items-center">
                        <i class="fas fa-users text-ocean-teal mr-2"></i>Engineers Meetup Tonight
                    </h4>
                    <p class="text-sm text-gray-600 mt-1">5 Chief Engineers gathering at Marina Port Cafe - 8 PM</p>
                    <button class="mt-2 bg-maritime-navy text-white px-4 py-2 rounded-lg text-sm font-medium w-full">
                        Join Group Chat
                    </button>
                </div>
            </div>

            <!-- More Officers Loading Indicator -->
            <div class="text-center py-4">
                <button class="text-ocean-teal font-medium text-sm flex items-center justify-center space-x-2">
                    <i class="fas fa-chevron-down"></i>
                    <span>Load More Officers</span>
                </button>
            </div>

        </div>

        <!-- WhatsApp Chat Overlay (Hidden by default) -->
        <div class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden" id="chatOverlay">
            <div class="bg-white h-full flex flex-col">
                <!-- Chat Header -->
                <div class="bg-maritime-navy text-white p-4 flex items-center space-x-3">
                    <button onclick="closeChatOverlay()" class="text-white">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <div class="w-10 h-10 rounded-full bg-ocean-teal flex items-center justify-center">
                        <i class="fas fa-crown text-white text-sm"></i>
                    </div>
                    <div>
                        <h3 class="font-bold">Piyush Gupta</h3>
                        <p class="text-xs text-gray-300">Chief Engineer • Online</p>
                    </div>
                </div>
                
                <!-- Chat Messages -->
                <div class="flex-1 p-4 space-y-3 overflow-y-auto bg-gray-100">
                    <!-- Automatic Introduction Message -->
                    <div class="bg-blue-100 border border-blue-200 rounded-lg p-3">
                        <p class="text-sm text-blue-800">
                            <i class="fas fa-robot mr-1"></i>
                            <strong>Koi Maritime Bot:</strong> "Fellow Chief Engineer Piyush has 21+ years experience on MAN B&W engines. Great for technical discussions!"
                        </p>
                    </div>
                    
                    <!-- Suggested Messages -->
                    <div class="space-y-2">
                        <p class="text-xs text-gray-500 text-center">Suggested messages based on your ranks:</p>
                        <button class="w-full bg-white border border-gray-200 rounded-lg p-3 text-left text-sm hover:bg-gray-50">
                            "Hi Piyush! Fellow Chief Engineer here. How's your experience with MAN B&W 6G50ME engines?"
                        </button>
                        <button class="w-full bg-white border border-gray-200 rounded-lg p-3 text-left text-sm hover:bg-gray-50">
                            "Hello! I see you have extensive tanker experience. Any tips for a chief engineer on his first chemical tanker?"
                        </button>
                        <button class="w-full bg-white border border-gray-200 rounded-lg p-3 text-left text-sm hover:bg-gray-50">
                            "Hi there! Noticed we're both in the area. Interested in sharing some technical insights over coffee?"
                        </button>
                    </div>
                </div>
                
                <!-- Chat Input -->
                <div class="bg-white p-4 border-t border-gray-200">
                    <div class="flex items-center space-x-2">
                        <input type="text" placeholder="Type your message..." class="flex-1 border border-gray-300 rounded-full px-4 py-2 text-sm">
                        <button class="bg-ocean-teal text-white p-2 rounded-full">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <!-- Bottom Navigation -->
    <nav class="fixed bottom-0 left-1/2 transform -translate-x-1/2 w-full max-w-md bg-white border-t border-gray-200 px-4 py-2">
        <div class="flex justify-around items-center">
            <button class="flex flex-col items-center space-y-1 text-ocean-teal">
                <i class="fas fa-users text-lg"></i>
                <span class="text-xs font-medium">Discover</span>
            </button>
            <button class="flex flex-col items-center space-y-1 text-gray-400">
                <i class="fas fa-comments text-lg"></i>
                <span class="text-xs">Chats</span>
            </button>
            <button class="flex flex-col items-center space-y-1 text-gray-400">
                <i class="fas fa-folder text-lg"></i>
                <span class="text-xs">Documents</span>
            </button>
            <button class="flex flex-col items-center space-y-1 text-gray-400">
                <i class="fas fa-anchor text-lg"></i>
                <span class="text-xs">QAAQ</span>
            </button>
            <button class="flex flex-col items-center space-y-1 text-gray-400">
                <i class="fas fa-user text-lg"></i>
                <span class="text-xs">Profile</span>
            </button>
        </div>
    </nav>

    <!-- Floating Action Button -->
    <button class="fixed bottom-20 right-4 bg-ocean-teal text-white p-4 rounded-full shadow-lg hover:bg-teal-500 transition-colors z-40">
        <i class="fas fa-plus text-lg"></i>
    </button>

    <script>
        // TODO: Implement real-time officer discovery and filtering
        // TODO: Integrate with QAAQ API for rank verification
        // TODO: Implement WhatsApp Bot integration
        // TODO: Add geolocation services for distance calculation
        // TODO: Implement rank-based access control
        
        function closeChatOverlay() {
            document.getElementById('chatOverlay').classList.add('hidden');
        }
        
        // Mock interaction for demo purposes
        (function() {
            const connectButtons = document.querySelectorAll('button:contains("Connect")');
            // This would be replaced with actual WhatsApp API integration
        })();
    </script>
</body>
</html>