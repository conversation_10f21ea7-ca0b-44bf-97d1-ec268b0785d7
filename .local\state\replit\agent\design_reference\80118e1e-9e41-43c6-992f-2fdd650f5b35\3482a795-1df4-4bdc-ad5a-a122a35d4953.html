<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QaaqConnect - Maritime Community Hub</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'navy': '#1e3a8a',
                        'ocean': '#0891b2',
                        'coral': '#f97316',
                        'maritime-gray': '#64748b'
                    },
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif']
                    }
                }
            }
        };
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-slate-50 font-inter">
    <!-- Navigation Header -->
    <nav class="bg-navy shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-3">
                    <div class="flex items-center space-x-2">
                        <i class="fas fa-anchor text-ocean text-2xl"></i>
                        <span class="text-white text-xl font-bold">QaaqConnect</span>
                    </div>
                </div>
                <div class="hidden md:flex items-center space-x-6">
                    <a href="#" class="text-slate-200 hover:text-ocean transition-colors">Discover</a>
                    <a href="#" class="text-slate-200 hover:text-ocean transition-colors">Community</a>
                    <a href="#" class="text-slate-200 hover:text-ocean transition-colors">Shore Guide</a>
                    <button class="bg-ocean hover:bg-ocean/90 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-user-circle mr-2"></i>Profile
                    </button>
                </div>
                <button class="md:hidden text-white">
                    <i class="fas fa-bars text-xl"></i>
                </button>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="relative bg-gradient-to-br from-navy via-navy/90 to-ocean/80 text-white">
        <!-- Background Image with Overlay -->
        <!-- A panoramic view of sailors enjoying shore leave in a vibrant port city with restaurants and shops -->
        <div class="absolute inset-0 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&h=1080'); opacity: 0.3;"></div>
        
        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20">
            <div class="text-center">
                <h1 class="text-4xl md:text-6xl font-bold mb-6">
                    Your Maritime Community
                    <span class="text-ocean block">Shore & Beyond</span>
                </h1>
                <p class="text-xl md:text-2xl mb-8 text-slate-200">
                    Connect with fellow seafarers, discover shore experiences, and share your maritime journey
                </p>
                
                <!-- QAAQ AI Search Bar -->
                <div class="max-w-2xl mx-auto mb-8">
                    <div class="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20">
                        <h3 class="text-lg font-semibold mb-4 text-center">QAAQ AI Maritime Assistant</h3>
                        <div class="relative">
                            <input type="text" placeholder="Ask anything about ships, shore leave, or maritime life..." 
                                   class="w-full px-4 py-4 pr-32 rounded-xl bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-ocean">
                            <button class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-ocean hover:bg-ocean/90 text-white px-6 py-2 rounded-lg transition-colors">
                                <div class="text-center">
                                    <div class="text-xs font-bold">1234</div>
                                    <div class="text-sm">koi hai</div>
                                </div>
                            </button>
                        </div>
                        <div class="mt-3 text-sm text-slate-200 italic">
                            💬 "QAAQ hai na. That is a nice question..."
                        </div>
                    </div>
                </div>
                
                <div class="flex flex-wrap justify-center gap-4">
                    <button class="bg-coral hover:bg-coral/90 text-white px-8 py-3 rounded-xl font-semibold transition-colors">
                        <i class="fas fa-compass mr-2"></i>Explore Ports
                    </button>
                    <button class="bg-white/20 hover:bg-white/30 backdrop-blur text-white px-8 py-3 rounded-xl font-semibold transition-colors border border-white/30">
                        <i class="fas fa-users mr-2"></i>Join Community
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Main Content Area -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            
            <!-- Left Sidebar - Map Discovery -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-2xl shadow-lg overflow-hidden sticky top-24">
                    <div class="p-6">
                        <h3 class="text-xl font-bold text-navy mb-4 flex items-center">
                            <i class="fas fa-map-marker-alt mr-2 text-ocean"></i>
                            Discover Nearby
                        </h3>
                        
                        <!-- Map Container -->
                        <div class="relative bg-slate-100 rounded-xl h-64 mb-4">
                            <!-- Map placeholder with sailor pins -->
                            <!-- Interactive map showing sailor locations and points of interest around ports -->
                            <div class="absolute inset-0 bg-cover bg-center rounded-xl" style="background-image: url('https://pixabay.com/get/gb858d712251173a8a2091bde33aa25207a96ac7c3d4168e991de04a9823b7bccc36e2f59041dae18ccd5310addda176bb9c764404e08985a7610d7bb258664bc_1280.jpg');"></div>
                            
                            <!-- Map Controls -->
                            <div class="absolute top-4 right-4 flex flex-col space-y-2">
                                <button class="bg-white hover:bg-slate-50 text-gray-700 w-10 h-10 rounded-lg shadow-md flex items-center justify-center transition-colors">
                                    <i class="fas fa-plus"></i>
                                </button>
                                <button class="bg-white hover:bg-slate-50 text-gray-700 w-10 h-10 rounded-lg shadow-md flex items-center justify-center transition-colors">
                                    <i class="fas fa-minus"></i>
                                </button>
                            </div>
                            
                            <!-- Sample Sailor Pins -->
                            <div class="absolute top-1/3 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                                <div class="bg-ocean text-white w-8 h-8 rounded-full flex items-center justify-center text-sm shadow-lg animate-bounce">
                                    <i class="fas fa-anchor"></i>
                                </div>
                            </div>
                            <div class="absolute bottom-1/3 right-1/3">
                                <div class="bg-coral text-white w-8 h-8 rounded-full flex items-center justify-center text-sm shadow-lg">
                                    <i class="fas fa-users"></i>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Location Info -->
                        <div class="space-y-3">
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-maritime-gray">Current Location:</span>
                                <span class="font-semibold text-navy">Singapore Port</span>
                            </div>
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-maritime-gray">Radius:</span>
                                <span class="font-semibold text-navy">50km</span>
                            </div>
                            <div class="flex items-center justify-between text-sm">
                                <span class="text-maritime-gray">Online Sailors:</span>
                                <span class="font-semibold text-ocean">127</span>
                            </div>
                        </div>
                        
                        <!-- Quick Filters -->
                        <div class="mt-4 pt-4 border-t border-slate-200">
                            <h4 class="text-sm font-semibold text-navy mb-3">Quick Filters</h4>
                            <div class="flex flex-wrap gap-2">
                                <button class="bg-ocean/10 text-ocean px-3 py-1 rounded-full text-sm hover:bg-ocean/20 transition-colors">
                                    🍽️ Restaurants
                                </button>
                                <button class="bg-slate-100 text-maritime-gray px-3 py-1 rounded-full text-sm hover:bg-slate-200 transition-colors">
                                    🛍️ Shopping
                                </button>
                                <button class="bg-slate-100 text-maritime-gray px-3 py-1 rounded-full text-sm hover:bg-slate-200 transition-colors">
                                    🏛️ Culture
                                </button>
                                <button class="bg-slate-100 text-maritime-gray px-3 py-1 rounded-full text-sm hover:bg-slate-200 transition-colors">
                                    🌙 Nightlife
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Main Feed -->
            <div class="lg:col-span-2">
                <div class="space-y-6">
                    
                    <!-- Share Something -->
                    <div class="bg-white rounded-2xl shadow-lg p-6">
                        <h3 class="text-lg font-semibold text-navy mb-4">Share Your Experience</h3>
                        <div class="flex space-x-4">
                            <div class="flex-shrink-0">
                                <div class="w-12 h-12 bg-ocean rounded-full flex items-center justify-center text-white font-bold">
                                    PG
                                </div>
                            </div>
                            <div class="flex-1">
                                <textarea placeholder="Share your shore leave experience, recommend a place, or ask fellow sailors..." 
                                          class="w-full p-4 border border-slate-200 rounded-xl resize-none focus:outline-none focus:ring-2 focus:ring-ocean focus:border-transparent"
                                          rows="3"></textarea>
                                <div class="flex justify-between items-center mt-4">
                                    <div class="flex space-x-4">
                                        <button class="text-maritime-gray hover:text-ocean transition-colors">
                                            <i class="fas fa-camera text-lg"></i>
                                        </button>
                                        <button class="text-maritime-gray hover:text-ocean transition-colors">
                                            <i class="fas fa-map-marker-alt text-lg"></i>
                                        </button>
                                        <button class="text-maritime-gray hover:text-ocean transition-colors">
                                            <i class="fas fa-poll text-lg"></i>
                                        </button>
                                    </div>
                                    <button class="bg-ocean hover:bg-ocean/90 text-white px-6 py-2 rounded-lg font-semibold transition-colors">
                                        Share
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Feed Posts -->
                    
                    <!-- Post 1 -->
                    <article class="bg-white rounded-2xl shadow-lg overflow-hidden">
                        <div class="p-6">
                            <div class="flex items-start space-x-4 mb-4">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-coral rounded-full flex items-center justify-center text-white font-bold">
                                        MK
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2">
                                        <h4 class="font-semibold text-navy">Captain Malik</h4>
                                        <span class="text-maritime-gray text-sm">• Chief Engineer</span>
                                        <span class="text-maritime-gray text-sm">• 2 hours ago</span>
                                    </div>
                                    <p class="text-maritime-gray text-sm">📍 Singapore Port</p>
                                </div>
                            </div>
                            
                            <p class="text-gray-900 mb-4">
                                Just discovered this amazing curry place near Singapore port! 🍛 Perfect spice level and the owner speaks multiple languages. Great for international crew. Highly recommend after a long voyage!
                            </p>
                            
                            <!-- Amazing curry restaurant in Singapore's Little India with colorful dishes and warm ambiance -->
                            <img src="https://images.unsplash.com/photo-1565557623262-b51c2513a641?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600" 
                                 alt="Delicious curry dishes at Singapore restaurant" 
                                 class="w-full h-64 object-cover rounded-xl mb-4">
                        </div>
                        
                        <!-- Engagement Actions -->
                        <div class="px-6 py-4 bg-slate-50 border-t">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-6">
                                    <button class="flex items-center space-x-2 text-maritime-gray hover:text-coral transition-colors">
                                        <span class="text-xl">🦆</span>
                                        <span class="font-semibold">42</span>
                                    </button>
                                    <button class="flex items-center space-x-2 text-maritime-gray hover:text-ocean transition-colors">
                                        <i class="fas fa-comment"></i>
                                        <span class="font-semibold">5</span>
                                    </button>
                                    <button class="flex items-center space-x-2 text-maritime-gray hover:text-green-600 transition-colors" onclick="openShareModal('post1')">
                                        <i class="fas fa-share"></i>
                                        <span class="font-semibold">Share</span>
                                    </button>
                                </div>
                                <button class="text-maritime-gray hover:text-navy transition-colors">
                                    <i class="fas fa-bookmark"></i>
                                </button>
                            </div>
                        </div>
                    </article>
                    
                    <!-- Post 2 -->
                    <article class="bg-white rounded-2xl shadow-lg overflow-hidden">
                        <div class="p-6">
                            <div class="flex items-start space-x-4 mb-4">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center text-white font-bold">
                                        AS
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2">
                                        <h4 class="font-semibold text-navy">Alex Santos</h4>
                                        <span class="text-maritime-gray text-sm">• 2nd Engineer</span>
                                        <span class="text-maritime-gray text-sm">• 4 hours ago</span>
                                    </div>
                                    <p class="text-maritime-gray text-sm">📍 Rotterdam Port</p>
                                </div>
                            </div>
                            
                            <p class="text-gray-900 mb-4">
                                Rotterdam crew meet-up tomorrow at 6 PM! 🍻 Planning to explore the Markthal and grab some local beers. Anyone from MT Solar Claire or nearby vessels want to join? DM me!
                            </p>
                            
                            <!-- Maritime crew members socializing at a port bar with ships visible in background -->
                            <img src="https://images.unsplash.com/photo-1436076863939-06870fe779c2?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600" 
                                 alt="Port bar with maritime atmosphere" 
                                 class="w-full h-64 object-cover rounded-xl mb-4">
                        </div>
                        
                        <div class="px-6 py-4 bg-slate-50 border-t">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-6">
                                    <button class="flex items-center space-x-2 text-maritime-gray hover:text-coral transition-colors">
                                        <span class="text-xl">🦆</span>
                                        <span class="font-semibold">28</span>
                                    </button>
                                    <button class="flex items-center space-x-2 text-maritime-gray hover:text-ocean transition-colors">
                                        <i class="fas fa-comment"></i>
                                        <span class="font-semibold">12</span>
                                    </button>
                                    <button class="flex items-center space-x-2 text-maritime-gray hover:text-green-600 transition-colors" onclick="openShareModal('post2')">
                                        <i class="fas fa-share"></i>
                                        <span class="font-semibold">Share</span>
                                    </button>
                                </div>
                                <button class="text-maritime-gray hover:text-navy transition-colors">
                                    <i class="fas fa-bookmark"></i>
                                </button>
                            </div>
                        </div>
                    </article>
                    
                    <!-- Post 3 -->
                    <article class="bg-white rounded-2xl shadow-lg overflow-hidden">
                        <div class="p-6">
                            <div class="flex items-start space-x-4 mb-4">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center text-white font-bold">
                                        RK
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2">
                                        <h4 class="font-semibold text-navy">Rajesh Kumar</h4>
                                        <span class="text-maritime-gray text-sm">• 3rd Engineer</span>
                                        <span class="text-maritime-gray text-sm">• 6 hours ago</span>
                                    </div>
                                    <p class="text-maritime-gray text-sm">📍 Dubai Port</p>
                                </div>
                            </div>
                            
                            <p class="text-gray-900 mb-4">
                                Dubai Gold Souk is incredible! 💰 Got some great deals for the family back home. Pro tip: Always negotiate and compare prices between different shops. The shopkeepers are very friendly to seafarers.
                            </p>
                            
                            <!-- Bustling Dubai Gold Souk with traditional Middle Eastern architecture and glittering jewelry displays -->
                            <img src="https://images.unsplash.com/photo-1548919973-5cef591cdbc9?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600" 
                                 alt="Dubai Gold Souk marketplace" 
                                 class="w-full h-64 object-cover rounded-xl mb-4">
                        </div>
                        
                        <div class="px-6 py-4 bg-slate-50 border-t">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-6">
                                    <button class="flex items-center space-x-2 text-maritime-gray hover:text-coral transition-colors">
                                        <span class="text-xl">🦆</span>
                                        <span class="font-semibold">67</span>
                                    </button>
                                    <button class="flex items-center space-x-2 text-maritime-gray hover:text-ocean transition-colors">
                                        <i class="fas fa-comment"></i>
                                        <span class="font-semibold">18</span>
                                    </button>
                                    <button class="flex items-center space-x-2 text-maritime-gray hover:text-green-600 transition-colors" onclick="openShareModal('post3')">
                                        <i class="fas fa-share"></i>
                                        <span class="font-semibold">Share</span>
                                    </button>
                                </div>
                                <button class="text-maritime-gray hover:text-navy transition-colors">
                                    <i class="fas fa-bookmark"></i>
                                </button>
                            </div>
                        </div>
                    </article>
                    
                    <!-- Post 4 -->
                    <article class="bg-white rounded-2xl shadow-lg overflow-hidden">
                        <div class="p-6">
                            <div class="flex items-start space-x-4 mb-4">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-indigo-600 rounded-full flex items-center justify-center text-white font-bold">
                                        LM
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2">
                                        <h4 class="font-semibold text-navy">Luis Martinez</h4>
                                        <span class="text-maritime-gray text-sm">• Chief Officer</span>
                                        <span class="text-maritime-gray text-sm">• 8 hours ago</span>
                                    </div>
                                    <p class="text-maritime-gray text-sm">📍 Barcelona Port</p>
                                </div>
                            </div>
                            
                            <p class="text-gray-900 mb-4">
                                Barcelona's Gothic Quarter is perfect for a morning walk before the city gets busy! 🏛️ Amazing architecture and lots of small cafés for a quick coffee. Don't miss the Cathedral - it's free before 12:45 PM.
                            </p>
                            
                            <!-- Sailors exploring the historic Gothic Quarter of Barcelona with narrow medieval streets and cathedral -->
                            <img src="https://images.unsplash.com/photo-1539037116277-4db20889f2d4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600" 
                                 alt="Gothic Quarter Barcelona with historic architecture" 
                                 class="w-full h-64 object-cover rounded-xl mb-4">
                        </div>
                        
                        <div class="px-6 py-4 bg-slate-50 border-t">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-6">
                                    <button class="flex items-center space-x-2 text-maritime-gray hover:text-coral transition-colors">
                                        <span class="text-xl">🦆</span>
                                        <span class="font-semibold">35</span>
                                    </button>
                                    <button class="flex items-center space-x-2 text-maritime-gray hover:text-ocean transition-colors">
                                        <i class="fas fa-comment"></i>
                                        <span class="font-semibold">9</span>
                                    </button>
                                    <button class="flex items-center space-x-2 text-maritime-gray hover:text-green-600 transition-colors" onclick="openShareModal('post4')">
                                        <i class="fas fa-share"></i>
                                        <span class="font-semibold">Share</span>
                                    </button>
                                </div>
                                <button class="text-maritime-gray hover:text-navy transition-colors">
                                    <i class="fas fa-bookmark"></i>
                                </button>
                            </div>
                        </div>
                    </article>
                    
                    <!-- Load More -->
                    <div class="text-center py-8">
                        <button class="bg-ocean hover:bg-ocean/90 text-white px-8 py-3 rounded-xl font-semibold transition-colors">
                            <i class="fas fa-spinner mr-2"></i>Load More Stories
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Share Modal -->
    <div id="shareModal" class="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-2xl shadow-2xl max-w-md w-full">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-xl font-semibold text-navy">Share Post</h3>
                        <button onclick="closeShareModal()" class="text-maritime-gray hover:text-navy transition-colors">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                    
                    <div class="space-y-4">
                        <button class="w-full flex items-center space-x-4 p-4 rounded-xl hover:bg-green-50 transition-colors border border-green-200">
                            <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center text-white">
                                <i class="fab fa-whatsapp text-xl"></i>
                            </div>
                            <div class="text-left">
                                <div class="font-semibold text-gray-900">WhatsApp</div>
                                <div class="text-sm text-maritime-gray">Share with crew members</div>
                            </div>
                        </button>
                        
                        <button class="w-full flex items-center space-x-4 p-4 rounded-xl hover:bg-blue-50 transition-colors border border-blue-200">
                            <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white">
                                <i class="fas fa-link text-xl"></i>
                            </div>
                            <div class="text-left">
                                <div class="font-semibold text-gray-900">Copy Link</div>
                                <div class="text-sm text-maritime-gray">Share anywhere</div>
                            </div>
                        </button>
                        
                        <button class="w-full flex items-center space-x-4 p-4 rounded-xl hover:bg-blue-50 transition-colors border border-blue-200">
                            <div class="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center text-white">
                                <i class="fab fa-facebook text-xl"></i>
                            </div>
                            <div class="text-left">
                                <div class="font-semibold text-gray-900">Facebook</div>
                                <div class="text-sm text-maritime-gray">Share with friends</div>
                            </div>
                        </button>
                        
                        <button class="w-full flex items-center space-x-4 p-4 rounded-xl hover:bg-sky-50 transition-colors border border-sky-200">
                            <div class="w-12 h-12 bg-sky-500 rounded-full flex items-center justify-center text-white">
                                <i class="fab fa-twitter text-xl"></i>
                            </div>
                            <div class="text-left">
                                <div class="font-semibold text-gray-900">Twitter</div>
                                <div class="text-sm text-maritime-gray">Tweet to followers</div>
                            </div>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bottom Navigation (Mobile) -->
    <nav class="md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-slate-200 z-40">
        <div class="flex justify-around py-2">
            <button class="flex flex-col items-center py-2 px-3 text-ocean">
                <i class="fas fa-home text-xl"></i>
                <span class="text-xs mt-1">Home</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3 text-maritime-gray">
                <i class="fas fa-compass text-xl"></i>
                <span class="text-xs mt-1">Discover</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3 text-maritime-gray">
                <i class="fas fa-plus-circle text-2xl"></i>
                <span class="text-xs">Post</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3 text-maritime-gray">
                <i class="fas fa-bell text-xl"></i>
                <span class="text-xs mt-1">Alerts</span>
            </button>
            <button class="flex flex-col items-center py-2 px-3 text-maritime-gray">
                <i class="fas fa-user text-xl"></i>
                <span class="text-xs mt-1">Profile</span>
            </button>
        </div>
    </nav>

    <script>
        (() => {
            // Share Modal Functions
            window.openShareModal = function(postId) {
                document.getElementById('shareModal').classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            };

            window.closeShareModal = function() {
                document.getElementById('shareModal').classList.add('hidden');
                document.body.style.overflow = 'auto';
            };

            // Close modal when clicking outside
            document.getElementById('shareModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    window.closeShareModal();
                }
            });

            // Search functionality simulation
            document.querySelector('input[placeholder*="Ask anything"]').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    // TODO: Implement QAAQ AI search functionality
                    console.log('QAAQ AI search triggered:', this.value);
                }
            });

            // Map zoom controls
            document.querySelectorAll('.fa-plus, .fa-minus').forEach(button => {
                button.parentElement.addEventListener('click', function() {
                    // TODO: Implement map zoom functionality
                    console.log('Map zoom triggered:', button.classList.contains('fa-plus') ? 'in' : 'out');
                });
            });

            // Duck like button animation
            document.querySelectorAll('button:has(.🦆)').forEach(button => {
                button.addEventListener('click', function() {
                    // TODO: Implement like functionality with backend API
                    this.style.transform = 'scale(1.1)';
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 150);
                });
            });

            // Simulate QAAQ AI response
            setTimeout(() => {
                const aiResponse = document.querySelector('.text-slate-200.italic');
                if (aiResponse) {
                    aiResponse.textContent = '💬 "QAAQ hai na. Ready to help with any maritime questions!"';
                }
            }, 2000);
        })();
    </script>
</body>
</html>