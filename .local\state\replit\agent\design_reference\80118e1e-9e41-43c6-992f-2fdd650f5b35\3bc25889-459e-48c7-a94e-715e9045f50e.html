<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QaaqConnect - Maritime Professionals Shore Leave Network</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'navy': '#1B365D',
                        'ocean-teal': '#2DD4BF',
                        'light-teal': '#7DD3FC',
                        'maritime-blue': '#0F172A',
                        'anchor-gray': '#64748B'
                    },
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif']
                    }
                }
            }
        };
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-slate-50 font-inter">
    <!-- @COMPONENT: Header -->
    <header class="bg-navy shadow-lg relative overflow-hidden">
        <div class="absolute inset-0 bg-gradient-to-r from-navy to-blue-900 opacity-90"></div>
        <div class="relative z-10 max-w-7xl mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-ocean-teal rounded-full flex items-center justify-center">
                        <i class="fas fa-anchor text-white text-lg"></i>
                    </div>
                    <div>
                        <h1 class="text-white text-xl font-bold">QaaqConnect</h1>
                        <p class="text-ocean-teal text-xs">Maritime Shore Network</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="text-white hover:text-ocean-teal transition-colors">
                        <i class="fas fa-bell text-lg"></i>
                    </button>
                    <div class="w-8 h-8 bg-ocean-teal rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-white text-sm"></i>
                    </div>
                </div>
            </div>
        </div>
    </header>
    <!-- @END_COMPONENT: Header -->

    <!-- @COMPONENT: SearchSection -->
    <section class="bg-white shadow-sm border-b">
        <div class="max-w-7xl mx-auto px-4 py-6">
            <div class="relative">
                <div class="bg-slate-50 rounded-2xl p-4 border border-slate-200">
                    <div class="flex items-center space-x-3">
                        <div class="flex-1">
                            <input 
                                type="text" 
                                placeholder="Ask about your next shore leave..."
                                class="w-full bg-white rounded-xl px-4 py-3 border border-slate-300 focus:border-ocean-teal focus:outline-none focus:ring-2 focus:ring-ocean-teal/20 text-gray-700"
                                data-mock="true"
                            />
                        </div>
                        <button class="bg-ocean-teal hover:bg-teal-600 px-6 py-3 rounded-xl transition-colors relative">
                            <div class="text-white text-xs font-medium mb-1">1234</div>
                            <div class="text-white font-semibold">koi hai</div>
                        </button>
                    </div>
                    <div class="mt-3 text-sm text-anchor-gray">
                        <span class="font-medium text-navy">QAAQ AI:</span> Ready to help with shore leave recommendations, local spots, and crew connections
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- @END_COMPONENT: SearchSection -->

    <!-- @COMPONENT: MainContent -->
    <main class="max-w-7xl mx-auto px-4 py-6">
        <div class="grid lg:grid-cols-3 gap-6">
            <!-- @COMPONENT: MapSection -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-2xl shadow-sm overflow-hidden">
                    <div class="p-4 border-b border-slate-200 flex items-center justify-between">
                        <h2 class="text-lg font-semibold text-navy">Sailors Near You</h2>
                        <button class="text-anchor-gray hover:text-navy transition-colors" data-event="click:toggleFilters">
                            <i class="fas fa-filter"></i>
                        </button>
                    </div>
                    
                    <!-- Map Container -->
                    <div class="relative h-96 bg-gradient-to-br from-blue-100 to-teal-50">
                        <!-- Placeholder Map Background -->
                        <div class="absolute inset-0 bg-slate-200 rounded-lg m-4" style="background-image: radial-gradient(circle at 30% 40%, rgba(45, 212, 191, 0.1) 0%, transparent 50%), radial-gradient(circle at 70% 70%, rgba(27, 54, 93, 0.1) 0%, transparent 50%);">
                            
                            <!-- Map Controls -->
                            <div class="absolute top-4 right-4 flex flex-col space-y-2">
                                <button class="w-10 h-10 bg-white rounded-lg shadow-md flex items-center justify-center hover:bg-slate-50 transition-colors">
                                    <i class="fas fa-plus text-anchor-gray"></i>
                                </button>
                                <button class="w-10 h-10 bg-white rounded-lg shadow-md flex items-center justify-center hover:bg-slate-50 transition-colors">
                                    <i class="fas fa-minus text-anchor-gray"></i>
                                </button>
                            </div>

                            <!-- Sailor Pins -->
                            <div class="absolute top-1/4 left-1/3 transform -translate-x-1/2 -translate-y-1/2">
                                <div class="relative">
                                    <div class="w-4 h-4 bg-ocean-teal rounded-full border-2 border-white shadow-lg animate-pulse"></div>
                                    <div class="absolute -top-2 -left-2 w-8 h-8 bg-ocean-teal/20 rounded-full animate-ping"></div>
                                </div>
                            </div>
                            
                            <div class="absolute top-1/2 left-2/3 transform -translate-x-1/2 -translate-y-1/2">
                                <div class="relative">
                                    <div class="w-4 h-4 bg-navy rounded-full border-2 border-white shadow-lg"></div>
                                </div>
                            </div>
                            
                            <div class="absolute bottom-1/3 left-1/4 transform -translate-x-1/2 -translate-y-1/2">
                                <div class="relative">
                                    <div class="w-4 h-4 bg-teal-500 rounded-full border-2 border-white shadow-lg"></div>
                                </div>
                            </div>

                            <!-- Your Location -->
                            <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                                <div class="relative">
                                    <div class="w-6 h-6 bg-red-500 rounded-full border-3 border-white shadow-lg"></div>
                                    <div class="absolute -top-3 -left-3 w-12 h-12 bg-red-500/10 rounded-full"></div>
                                    <div class="absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-xs text-red-600 font-medium whitespace-nowrap">You</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Filter Dropdown (Initially Hidden) -->
                    <div class="hidden p-4 border-t border-slate-200 bg-slate-50" data-filter-panel="true">
                        <div class="space-y-3">
                            <h3 class="text-sm font-semibold text-navy">Filter Sailors</h3>
                            <div class="space-y-2">
                                <label class="flex items-center space-x-2">
                                    <input type="checkbox" class="rounded border-slate-300 text-ocean-teal focus:ring-ocean-teal" data-mock="true" />
                                    <span class="text-sm text-anchor-gray">Officers only</span>
                                </label>
                                <label class="flex items-center space-x-2">
                                    <input type="checkbox" class="rounded border-slate-300 text-ocean-teal focus:ring-ocean-teal" data-mock="true" />
                                    <span class="text-sm text-anchor-gray">Crew only</span>
                                </label>
                                <label class="flex items-center space-x-2">
                                    <input type="checkbox" class="rounded border-slate-300 text-ocean-teal focus:ring-ocean-teal" data-mock="true" />
                                    <span class="text-sm text-anchor-gray">Same nationality</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Conversations -->
                <div class="mt-6 bg-white rounded-2xl shadow-sm p-6">
                    <h3 class="text-lg font-semibold text-navy mb-4">Recent Shore Leave Conversations</h3>
                    <div class="space-y-4">
                        <div class="bg-slate-50 rounded-xl p-4">
                            <div class="flex items-start space-x-3">
                                <div class="w-10 h-10 bg-ocean-teal rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-white text-sm"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2 mb-1">
                                        <span class="font-medium text-navy" data-bind="user.name">Chief Engineer</span>
                                        <span class="text-xs text-anchor-gray">2 hours ago</span>
                                    </div>
                                    <p class="text-sm text-gray-700" data-mock="true">"Where to eat near Hamburg port?"</p>
                                    <div class="mt-2 text-xs text-ocean-teal font-medium">
                                        QAAQ hai na. That is a nice question. Near Hamburg port, try these great spots...
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- @END_COMPONENT: MapSection -->

            <!-- @COMPONENT: ActivityFeed -->
            <div class="space-y-6">
                <!-- Shore Leave Activities -->
                <div class="bg-white rounded-2xl shadow-sm overflow-hidden">
                    <div class="p-4 border-b border-slate-200">
                        <h3 class="text-lg font-semibold text-navy">Shore Leave Activities</h3>
                    </div>
                    <div class="p-4 space-y-4">
                        <!-- Activity 1 -->
                        <div class="border-l-4 border-ocean-teal pl-4">
                            <!-- Sailors enjoying shore leave activities in a bustling port city -->
                            <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&h=400" alt="Sailors enjoying shore leave activities" class="w-full h-32 object-cover rounded-lg mb-3" />
                            <div class="flex items-start space-x-3">
                                <div class="w-8 h-8 bg-navy rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-white text-xs"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2 mb-1">
                                        <span class="font-medium text-navy text-sm" data-bind="user.name">2nd Engineer</span>
                                        <span class="text-xs text-anchor-gray">Hamburg Port</span>
                                    </div>
                                    <p class="text-sm text-gray-700" data-mock="true">Found this amazing seafood restaurant near the port! Great prices and authentic local food. Anyone else been here?</p>
                                    <div class="flex items-center space-x-4 mt-2">
                                        <button class="text-xs text-anchor-gray hover:text-ocean-teal transition-colors">
                                            <i class="fas fa-heart mr-1"></i>12
                                        </button>
                                        <button class="text-xs text-anchor-gray hover:text-ocean-teal transition-colors">
                                            <i class="fas fa-comment mr-1"></i>5
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Activity 2 -->
                        <div class="border-l-4 border-light-teal pl-4">
                            <!-- Maritime crew members socializing at a waterfront cafe -->
                            <img src="https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&h=400" alt="Maritime crew socializing at waterfront cafe" class="w-full h-32 object-cover rounded-lg mb-3" />
                            <div class="flex items-start space-x-3">
                                <div class="w-8 h-8 bg-ocean-teal rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-white text-xs"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2 mb-1">
                                        <span class="font-medium text-navy text-sm" data-bind="user.name">Chief Officer</span>
                                        <span class="text-xs text-anchor-gray">Rotterdam</span>
                                    </div>
                                    <p class="text-sm text-gray-700" data-mock="true">Coffee meetup at the marina cafe tomorrow 3PM. All ranks welcome! Let's share some stories.</p>
                                    <div class="flex items-center space-x-4 mt-2">
                                        <button class="text-xs text-anchor-gray hover:text-ocean-teal transition-colors">
                                            <i class="fas fa-heart mr-1"></i>8
                                        </button>
                                        <button class="text-xs text-anchor-gray hover:text-ocean-teal transition-colors">
                                            <i class="fas fa-comment mr-1"></i>3
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Activity 3 -->
                        <div class="border-l-4 border-teal-400 pl-4">
                            <!-- Sailors exploring a historic port city with local architecture -->
                            <img src="https://images.unsplash.com/photo-1551698618-1dfe5d97d256?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&h=400" alt="Sailors exploring historic port city" class="w-full h-32 object-cover rounded-lg mb-3" />
                            <div class="flex items-start space-x-3">
                                <div class="w-8 h-8 bg-teal-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-user text-white text-xs"></i>
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2 mb-1">
                                        <span class="font-medium text-navy text-sm" data-bind="user.name">3rd Engineer</span>
                                        <span class="text-xs text-anchor-gray">Antwerp</span>
                                    </div>
                                    <p class="text-sm text-gray-700" data-mock="true">Historical city tour was incredible! The maritime museum here is a must-visit for anyone in the industry.</p>
                                    <div class="flex items-center space-x-4 mt-2">
                                        <button class="text-xs text-anchor-gray hover:text-ocean-teal transition-colors">
                                            <i class="fas fa-heart mr-1"></i>15
                                        </button>
                                        <button class="text-xs text-anchor-gray hover:text-ocean-teal transition-colors">
                                            <i class="fas fa-comment mr-1"></i>7
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Local Recommendations -->
                <div class="bg-white rounded-2xl shadow-sm overflow-hidden">
                    <div class="p-4 border-b border-slate-200">
                        <h3 class="text-lg font-semibold text-navy">Popular Port Experiences</h3>
                    </div>
                    <div class="space-y-4 p-4">
                        <!-- Port leisure experience in a vibrant harbor setting -->
                        <img src="https://pixabay.com/get/g34500dd66eb214ac2c5ad486b1009659d92d16f8b157b8d644d144dbbe339bb28511a221c30b154e1f5cd9f14957e88df97e030c239758269a3f22f4612e0b38_1280.jpg" alt="Port leisure experience in vibrant harbor" class="w-full h-24 object-cover rounded-lg" />
                        
                        <div class="space-y-3">
                            <div class="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                                <div>
                                    <h4 class="font-medium text-navy text-sm" data-mock="true">Harbor District Walking Tour</h4>
                                    <p class="text-xs text-anchor-gray" data-mock="true">Popular with Chief Engineers</p>
                                </div>
                                <div class="text-ocean-teal">
                                    <i class="fas fa-star text-xs"></i>
                                    <span class="text-xs font-medium" data-mock="true">4.8</span>
                                </div>
                            </div>
                            
                            <div class="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                                <div>
                                    <h4 class="font-medium text-navy text-sm" data-mock="true">Maritime Museum</h4>
                                    <p class="text-xs text-anchor-gray" data-mock="true">Educational & Inspiring</p>
                                </div>
                                <div class="text-ocean-teal">
                                    <i class="fas fa-star text-xs"></i>
                                    <span class="text-xs font-medium" data-mock="true">4.9</span>
                                </div>
                            </div>

                            <div class="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                                <div>
                                    <h4 class="font-medium text-navy text-sm" data-mock="true">Local Seafood Market</h4>
                                    <p class="text-xs text-anchor-gray" data-mock="true">Fresh & Affordable</p>
                                </div>
                                <div class="text-ocean-teal">
                                    <i class="fas fa-star text-xs"></i>
                                    <span class="text-xs font-medium" data-mock="true">4.7</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="bg-white rounded-2xl shadow-sm p-4">
                    <h3 class="text-lg font-semibold text-navy mb-4">Quick Actions</h3>
                    <div class="grid grid-cols-2 gap-3">
                        <button class="bg-ocean-teal hover:bg-teal-600 text-white p-3 rounded-xl transition-colors text-sm font-medium">
                            <i class="fas fa-camera-retro mb-2"></i>
                            <br>Share Photo
                        </button>
                        <button class="bg-navy hover:bg-blue-900 text-white p-3 rounded-xl transition-colors text-sm font-medium">
                            <i class="fas fa-map-marker-alt mb-2"></i>
                            <br>Check In
                        </button>
                        <button class="bg-light-teal hover:bg-cyan-400 text-white p-3 rounded-xl transition-colors text-sm font-medium">
                            <i class="fas fa-users mb-2"></i>
                            <br>Find Crew
                        </button>
                        <button class="bg-teal-500 hover:bg-teal-600 text-white p-3 rounded-xl transition-colors text-sm font-medium">
                            <i class="fas fa-question-circle mb-2"></i>
                            <br>Ask QAAQ
                        </button>
                    </div>
                </div>
            </div>
            <!-- @END_COMPONENT: ActivityFeed -->
        </div>
    </main>
    <!-- @END_COMPONENT: MainContent -->

    <!-- @COMPONENT: BottomNavigation -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-slate-200 px-4 py-2">
        <div class="flex items-center justify-around max-w-md mx-auto">
            <button class="flex flex-col items-center space-y-1 text-ocean-teal">
                <i class="fas fa-map text-lg"></i>
                <span class="text-xs font-medium">Discover</span>
            </button>
            <button class="flex flex-col items-center space-y-1 text-anchor-gray hover:text-navy transition-colors">
                <i class="fas fa-comments text-lg"></i>
                <span class="text-xs">Chat</span>
            </button>
            <button class="flex flex-col items-center space-y-1 text-anchor-gray hover:text-navy transition-colors">
                <i class="fas fa-calendar text-lg"></i>
                <span class="text-xs">Events</span>
            </button>
            <button class="flex flex-col items-center space-y-1 text-anchor-gray hover:text-navy transition-colors">
                <i class="fas fa-user text-lg"></i>
                <span class="text-xs">Profile</span>
            </button>
        </div>
    </nav>
    <!-- @END_COMPONENT: BottomNavigation -->

    <script>
        // TODO: Implement map functionality, real-time sailor tracking, and filter logic
        // TODO: Integrate with QAAQ API for AI responses and user data
        // TODO: Add geolocation services and distance calculations
        
        (function() {
            // Simple filter toggle functionality
            document.addEventListener('click', function(e) {
                if (e.target.closest('[data-event="click:toggleFilters"]') || e.target.closest('button[data-event="click:toggleFilters"]')) {
                    const filterPanel = document.querySelector('[data-filter-panel="true"]');
                    if (filterPanel) {
                        filterPanel.classList.toggle('hidden');
                    }
                }
            });

            // Mock search interaction
            const searchInput = document.querySelector('input[placeholder*="shore leave"]');
            if (searchInput) {
                searchInput.addEventListener('focus', function() {
                    console.log('QAAQ AI activated - ready to help with shore leave questions');
                });
            }

            // Add smooth animations to pins
            const pins = document.querySelectorAll('.animate-pulse');
            pins.forEach(pin => {
                setInterval(() => {
                    pin.style.opacity = pin.style.opacity === '0.5' ? '1' : '0.5';
                }, 2000);
            });
        })();
    </script>
</body>
</html>