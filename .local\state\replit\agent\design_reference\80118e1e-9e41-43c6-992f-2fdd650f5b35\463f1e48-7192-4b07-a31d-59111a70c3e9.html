<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QAAQ Connect - Maritime Professional Network</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Montserrat:wght@500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Inter', 'sans-serif'],
                        'maritime': ['Montserrat', 'sans-serif'],
                    },
                    colors: {
                        'navy': {
                            900: '#1B365D',
                            800: '#1E40AF',
                            700: '#1D4ED8',
                            600: '#2563EB',
                            500: '#3B82F6',
                        },
                        'ocean': {
                            600: '#0284C7',
                            500: '#0EA5E9',
                            400: '#38BDF8',
                            300: '#7DD3FC',
                        },
                        'maritime-gold': '#F59E0B',
                    }
                }
            }
        };
    </script>
</head>
<body class="bg-slate-50 font-sans">
    
    <!-- @COMPONENT: Header [navigation, branding] -->
    <header class="bg-navy-900 shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-ocean-500 rounded-lg flex items-center justify-center">
                        <i class="fas fa-anchor text-white text-lg"></i>
                    </div>
                    <div>
                        <h1 class="text-white font-maritime font-bold text-xl">QAAQ Connect</h1>
                        <p class="text-ocean-300 text-xs">Maritime Network</p>
                    </div>
                </div>
                <nav class="hidden md:flex space-x-6">
                    <a href="#" class="text-white hover:text-ocean-300 transition-colors font-medium">Web App</a>
                    <a href="#" class="text-white hover:text-ocean-300 transition-colors font-medium">WhatsApp Bot</a>
                    <a href="#" class="text-white hover:text-ocean-300 transition-colors font-medium">About</a>
                </nav>
                <button class="md:hidden text-white">
                    <i class="fas fa-bars text-xl"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- @END_COMPONENT: Header -->

    <!-- @COMPONENT: HeroSection [main landing area with dual access options] -->
    <section class="relative bg-gradient-to-br from-navy-900 via-navy-800 to-ocean-600 text-white py-20">
        <!-- Background maritime pattern overlay -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><path d=%22M10 50L30 30L50 50L70 30L90 50%22 stroke=%22white%22 stroke-width=%221%22 fill=%22none%22/></svg>'); background-size: 100px 100px;"></div>
        </div>
        
        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h1 class="text-4xl md:text-6xl font-maritime font-bold mb-6 leading-tight">
                    Connect with Maritime<br>
                    <span class="text-ocean-300">Professionals Nearby</span>
                </h1>
                <p class="text-xl md:text-2xl text-ocean-200 mb-8 max-w-3xl mx-auto">
                    Find fellow sailors, engineers, and maritime professionals in your area. Access via web or WhatsApp bot.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
                    <button class="bg-ocean-500 hover:bg-ocean-600 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all transform hover:scale-105 shadow-lg">
                        <i class="fas fa-globe mr-2"></i>
                        Launch Web App
                    </button>
                    <button class="bg-green-500 hover:bg-green-600 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-all transform hover:scale-105 shadow-lg">
                        <i class="fab fa-whatsapp mr-2"></i>
                        Chat on WhatsApp
                    </button>
                </div>
            </div>

            <!-- Dual Platform Preview -->
            <div class="grid md:grid-cols-2 gap-8 max-w-6xl mx-auto">
                <!-- Web App Preview -->
                <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
                    <h3 class="text-2xl font-maritime font-semibold mb-4 text-ocean-200">
                        <i class="fas fa-desktop mr-2"></i>
                        Web Application
                    </h3>
                    <div class="bg-white rounded-lg p-4 shadow-xl">
                        <div class="h-48 bg-gradient-to-br from-ocean-100 to-ocean-200 rounded-lg relative overflow-hidden">
                            <!-- Mock map interface -->
                            <div class="absolute inset-0 flex items-center justify-center">
                                <div class="text-navy-700 text-center">
                                    <i class="fas fa-map-marked-alt text-4xl mb-2"></i>
                                    <p class="font-semibold">Interactive Map</p>
                                    <p class="text-sm">Find nearby sailors</p>
                                </div>
                            </div>
                            <!-- Mock user pins -->
                            <div class="absolute top-4 left-8 w-6 h-6 bg-red-500 rounded-full border-2 border-white shadow animate-pulse"></div>
                            <div class="absolute bottom-8 right-12 w-6 h-6 bg-blue-500 rounded-full border-2 border-white shadow animate-pulse"></div>
                            <div class="absolute top-12 right-4 w-6 h-6 bg-green-500 rounded-full border-2 border-white shadow animate-pulse"></div>
                        </div>
                    </div>
                </div>

                <!-- WhatsApp Bot Preview -->
                <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
                    <h3 class="text-2xl font-maritime font-semibold mb-4 text-ocean-200">
                        <i class="fab fa-whatsapp mr-2"></i>
                        WhatsApp Bot
                    </h3>
                    <div class="bg-green-50 rounded-lg p-4 shadow-xl">
                        <!-- Mock WhatsApp interface -->
                        <div class="space-y-3">
                            <div class="bg-green-500 text-white p-3 rounded-2xl rounded-tl-md max-w-xs ml-auto">
                                <p class="text-sm">/nearby 5km</p>
                            </div>
                            <div class="bg-white p-3 rounded-2xl rounded-tr-md max-w-xs shadow-sm">
                                <p class="text-sm text-gray-800">🧭 Found 3 maritime professionals within 5km:</p>
                                <div class="mt-2 space-y-1 text-xs text-gray-600">
                                    <p>👨‍✈️ Chief Engineer - 2.1km</p>
                                    <p>⚓ Deck Officer - 3.7km</p>
                                    <p>🔧 Marine Engineer - 4.2km</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- @END_COMPONENT: HeroSection -->

    <!-- @COMPONENT: WebAppInterface [main application mockup] -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-maritime font-bold text-navy-900 mb-4">
                    Web Application Interface
                </h2>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                    Comprehensive maritime professional network with location-based connections and integrated QAAQ database access.
                </p>
            </div>

            <!-- Main App Interface -->
            <div class="bg-gray-50 rounded-3xl shadow-2xl overflow-hidden">
                <!-- App Header -->
                <div class="bg-navy-900 text-white p-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-ocean-500 rounded-lg flex items-center justify-center">
                                <i class="fas fa-anchor text-sm"></i>
                            </div>
                            <span class="font-maritime font-semibold">QAAQ Connect</span>
                        </div>
                        <div class="flex items-center space-x-4">
                            <button class="text-ocean-300 hover:text-white transition-colors">
                                <i class="fas fa-bell"></i>
                            </button>
                            <div class="w-8 h-8 bg-ocean-400 rounded-full flex items-center justify-center">
                                <span class="text-xs font-semibold" data-bind="user.initials">JD</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="grid lg:grid-cols-3 min-h-96">
                    <!-- Map Section -->
                    <div class="lg:col-span-2 relative">
                        <!-- Maritime professionals on cruise ships around mediterranean ports -->
                        <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1200&h=600" 
                             alt="Mediterranean maritime map view" 
                             class="w-full h-full object-cover" />
                        
                        <!-- Map overlay UI -->
                        <div class="absolute inset-0 bg-navy-900/20">
                            <!-- Search bar -->
                            <div class="absolute top-4 left-4 right-4">
                                <div class="bg-white rounded-xl shadow-lg p-3 flex items-center space-x-3">
                                    <i class="fas fa-search text-gray-400"></i>
                                    <input type="text" placeholder="Search location or maritime professional..." 
                                           class="flex-1 outline-none text-gray-700" />
                                    <button class="bg-ocean-500 text-white px-4 py-2 rounded-lg text-sm font-medium">
                                        Search
                                    </button>
                                </div>
                            </div>

                            <!-- Distance slider -->
                            <div class="absolute bottom-4 left-4">
                                <div class="bg-white rounded-xl shadow-lg p-4">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Search Radius</label>
                                    <div class="flex items-center space-x-3">
                                        <span class="text-sm text-gray-500">1km</span>
                                        <input type="range" min="1" max="50" value="10" 
                                               class="flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider" />
                                        <span class="text-sm font-medium text-navy-700" data-bind="distanceValue">10km</span>
                                    </div>
                                </div>
                            </div>

                            <!-- Mock user pins -->
                            <div class="absolute top-32 left-24 transform -translate-x-1/2 -translate-y-1/2">
                                <div class="bg-red-500 w-10 h-10 rounded-full border-4 border-white shadow-lg flex items-center justify-center cursor-pointer hover:scale-110 transition-transform">
                                    <i class="fas fa-user text-white text-xs"></i>
                                </div>
                                <div class="bg-white rounded-lg shadow-lg p-2 mt-2 min-w-max">
                                    <p class="text-xs font-semibold">Chief Engineer</p>
                                    <p class="text-xs text-gray-600">2.3km away</p>
                                </div>
                            </div>

                            <div class="absolute bottom-32 right-32 transform -translate-x-1/2 -translate-y-1/2">
                                <div class="bg-blue-500 w-10 h-10 rounded-full border-4 border-white shadow-lg flex items-center justify-center cursor-pointer hover:scale-110 transition-transform">
                                    <i class="fas fa-user text-white text-xs"></i>
                                </div>
                            </div>

                            <div class="absolute top-48 right-16 transform -translate-x-1/2 -translate-y-1/2">
                                <div class="bg-green-500 w-10 h-10 rounded-full border-4 border-white shadow-lg flex items-center justify-center cursor-pointer hover:scale-110 transition-transform">
                                    <i class="fas fa-user text-white text-xs"></i>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Nearby Professionals Panel -->
                    <div class="bg-white border-l border-gray-200 p-6">
                        <h3 class="text-lg font-maritime font-semibold text-navy-900 mb-4">
                            <i class="fas fa-users mr-2 text-ocean-500"></i>
                            Nearby Professionals
                        </h3>
                        
                        <!-- @MAP: nearbyProfessionals.map(professional => ( -->
                        <div class="space-y-4" data-mock="true">
                            <div class="bg-gray-50 rounded-xl p-4 hover:bg-gray-100 transition-colors cursor-pointer">
                                <div class="flex items-start space-x-3">
                                    <div class="w-12 h-12 bg-red-500 rounded-full flex items-center justify-center">
                                        <span class="text-white font-semibold text-sm">AK</span>
                                    </div>
                                    <div class="flex-1">
                                        <h4 class="font-semibold text-gray-900" data-bind="professional.name">Alexander Kim</h4>
                                        <p class="text-sm text-gray-600" data-bind="professional.title">Chief Engineer</p>
                                        <p class="text-sm text-ocean-600 font-medium" data-bind="professional.vessel">MV Ocean Pride</p>
                                        <div class="flex items-center mt-2 space-x-4">
                                            <span class="text-xs text-gray-500">
                                                <i class="fas fa-map-marker-alt mr-1"></i>
                                                <span data-bind="professional.distance">2.3km</span>
                                            </span>
                                            <div class="flex items-center">
                                                <div class="w-2 h-2 bg-green-400 rounded-full mr-1"></div>
                                                <span class="text-xs text-green-600">Available</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-3 flex space-x-2">
                                    <button class="bg-ocean-500 text-white px-3 py-1 rounded-lg text-xs font-medium hover:bg-ocean-600 transition-colors">
                                        <i class="fas fa-comments mr-1"></i>
                                        Connect
                                    </button>
                                    <button class="bg-gray-200 text-gray-700 px-3 py-1 rounded-lg text-xs font-medium hover:bg-gray-300 transition-colors">
                                        <i class="fas fa-eye mr-1"></i>
                                        Profile
                                    </button>
                                </div>
                            </div>

                            <div class="bg-gray-50 rounded-xl p-4 hover:bg-gray-100 transition-colors cursor-pointer">
                                <div class="flex items-start space-x-3">
                                    <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center">
                                        <span class="text-white font-semibold text-sm">MC</span>
                                    </div>
                                    <div class="flex-1">
                                        <h4 class="font-semibold text-gray-900" data-bind="professional.name">Maria Costa</h4>
                                        <p class="text-sm text-gray-600" data-bind="professional.title">Navigation Officer</p>
                                        <p class="text-sm text-ocean-600 font-medium" data-bind="professional.vessel">MS Mediterranean Star</p>
                                        <div class="flex items-center mt-2 space-x-4">
                                            <span class="text-xs text-gray-500">
                                                <i class="fas fa-map-marker-alt mr-1"></i>
                                                <span data-bind="professional.distance">3.7km</span>
                                            </span>
                                            <div class="flex items-center">
                                                <div class="w-2 h-2 bg-yellow-400 rounded-full mr-1"></div>
                                                <span class="text-xs text-yellow-600">Busy</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-3 flex space-x-2">
                                    <button class="bg-gray-300 text-gray-600 px-3 py-1 rounded-lg text-xs font-medium cursor-not-allowed">
                                        <i class="fas fa-comments mr-1"></i>
                                        Connect
                                    </button>
                                    <button class="bg-gray-200 text-gray-700 px-3 py-1 rounded-lg text-xs font-medium hover:bg-gray-300 transition-colors">
                                        <i class="fas fa-eye mr-1"></i>
                                        Profile
                                    </button>
                                </div>
                            </div>

                            <div class="bg-gray-50 rounded-xl p-4 hover:bg-gray-100 transition-colors cursor-pointer">
                                <div class="flex items-start space-x-3">
                                    <div class="w-12 h-12 bg-green-500 rounded-full flex items-center justify-center">
                                        <span class="text-white font-semibold text-sm">RP</span>
                                    </div>
                                    <div class="flex-1">
                                        <h4 class="font-semibold text-gray-900" data-bind="professional.name">Robert Phillips</h4>
                                        <p class="text-sm text-gray-600" data-bind="professional.title">Marine Engineer</p>
                                        <p class="text-sm text-ocean-600 font-medium" data-bind="professional.vessel">Container Vessel Atlas</p>
                                        <div class="flex items-center mt-2 space-x-4">
                                            <span class="text-xs text-gray-500">
                                                <i class="fas fa-map-marker-alt mr-1"></i>
                                                <span data-bind="professional.distance">4.2km</span>
                                            </span>
                                            <div class="flex items-center">
                                                <div class="w-2 h-2 bg-green-400 rounded-full mr-1"></div>
                                                <span class="text-xs text-green-600">Available</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-3 flex space-x-2">
                                    <button class="bg-ocean-500 text-white px-3 py-1 rounded-lg text-xs font-medium hover:bg-ocean-600 transition-colors">
                                        <i class="fas fa-comments mr-1"></i>
                                        Connect
                                    </button>
                                    <button class="bg-gray-200 text-gray-700 px-3 py-1 rounded-lg text-xs font-medium hover:bg-gray-300 transition-colors">
                                        <i class="fas fa-eye mr-1"></i>
                                        Profile
                                    </button>
                                </div>
                            </div>
                        </div>
                        <!-- @END_MAP )) -->

                        <button class="w-full mt-4 bg-navy-900 text-white py-3 rounded-xl font-medium hover:bg-navy-800 transition-colors">
                            <i class="fas fa-refresh mr-2"></i>
                            Refresh Search
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- @END_COMPONENT: WebAppInterface -->

    <!-- @COMPONENT: WhatsAppBotInterface [bot interaction mockup] -->
    <section class="py-16 bg-green-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-maritime font-bold text-navy-900 mb-4">
                    <i class="fab fa-whatsapp text-green-500 mr-3"></i>
                    WhatsApp Bot Interface
                </h2>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                    Access all features through WhatsApp commands. Perfect for quick connections while at sea or on duty.
                </p>
            </div>

            <!-- WhatsApp Interface Mockup -->
            <div class="max-w-4xl mx-auto">
                <div class="bg-white rounded-3xl shadow-2xl overflow-hidden">
                    <!-- WhatsApp Header -->
                    <div class="bg-green-600 text-white p-4">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-white rounded-full flex items-center justify-center">
                                <i class="fas fa-anchor text-green-600"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold">QAAQ Connect Bot</h3>
                                <p class="text-sm text-green-100">🟢 Online - Maritime Network Assistant</p>
                            </div>
                        </div>
                    </div>

                    <!-- Chat Messages -->
                    <div class="bg-green-50 h-96 overflow-y-auto p-4 space-y-4">
                        <!-- Welcome message -->
                        <div class="flex items-start space-x-2">
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-robot text-white text-xs"></i>
                            </div>
                            <div class="bg-white p-3 rounded-2xl rounded-tl-md shadow-sm max-w-xs">
                                <p class="text-sm text-gray-800">🧭 Welcome to QAAQ Connect!</p>
                                <p class="text-sm text-gray-600 mt-1">Find maritime professionals near you. Try these commands:</p>
                                <div class="mt-2 space-y-1 text-xs bg-gray-50 p-2 rounded">
                                    <p>📍 /nearby - Find professionals nearby</p>
                                    <p>🎯 /range 5km - Set search distance</p>
                                    <p>👤 /profile - View your profile</p>
                                    <p>💬 /connect [name] - Connect with someone</p>
                                </div>
                            </div>
                        </div>

                        <!-- User message -->
                        <div class="flex justify-end">
                            <div class="bg-green-500 text-white p-3 rounded-2xl rounded-tr-md max-w-xs">
                                <p class="text-sm">/nearby</p>
                            </div>
                        </div>

                        <!-- Bot response -->
                        <div class="flex items-start space-x-2">
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-robot text-white text-xs"></i>
                            </div>
                            <div class="bg-white p-3 rounded-2xl rounded-tl-md shadow-sm max-w-xs">
                                <p class="text-sm text-gray-800">🗺️ Searching for maritime professionals near your location...</p>
                            </div>
                        </div>

                        <!-- Location sharing request -->
                        <div class="flex items-start space-x-2">
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-robot text-white text-xs"></i>
                            </div>
                            <div class="bg-white p-3 rounded-2xl rounded-tl-md shadow-sm">
                                <p class="text-sm text-gray-800 mb-2">📍 Please share your location to find nearby professionals</p>
                                <button class="bg-blue-500 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-600 transition-colors">
                                    <i class="fas fa-map-marker-alt mr-1"></i>
                                    Share Location
                                </button>
                            </div>
                        </div>

                        <!-- Results -->
                        <div class="flex items-start space-x-2">
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-robot text-white text-xs"></i>
                            </div>
                            <div class="bg-white p-3 rounded-2xl rounded-tl-md shadow-sm max-w-sm">
                                <p class="text-sm text-gray-800 mb-2">🧭 Found 3 maritime professionals within 10km:</p>
                                <div class="space-y-2">
                                    <div class="bg-gray-50 p-2 rounded-lg">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <p class="text-sm font-medium">👨‍✈️ Alexander Kim</p>
                                                <p class="text-xs text-gray-600">Chief Engineer - 2.3km</p>
                                            </div>
                                            <button class="bg-ocean-500 text-white px-2 py-1 rounded text-xs">Connect</button>
                                        </div>
                                    </div>
                                    <div class="bg-gray-50 p-2 rounded-lg">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <p class="text-sm font-medium">⚓ Maria Costa</p>
                                                <p class="text-xs text-gray-600">Navigation Officer - 3.7km</p>
                                            </div>
                                            <button class="bg-gray-300 text-gray-600 px-2 py-1 rounded text-xs cursor-not-allowed">Busy</button>
                                        </div>
                                    </div>
                                    <div class="bg-gray-50 p-2 rounded-lg">
                                        <div class="flex items-center justify-between">
                                            <div>
                                                <p class="text-sm font-medium">🔧 Robert Phillips</p>
                                                <p class="text-xs text-gray-600">Marine Engineer - 4.2km</p>
                                            </div>
                                            <button class="bg-ocean-500 text-white px-2 py-1 rounded text-xs">Connect</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- User command -->
                        <div class="flex justify-end">
                            <div class="bg-green-500 text-white p-3 rounded-2xl rounded-tr-md max-w-xs">
                                <p class="text-sm">/connect Alexander Kim</p>
                            </div>
                        </div>

                        <!-- Connection response -->
                        <div class="flex items-start space-x-2">
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-robot text-white text-xs"></i>
                            </div>
                            <div class="bg-white p-3 rounded-2xl rounded-tl-md shadow-sm">
                                <p class="text-sm text-gray-800 mb-2">✅ Connection request sent to Alexander Kim!</p>
                                <p class="text-xs text-gray-600">They'll receive your contact info if they accept. You can also:</p>
                                <div class="mt-2 space-y-1">
                                    <button class="bg-navy-900 text-white px-3 py-1 rounded-lg text-xs mr-2">
                                        <i class="fas fa-question-circle mr-1"></i>
                                        Ask QAAQ Question
                                    </button>
                                    <button class="bg-ocean-500 text-white px-3 py-1 rounded-lg text-xs">
                                        <i class="fas fa-share mr-1"></i>
                                        Share Profile
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Message Input -->
                    <div class="bg-gray-100 p-4">
                        <div class="flex items-center space-x-3">
                            <input type="text" placeholder="Type a message or command..." 
                                   class="flex-1 p-3 rounded-full border border-gray-300 outline-none focus:border-green-500" />
                            <button class="bg-green-500 text-white p-3 rounded-full hover:bg-green-600 transition-colors">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                        <div class="mt-2 flex flex-wrap gap-2">
                            <button class="bg-white text-gray-700 px-3 py-1 rounded-full text-sm border hover:bg-gray-50 transition-colors">
                                /nearby
                            </button>
                            <button class="bg-white text-gray-700 px-3 py-1 rounded-full text-sm border hover:bg-gray-50 transition-colors">
                                /range 5km
                            </button>
                            <button class="bg-white text-gray-700 px-3 py-1 rounded-full text-sm border hover:bg-gray-50 transition-colors">
                                /profile
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- @END_COMPONENT: WhatsAppBotInterface -->

    <!-- @COMPONENT: FeatureComparison [dual platform features] -->
    <section class="py-16 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-maritime font-bold text-navy-900 mb-4">
                    Choose Your Platform
                </h2>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                    Access QAAQ Connect through web application or WhatsApp bot. Both platforms sync with your maritime professional profile.
                </p>
            </div>

            <div class="grid md:grid-cols-2 gap-8 max-w-6xl mx-auto">
                <!-- Web App Features -->
                <div class="bg-gradient-to-br from-navy-50 to-ocean-50 rounded-2xl p-8 border border-navy-200">
                    <div class="text-center mb-6">
                        <div class="w-16 h-16 bg-ocean-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-desktop text-white text-2xl"></i>
                        </div>
                        <h3 class="text-2xl font-maritime font-bold text-navy-900">Web Application</h3>
                        <p class="text-gray-600 mt-2">Full-featured desktop and mobile experience</p>
                    </div>
                    
                    <div class="space-y-4">
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <i class="fas fa-check text-white text-xs"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900">Interactive Map Interface</h4>
                                <p class="text-sm text-gray-600">Visual map with real-time professional locations and clustering</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <i class="fas fa-check text-white text-xs"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900">Advanced Search Filters</h4>
                                <p class="text-sm text-gray-600">Filter by rank, vessel type, experience, and specialization</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <i class="fas fa-check text-white text-xs"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900">Detailed Professional Profiles</h4>
                                <p class="text-sm text-gray-600">Complete maritime credentials, certifications, and experience</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <i class="fas fa-check text-white text-xs"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900">QAAQ Integration</h4>
                                <p class="text-sm text-gray-600">Direct access to technical Q&A database and maritime knowledge</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <i class="fas fa-check text-white text-xs"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900">Privacy Dashboard</h4>
                                <p class="text-sm text-gray-600">Granular control over location sharing and profile visibility</p>
                            </div>
                        </div>
                    </div>
                    
                    <button class="w-full mt-8 bg-ocean-500 hover:bg-ocean-600 text-white py-3 rounded-xl font-semibold transition-colors">
                        Launch Web App
                    </button>
                </div>

                <!-- WhatsApp Bot Features -->
                <div class="bg-gradient-to-br from-green-50 to-emerald-50 rounded-2xl p-8 border border-green-200">
                    <div class="text-center mb-6">
                        <div class="w-16 h-16 bg-green-500 rounded-2xl flex items-center justify-center mx-auto mb-4">
                            <i class="fab fa-whatsapp text-white text-2xl"></i>
                        </div>
                        <h3 class="text-2xl font-maritime font-bold text-navy-900">WhatsApp Bot</h3>
                        <p class="text-gray-600 mt-2">Quick access via familiar messaging interface</p>
                    </div>
                    
                    <div class="space-y-4">
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <i class="fas fa-check text-white text-xs"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900">Command-Based Interface</h4>
                                <p class="text-sm text-gray-600">Simple commands like /nearby, /range, /connect for quick actions</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <i class="fas fa-check text-white text-xs"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900">Native Location Sharing</h4>
                                <p class="text-sm text-gray-600">Use WhatsApp's built-in location features for seamless sharing</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <i class="fas fa-check text-white text-xs"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900">Instant Notifications</h4>
                                <p class="text-sm text-gray-600">Real-time alerts for nearby professionals and connection requests</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <i class="fas fa-check text-white text-xs"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900">Contact Facilitation</h4>
                                <p class="text-sm text-gray-600">Secure contact sharing and connection management</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start space-x-3">
                            <div class="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                                <i class="fas fa-check text-white text-xs"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold text-gray-900">Works Offline</h4>
                                <p class="text-sm text-gray-600">Queue commands when offline, sync when connection restored</p>
                            </div>
                        </div>
                    </div>
                    
                    <button class="w-full mt-8 bg-green-500 hover:bg-green-600 text-white py-3 rounded-xl font-semibold transition-colors">
                        <i class="fab fa-whatsapp mr-2"></i>
                        Start WhatsApp Bot
                    </button>
                </div>
            </div>
        </div>
    </section>
    <!-- @END_COMPONENT: FeatureComparison -->

    <!-- @COMPONENT: RegistrationProcess [maritime verification flow] -->
    <section class="py-16 bg-navy-900 text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-maritime font-bold mb-4">
                    Maritime Professional Verification
                </h2>
                <p class="text-lg text-ocean-200 max-w-2xl mx-auto">
                    Secure registration with maritime credential verification. Integration with QAAQ database ensures authentic professional connections.
                </p>
            </div>

            <!-- Registration Steps -->
            <div class="max-w-4xl mx-auto">
                <div class="grid md:grid-cols-3 gap-8">
                    <!-- Step 1 -->
                    <div class="text-center">
                        <div class="w-16 h-16 bg-ocean-500 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl font-bold">1</span>
                        </div>
                        <h3 class="text-xl font-maritime font-semibold mb-3">Choose Platform</h3>
                        <p class="text-ocean-200 text-sm">Select web app authentication via Replit Auth or WhatsApp registration via phone number</p>
                    </div>

                    <!-- Step 2 -->
                    <div class="text-center">
                        <div class="w-16 h-16 bg-ocean-500 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl font-bold">2</span>
                        </div>
                        <h3 class="text-xl font-maritime font-semibold mb-3">Maritime Credentials</h3>
                        <p class="text-ocean-200 text-sm">Verify maritime rank, certifications, vessel experience, and professional credentials</p>
                    </div>

                    <!-- Step 3 -->
                    <div class="text-center">
                        <div class="w-16 h-16 bg-ocean-500 rounded-full flex items-center justify-center mx-auto mb-4">
                            <span class="text-2xl font-bold">3</span>
                        </div>
                        <h3 class="text-xl font-maritime font-semibold mb-3">Profile & Privacy</h3>
                        <p class="text-ocean-200 text-sm">Set up professional profile, location sharing preferences, and safety controls</p>
                    </div>
                </div>

                <!-- Registration Form Preview -->
                <div class="mt-12 bg-white rounded-2xl p-8 text-gray-900">
                    <h3 class="text-2xl font-maritime font-bold text-navy-900 mb-6 text-center">
                        Professional Registration
                    </h3>
                    
                    <div class="grid md:grid-cols-2 gap-6">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                            <input type="text" placeholder="Enter your full name" 
                                   class="w-full p-3 border border-gray-300 rounded-lg focus:border-ocean-500 outline-none" />
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Maritime Rank</label>
                            <select class="w-full p-3 border border-gray-300 rounded-lg focus:border-ocean-500 outline-none">
                                <option>Select your rank</option>
                                <option>Chief Engineer</option>
                                <option>2nd Engineer</option>
                                <option>3rd Engineer</option>
                                <option>4th Engineer</option>
                                <option>Captain</option>
                                <option>Chief Officer</option>
                                <option>2nd Officer</option>
                                <option>3rd Officer</option>
                                <option>Bosun</option>
                                <option>AB Seaman</option>
                                <option>OS Seaman</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Current Vessel</label>
                            <input type="text" placeholder="Vessel name (optional)" 
                                   class="w-full p-3 border border-gray-300 rounded-lg focus:border-ocean-500 outline-none" />
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Years of Experience</label>
                            <select class="w-full p-3 border border-gray-300 rounded-lg focus:border-ocean-500 outline-none">
                                <option>Select experience</option>
                                <option>0-2 years</option>
                                <option>3-5 years</option>
                                <option>6-10 years</option>
                                <option>11-15 years</option>
                                <option>16-20 years</option>
                                <option>20+ years</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="mt-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Certifications</label>
                        <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                            <label class="flex items-center space-x-2 cursor-pointer">
                                <input type="checkbox" class="text-ocean-500 focus:ring-ocean-500" />
                                <span class="text-sm">STCW Basic Safety</span>
                            </label>
                            <label class="flex items-center space-x-2 cursor-pointer">
                                <input type="checkbox" class="text-ocean-500 focus:ring-ocean-500" />
                                <span class="text-sm">STCW Advanced</span>
                            </label>
                            <label class="flex items-center space-x-2 cursor-pointer">
                                <input type="checkbox" class="text-ocean-500 focus:ring-ocean-500" />
                                <span class="text-sm">COC Engine/Deck</span>
                            </label>
                            <label class="flex items-center space-x-2 cursor-pointer">
                                <input type="checkbox" class="text-ocean-500 focus:ring-ocean-500" />
                                <span class="text-sm">GMDSS Radio</span>
                            </label>
                            <label class="flex items-center space-x-2 cursor-pointer">
                                <input type="checkbox" class="text-ocean-500 focus:ring-ocean-500" />
                                <span class="text-sm">Medical First Aid</span>
                            </label>
                            <label class="flex items-center space-x-2 cursor-pointer">
                                <input type="checkbox" class="text-ocean-500 focus:ring-ocean-500" />
                                <span class="text-sm">Tanker Endorsement</span>
                            </label>
                        </div>
                    </div>
                    
                    <div class="mt-6">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Privacy Settings</label>
                        <div class="space-y-3">
                            <label class="flex items-center justify-between p-3 bg-gray-50 rounded-lg cursor-pointer">
                                <span class="text-sm">Share location with nearby professionals</span>
                                <input type="checkbox" checked class="text-ocean-500 focus:ring-ocean-500" />
                            </label>
                            <label class="flex items-center justify-between p-3 bg-gray-50 rounded-lg cursor-pointer">
                                <span class="text-sm">Allow connection requests</span>
                                <input type="checkbox" checked class="text-ocean-500 focus:ring-ocean-500" />
                            </label>
                            <label class="flex items-center justify-between p-3 bg-gray-50 rounded-lg cursor-pointer">
                                <span class="text-sm">Visible in QAAQ integration</span>
                                <input type="checkbox" checked class="text-ocean-500 focus:ring-ocean-500" />
                            </label>
                        </div>
                    </div>
                    
                    <div class="mt-8 flex flex-col sm:flex-row gap-4">
                        <button class="flex-1 bg-navy-900 text-white py-3 rounded-xl font-semibold hover:bg-navy-800 transition-colors">
                            <i class="fas fa-user-plus mr-2"></i>
                            Create Web Account
                        </button>
                        <button class="flex-1 bg-green-500 text-white py-3 rounded-xl font-semibold hover:bg-green-600 transition-colors">
                            <i class="fab fa-whatsapp mr-2"></i>
                            Register via WhatsApp
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- @END_COMPONENT: RegistrationProcess -->

    <!-- @COMPONENT: QAAQIntegration [showcase database integration] -->
    <section class="py-16 bg-gradient-to-br from-slate-50 to-ocean-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-12">
                <h2 class="text-3xl md:text-4xl font-maritime font-bold text-navy-900 mb-4">
                    Integrated with QAAQ Database
                </h2>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                    Connect with professionals and access the maritime industry's most comprehensive technical knowledge base.
                </p>
            </div>

            <div class="grid lg:grid-cols-2 gap-12 items-center">
                <!-- QAAQ Integration Features -->
                <div>
                    <h3 class="text-2xl font-maritime font-bold text-navy-900 mb-6">
                        Seamless Knowledge Integration
                    </h3>
                    
                    <div class="space-y-6">
                        <div class="flex items-start space-x-4">
                            <div class="w-12 h-12 bg-ocean-500 rounded-xl flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-database text-white"></i>
                            </div>
                            <div>
                                <h4 class="text-lg font-semibold text-gray-900 mb-2">Verified Professional Network</h4>
                                <p class="text-gray-600">All users verified through QAAQ's maritime professional database with authentic credentials and experience verification.</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start space-x-4">
                            <div class="w-12 h-12 bg-maritime-gold rounded-xl flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-question-circle text-white"></i>
                            </div>
                            <div>
                                <h4 class="text-lg font-semibold text-gray-900 mb-2">Technical Q&A Access</h4>
                                <p class="text-gray-600">Direct access to QAAQ's technical knowledge base with 2,847+ answered questions about marine machinery and systems.</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start space-x-4">
                            <div class="w-12 h-12 bg-green-500 rounded-xl flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-users text-white"></i>
                            </div>
                            <div>
                                <h4 class="text-lg font-semibold text-gray-900 mb-2">Expert Connections</h4>
                                <p class="text-gray-600">Connect with maritime engineers who have proven expertise in specific equipment and systems through their QAAQ contributions.</p>
                            </div>
                        </div>
                        
                        <div class="flex items-start space-x-4">
                            <div class="w-12 h-12 bg-navy-700 rounded-xl flex items-center justify-center flex-shrink-0">
                                <i class="fas fa-shield-alt text-white"></i>
                            </div>
                            <div>
                                <h4 class="text-lg font-semibold text-gray-900 mb-2">Rank-Based Trust System</h4>
                                <p class="text-gray-600">Professional connections based on verified maritime ranks and experience levels for relevant peer networking.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- QAAQ Interface Preview -->
                <div>
                    <div class="bg-white rounded-2xl shadow-2xl overflow-hidden">
                        <!-- QAAQ Header -->
                        <div class="bg-navy-900 text-white p-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-maritime-gold rounded-lg flex items-center justify-center">
                                    <i class="fas fa-anchor text-navy-900"></i>
                                </div>
                                <div>
                                    <h3 class="font-maritime font-bold text-lg">QAAQ Integration</h3>
                                    <p class="text-ocean-300 text-sm">Maritime Knowledge Access</p>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Questions -->
                        <div class="p-6">
                            <h4 class="font-semibold text-gray-900 mb-4">Recent Technical Questions</h4>
                            
                            <div class="space-y-4">
                                <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors cursor-pointer">
                                    <div class="flex items-start justify-between">
                                        <div class="flex-1">
                                            <h5 class="font-medium text-gray-900 text-sm">Mitsubishi 6UEC50LSE High Exhaust Temperature</h5>
                                            <p class="text-xs text-gray-600 mt-1">Cylinder 3 showing 420°C exhaust temp...</p>
                                            <div class="flex items-center mt-2 space-x-3">
                                                <span class="text-xs text-ocean-600 font-medium">Main Engine</span>
                                                <span class="text-xs text-gray-500">Asked by 3rd Engineer</span>
                                            </div>
                                        </div>
                                        <div class="text-xs text-gray-500 ml-4">2 days ago</div>
                                    </div>
                                </div>
                                
                                <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors cursor-pointer">
                                    <div class="flex items-start justify-between">
                                        <div class="flex-1">
                                            <h5 class="font-medium text-gray-900 text-sm">Alfa Laval MAPX 207 Purifier Vibration</h5>
                                            <p class="text-xs text-gray-600 mt-1">Purifier runs for 30 minutes then stops...</p>
                                            <div class="flex items-center mt-2 space-x-3">
                                                <span class="text-xs text-ocean-600 font-medium">Purifier</span>
                                                <span class="text-xs text-gray-500">Asked by 2nd Engineer</span>
                                            </div>
                                        </div>
                                        <div class="text-xs text-gray-500 ml-4">5 hours ago</div>
                                    </div>
                                </div>
                                
                                <div class="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 transition-colors cursor-pointer">
                                    <div class="flex items-start justify-between">
                                        <div class="flex-1">
                                            <h5 class="font-medium text-gray-900 text-sm">Sperre Air Compressor Unusual Noise</h5>
                                            <p class="text-xs text-gray-600 mt-1">SC-5 making knocking sound during compression...</p>
                                            <div class="flex items-center mt-2 space-x-3">
                                                <span class="text-xs text-ocean-600 font-medium">Air Compressor</span>
                                                <span class="text-xs text-gray-500">Asked by 4th Engineer</span>
                                            </div>
                                        </div>
                                        <div class="text-xs text-gray-500 ml-4">1 day ago</div>
                                    </div>
                                </div>
                            </div>
                            
                            <button class="w-full mt-4 bg-navy-900 text-white py-3 rounded-xl font-medium hover:bg-navy-800 transition-colors">
                                <i class="fas fa-external-link-alt mr-2"></i>
                                Access Full QAAQ Database
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- @END_COMPONENT: QAAQIntegration -->

    <!-- @COMPONENT: Footer [contact and links] -->
    <footer class="bg-navy-900 text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid md:grid-cols-4 gap-8">
                <!-- Brand -->
                <div>
                    <div class="flex items-center space-x-3 mb-4">
                        <div class="w-10 h-10 bg-ocean-500 rounded-lg flex items-center justify-center">
                            <i class="fas fa-anchor text-white"></i>
                        </div>
                        <div>
                            <h3 class="font-maritime font-bold text-xl">QAAQ Connect</h3>
                            <p class="text-ocean-300 text-sm">Maritime Network</p>
                        </div>
                    </div>
                    <p class="text-ocean-200 text-sm">
                        Connecting maritime professionals worldwide through web and WhatsApp platforms.
                    </p>
                </div>

                <!-- Platform Links -->
                <div>
                    <h4 class="font-maritime font-semibold text-lg mb-4">Platforms</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-ocean-200 hover:text-white transition-colors text-sm">Web Application</a></li>
                        <li><a href="#" class="text-ocean-200 hover:text-white transition-colors text-sm">WhatsApp Bot</a></li>
                        <li><a href="https://qaaqit.replit.app/" class="text-ocean-200 hover:text-white transition-colors text-sm">QAAQ Main Site</a></li>
                        <li><a href="#" class="text-ocean-200 hover:text-white transition-colors text-sm">Mobile App (Coming Soon)</a></li>
                    </ul>
                </div>

                <!-- Features -->
                <div>
                    <h4 class="font-maritime font-semibold text-lg mb-4">Features</h4>
                    <ul class="space-y-2">
                        <li><a href="#" class="text-ocean-200 hover:text-white transition-colors text-sm">Location Sharing</a></li>
                        <li><a href="#" class="text-ocean-200 hover:text-white transition-colors text-sm">Professional Verification</a></li>
                        <li><a href="#" class="text-ocean-200 hover:text-white transition-colors text-sm">Maritime Q&A Access</a></li>
                        <li><a href="#" class="text-ocean-200 hover:text-white transition-colors text-sm">Privacy Controls</a></li>
                    </ul>
                </div>

                <!-- Contact -->
                <div>
                    <h4 class="font-maritime font-semibold text-lg mb-4">Contact</h4>
                    <ul class="space-y-2">
                        <li class="text-ocean-200 text-sm">
                            <i class="fab fa-whatsapp mr-2"></i>
                            WhatsApp: +905363694997
                        </li>
                        <li class="text-ocean-200 text-sm">
                            <i class="fas fa-envelope mr-2"></i>
                            Available via QAAQ platform
                        </li>
                        <li class="text-ocean-200 text-sm">
                            <i class="fas fa-globe mr-2"></i>
                            Global Maritime Network
                        </li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-navy-800 mt-8 pt-8 text-center">
                <p class="text-ocean-200 text-sm">
                    © 2024 QAAQ Connect. Part of the QAAQ Maritime Engineering Knowledge Platform.
                </p>
                <p class="text-ocean-300 text-xs mt-2">
                    Connecting maritime professionals worldwide. WhatsApp Bot launching August 1, 2025.
                </p>
            </div>
        </div>
    </footer>
    <!-- @END_COMPONENT: Footer -->

    <script>
        (function() {
            // TODO: Implement actual geolocation API integration
            // TODO: Add real-time professional location updates
            // TODO: Implement WhatsApp Business API connection
            // TODO: Add PostgreSQL database integration for user verification
            // TODO: Implement Replit Auth for web platform
            
            // Mock interactivity for demonstration
            const distanceSlider = document.querySelector('input[type="range"]');
            const distanceValue = document.querySelector('[data-bind="distanceValue"]');
            
            if (distanceSlider && distanceValue) {
                distanceSlider.addEventListener('input', function() {
                    distanceValue.textContent = this.value + 'km';
                });
            }
            
            // Mock location pin interactions
            const pins = document.querySelectorAll('.absolute .bg-red-500, .absolute .bg-blue-500, .absolute .bg-green-500');
            pins.forEach(pin => {
                pin.addEventListener('click', function() {
                    // TODO: Show professional profile modal
                    console.log('Professional pin clicked - show profile modal');
                });
            });
        })();
    </script>

</body>
</html>