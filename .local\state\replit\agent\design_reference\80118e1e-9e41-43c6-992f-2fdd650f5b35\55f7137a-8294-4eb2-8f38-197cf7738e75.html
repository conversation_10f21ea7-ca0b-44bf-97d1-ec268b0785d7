<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QaaqConnect - Maritime Community Discovery</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'navy': '#1e3a8a',
                        'ocean-teal': '#0891b2',
                        'maritime-grey': '#6b7280'
                    },
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif']
                    }
                }
            }
        };
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #1e3a8a 0%, #0891b2 100%);
        }
        .glass-effect {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .maritime-shadow {
            box-shadow: 0 8px 32px rgba(30, 58, 138, 0.12);
        }
        .duck-like {
            color: #f59e0b;
            transition: all 0.2s ease;
        }
        .duck-like:hover {
            transform: scale(1.1);
            color: #d97706;
        }
    </style>
</head>
<body class="font-inter bg-slate-50 text-gray-900">

    <!-- @COMPONENT: LandingHeader -->
    <header class="gradient-bg text-white relative overflow-hidden">
        <!-- Maritime pattern overlay -->
        <div class="absolute inset-0 opacity-10">
            <div class="w-full h-full" style="background-image: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><path d=\"M20 50h60m-50-20h40m-30 40h20\" stroke=\"white\" stroke-width=\"1\" fill=\"none\"/></svg>'); background-size: 50px 50px;"></div>
        </div>
        
        <div class="container mx-auto px-4 py-6 relative z-10">
            <nav class="flex items-center justify-between mb-8">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                        <i class="fas fa-anchor text-xl text-white"></i>
                    </div>
                    <div>
                        <h1 class="text-2xl font-bold">QaaqConnect</h1>
                        <p class="text-sm text-white/80">Maritime Community</p>
                    </div>
                </div>
                <button class="bg-white/20 px-4 py-2 rounded-lg text-sm font-medium hover:bg-white/30 transition-colors">
                    <i class="fas fa-user mr-2"></i>Login
                </button>
            </nav>

            <div class="text-center py-12">
                <h2 class="text-4xl font-bold mb-4">Explore Ports.<br>Connect Locally.</h2>
                <p class="text-xl text-white/90 mb-8 max-w-2xl mx-auto">
                    Connect sailors with locals for authentic port city experiences. 
                    Discover hidden gems, join maritime meetups, and explore like a local.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
                    <button class="bg-white text-navy px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-100 transition-colors flex items-center justify-center">
                        <i class="fas fa-ship mr-3"></i>I'm a Sailor
                    </button>
                    <button class="bg-ocean-teal text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-cyan-600 transition-colors flex items-center justify-center">
                        <i class="fas fa-home mr-3"></i>I'm a Local
                    </button>
                </div>
            </div>
        </div>
    </header>
    <!-- @END_COMPONENT: LandingHeader -->

    <!-- @COMPONENT: FeaturesSection -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h3 class="text-3xl font-bold text-navy mb-4">Minimum Fuss, Maximum Fun</h3>
                <p class="text-lg text-gray-600 max-w-2xl mx-auto">
                    Quick registration, instant access, and seamless connections between maritime professionals and port city locals.
                </p>
            </div>

            <div class="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
                <div class="text-center group">
                    <div class="w-16 h-16 bg-navy/10 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:bg-navy/20 transition-colors">
                        <i class="fas fa-rocket text-2xl text-navy"></i>
                    </div>
                    <h4 class="text-xl font-semibold mb-3">Instant Access</h4>
                    <p class="text-gray-600">Just 3 fields: name, email, and role. You're in and exploring within seconds.</p>
                </div>

                <div class="text-center group">
                    <div class="w-16 h-16 bg-ocean-teal/10 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:bg-ocean-teal/20 transition-colors">
                        <i class="fas fa-map-marked-alt text-2xl text-ocean-teal"></i>
                    </div>
                    <h4 class="text-xl font-semibold mb-3">Local Discovery</h4>
                    <p class="text-gray-600">"1234 koi hai" search finds locals and experiences in any port city worldwide.</p>
                </div>

                <div class="text-center group">
                    <div class="w-16 h-16 bg-yellow-500/10 rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:bg-yellow-500/20 transition-colors">
                        <i class="fas fa-users text-2xl text-yellow-600"></i>
                    </div>
                    <h4 class="text-xl font-semibold mb-3">Maritime Community</h4>
                    <p class="text-gray-600">Connect with fellow seafarers and maritime professionals in ports worldwide.</p>
                </div>
            </div>
        </div>
    </section>
    <!-- @END_COMPONENT: FeaturesSection -->

    <!-- @COMPONENT: RegistrationFlow -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="max-w-md mx-auto">
                <div class="bg-white rounded-2xl maritime-shadow p-8">
                    <div class="text-center mb-8">
                        <div class="w-16 h-16 bg-gradient-to-br from-navy to-ocean-teal rounded-2xl flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-anchor text-2xl text-white"></i>
                        </div>
                        <h3 class="text-2xl font-bold text-gray-900 mb-2">Join QaaqConnect</h3>
                        <p class="text-gray-600">Super quick registration - get started in seconds</p>
                    </div>

                    <form class="space-y-6">
                        <!-- @STATE: registrationStep:number = 1 -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                            <input type="text" 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-ocean-teal focus:border-transparent outline-none transition-all"
                                   placeholder="Enter your full name"
                                   data-bind="user.fullName">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Email</label>
                            <input type="email" 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-ocean-teal focus:border-transparent outline-none transition-all"
                                   placeholder="<EMAIL>"
                                   data-bind="user.email">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-4">I'm a:</label>
                            <div class="grid grid-cols-2 gap-4">
                                <button type="button" 
                                        class="flex flex-col items-center p-4 border-2 border-gray-200 rounded-xl hover:border-navy hover:bg-navy/5 transition-all group"
                                        data-event="click:selectUserType"
                                        data-value="sailor">
                                    <i class="fas fa-ship text-2xl text-gray-400 group-hover:text-navy mb-2"></i>
                                    <span class="font-medium text-gray-700 group-hover:text-navy">Sailor 🚢</span>
                                </button>
                                <button type="button" 
                                        class="flex flex-col items-center p-4 border-2 border-gray-200 rounded-xl hover:border-ocean-teal hover:bg-ocean-teal/5 transition-all group"
                                        data-event="click:selectUserType"
                                        data-value="local">
                                    <i class="fas fa-home text-2xl text-gray-400 group-hover:text-ocean-teal mb-2"></i>
                                    <span class="font-medium text-gray-700 group-hover:text-ocean-teal">Local 🏠</span>
                                </button>
                            </div>
                        </div>

                        <button type="submit" 
                                class="w-full bg-gradient-to-r from-navy to-ocean-teal text-white py-4 rounded-xl font-semibold text-lg hover:shadow-lg transition-all transform hover:scale-[1.02]"
                                data-event="click:handleRegistration">
                            Let's Go! 🚀
                        </button>
                    </form>

                    <div class="mt-6 text-center">
                        <p class="text-sm text-gray-500">
                            Already have an account? 
                            <button class="text-ocean-teal font-medium hover:underline">Sign in</button>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- @END_COMPONENT: RegistrationFlow -->

    <!-- @COMPONENT: DiscoveryInterface -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">
                <div class="text-center mb-12">
                    <h3 class="text-3xl font-bold text-navy mb-4">Discover Port Cities</h3>
                    <p class="text-lg text-gray-600">Find experiences, connect with locals, and explore like never before</p>
                </div>

                <!-- Search Interface -->
                <div class="bg-gray-50 rounded-2xl p-6 mb-8">
                    <div class="flex flex-col md:flex-row gap-4">
                        <div class="flex-1">
                            <div class="relative">
                                <i class="fas fa-search absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                                <input type="text" 
                                       class="w-full pl-12 pr-4 py-4 bg-white border border-gray-200 rounded-xl focus:ring-2 focus:ring-ocean-teal focus:border-transparent outline-none"
                                       placeholder="Search port cities, activities, or locals..."
                                       data-bind="search.query">
                            </div>
                        </div>
                        <button class="bg-ocean-teal text-white px-8 py-4 rounded-xl font-semibold hover:bg-cyan-600 transition-colors">
                            <i class="fas fa-search mr-2"></i>Find
                        </button>
                    </div>
                    
                    <div class="flex flex-wrap gap-2 mt-4">
                        <span class="bg-white px-4 py-2 rounded-full text-sm font-medium text-gray-700 border hover:bg-gray-50 cursor-pointer">
                            🚢 Maritime Meetups
                        </span>
                        <span class="bg-white px-4 py-2 rounded-full text-sm font-medium text-gray-700 border hover:bg-gray-50 cursor-pointer">
                            🗺️ Local Tours
                        </span>
                        <span class="bg-white px-4 py-2 rounded-full text-sm font-medium text-gray-700 border hover:bg-gray-50 cursor-pointer">
                            🍽️ Port Dining
                        </span>
                        <span class="bg-white px-4 py-2 rounded-full text-sm font-medium text-gray-700 border hover:bg-gray-50 cursor-pointer">
                            🛍️ Shore Shopping
                        </span>
                    </div>
                </div>

                <!-- Discovery Cards -->
                <!-- @MAP: discoveryItems.map(item => ( -->
                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                    <!-- Sailors exploring port city -->
                    <div class="bg-white rounded-2xl maritime-shadow overflow-hidden hover:shadow-xl transition-shadow">
                        <div class="relative">
                            <!-- Two sailors walking through colorful European port town with historic buildings -->
                            <img src="https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=400&h=240" 
                                 alt="Sailors exploring European port town" 
                                 class="w-full h-48 object-cover">
                            <div class="absolute top-4 right-4 bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full text-sm font-medium text-navy">
                                <i class="fas fa-ship mr-1"></i>Sailor Experience
                            </div>
                        </div>
                        <div class="p-6">
                            <h4 class="text-lg font-semibold mb-2">Historic Port Walking Tour</h4>
                            <p class="text-gray-600 text-sm mb-4">Join fellow seafarers exploring the old maritime district with local historian Maria.</p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-4">
                                    <button class="duck-like hover:scale-110 transition-transform">
                                        <i class="fas fa-heart duck-like text-lg"></i>
                                        <span class="text-sm ml-1">12</span>
                                    </button>
                                    <span class="text-sm text-gray-500">
                                        <i class="fas fa-users mr-1"></i>6 going
                                    </span>
                                </div>
                                <span class="text-xs text-gray-400">2 hours ago</span>
                            </div>
                        </div>
                    </div>

                    <!-- Local discovery activity -->
                    <div class="bg-white rounded-2xl maritime-shadow overflow-hidden hover:shadow-xl transition-shadow">
                        <div class="relative">
                            <!-- Traditional seafood market with fresh catch display -->
                            <img src="https://images.unsplash.com/photo-1544551763-46a013bb70d5?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=400&h=240" 
                                 alt="Traditional seafood market" 
                                 class="w-full h-48 object-cover">
                            <div class="absolute top-4 right-4 bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full text-sm font-medium text-ocean-teal">
                                <i class="fas fa-home mr-1"></i>Local Guide
                            </div>
                        </div>
                        <div class="p-6">
                            <h4 class="text-lg font-semibold mb-2">Fresh Seafood Market Tour</h4>
                            <p class="text-gray-600 text-sm mb-4">Local fisherman Carlos shows you the best catches and cooking tips.</p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-4">
                                    <button class="duck-like hover:scale-110 transition-transform">
                                        <i class="fas fa-heart duck-like text-lg"></i>
                                        <span class="text-sm ml-1">8</span>
                                    </button>
                                    <span class="text-sm text-gray-500">
                                        <i class="fas fa-users mr-1"></i>4 going
                                    </span>
                                </div>
                                <span class="text-xs text-gray-400">4 hours ago</span>
                            </div>
                        </div>
                    </div>

                    <!-- Maritime community meetup -->
                    <div class="bg-white rounded-2xl maritime-shadow overflow-hidden hover:shadow-xl transition-shadow">
                        <div class="relative">
                            <!-- Group of maritime professionals meeting at waterfront cafe -->
                            <img src="https://images.unsplash.com/photo-1521791136064-7986c2920216?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=400&h=240" 
                                 alt="Maritime professionals meeting at waterfront" 
                                 class="w-full h-48 object-cover">
                            <div class="absolute top-4 right-4 bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full text-sm font-medium text-yellow-600">
                                <i class="fas fa-users mr-1"></i>Community
                            </div>
                        </div>
                        <div class="p-6">
                            <h4 class="text-lg font-semibold mb-2">Chief Engineers Meetup</h4>
                            <p class="text-gray-600 text-sm mb-4">Monthly gathering of maritime engineers sharing experiences and technical insights.</p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-4">
                                    <button class="duck-like hover:scale-110 transition-transform">
                                        <i class="fas fa-heart duck-like text-lg"></i>
                                        <span class="text-sm ml-1">15</span>
                                    </button>
                                    <span class="text-sm text-gray-500">
                                        <i class="fas fa-users mr-1"></i>12 going
                                    </span>
                                </div>
                                <span class="text-xs text-gray-400">1 day ago</span>
                            </div>
                        </div>
                    </div>

                    <!-- More discovery cards -->
                    <div class="bg-white rounded-2xl maritime-shadow overflow-hidden hover:shadow-xl transition-shadow">
                        <div class="relative">
                            <!-- Sailors at port adventure activity like kayaking -->
                            <img src="https://images.unsplash.com/photo-1544197150-b99a580bb7a8?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=400&h=240" 
                                 alt="Kayaking adventure in port waters" 
                                 class="w-full h-48 object-cover">
                            <div class="absolute top-4 right-4 bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full text-sm font-medium text-green-600">
                                <i class="fas fa-leaf mr-1"></i>Adventure
                            </div>
                        </div>
                        <div class="p-6">
                            <h4 class="text-lg font-semibold mb-2">Harbor Kayak Adventure</h4>
                            <p class="text-gray-600 text-sm mb-4">Explore the harbor from water level with experienced local guide and equipment.</p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-4">
                                    <button class="duck-like hover:scale-110 transition-transform">
                                        <i class="fas fa-heart duck-like text-lg"></i>
                                        <span class="text-sm ml-1">22</span>
                                    </button>
                                    <span class="text-sm text-gray-500">
                                        <i class="fas fa-users mr-1"></i>8 going
                                    </span>
                                </div>
                                <span class="text-xs text-gray-400">3 hours ago</span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-2xl maritime-shadow overflow-hidden hover:shadow-xl transition-shadow">
                        <div class="relative">
                            <!-- Cultural experience in port city with local architecture -->
                            <img src="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=400&h=240" 
                                 alt="Local cultural experience in port city" 
                                 class="w-full h-48 object-cover">
                            <div class="absolute top-4 right-4 bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full text-sm font-medium text-purple-600">
                                <i class="fas fa-palette mr-1"></i>Culture
                            </div>
                        </div>
                        <div class="p-6">
                            <h4 class="text-lg font-semibold mb-2">Maritime Museum & Art</h4>
                            <p class="text-gray-600 text-sm mb-4">Private tour of maritime artifacts and local artists' interpretations of sea life.</p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-4">
                                    <button class="duck-like hover:scale-110 transition-transform">
                                        <i class="fas fa-heart duck-like text-lg"></i>
                                        <span class="text-sm ml-1">18</span>
                                    </button>
                                    <span class="text-sm text-gray-500">
                                        <i class="fas fa-users mr-1"></i>5 going
                                    </span>
                                </div>
                                <span class="text-xs text-gray-400">6 hours ago</span>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-2xl maritime-shadow overflow-hidden hover:shadow-xl transition-shadow">
                        <div class="relative">
                            <!-- Port city nightlife and community gathering -->
                            <img src="https://pixabay.com/get/g4f78d32faecaebff7b78a5ca421104264e33afed10ee45727c375624d0c2204f09d46989e6689b91c56146c8a1c55c40996bf39017d8491e321d2463a2ceaa44_1280.jpg" 
                                 alt="Port city evening community gathering" 
                                 class="w-full h-48 object-cover">
                            <div class="absolute top-4 right-4 bg-white/90 backdrop-blur-sm px-3 py-1 rounded-full text-sm font-medium text-indigo-600">
                                <i class="fas fa-moon mr-1"></i>Evening
                            </div>
                        </div>
                        <div class="p-6">
                            <h4 class="text-lg font-semibold mb-2">Sunset Harbor Social</h4>
                            <p class="text-gray-600 text-sm mb-4">Evening gathering with live music, local food, and maritime stories from around the world.</p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-4">
                                    <button class="duck-like hover:scale-110 transition-transform">
                                        <i class="fas fa-heart duck-like text-lg"></i>
                                        <span class="text-sm ml-1">31</span>
                                    </button>
                                    <span class="text-sm text-gray-500">
                                        <i class="fas fa-users mr-1"></i>15 going
                                    </span>
                                </div>
                                <span class="text-xs text-gray-400">1 hour ago</span>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- @END_MAP )) -->
            </div>
        </div>
    </section>
    <!-- @END_COMPONENT: DiscoveryInterface -->

    <!-- @COMPONENT: PostingInterface -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="max-w-2xl mx-auto">
                <div class="bg-white rounded-2xl maritime-shadow p-8">
                    <div class="text-center mb-8">
                        <h3 class="text-2xl font-bold text-navy mb-2">Share an Experience</h3>
                        <p class="text-gray-600">Post about activities, meetups, or local recommendations</p>
                    </div>

                    <form class="space-y-6">
                        <!-- Identity Options -->
                        <div class="bg-gray-50 rounded-xl p-4">
                            <label class="block text-sm font-medium text-gray-700 mb-3">Post as:</label>
                            <div class="flex flex-wrap gap-3">
                                <button type="button" class="flex items-center px-4 py-2 bg-white border border-gray-200 rounded-lg hover:border-navy hover:bg-navy/5 transition-all">
                                    <i class="fas fa-user mr-2 text-gray-400"></i>
                                    <span data-bind="user.fullName">Piyush Gupta</span>
                                </button>
                                <button type="button" class="flex items-center px-4 py-2 bg-white border border-gray-200 rounded-lg hover:border-ocean-teal hover:bg-ocean-teal/5 transition-all">
                                    <i class="fas fa-anchor mr-2 text-gray-400"></i>
                                    <span>Chief Engineer PG</span>
                                </button>
                                <button type="button" class="flex items-center px-4 py-2 bg-white border border-gray-200 rounded-lg hover:border-gray-400 hover:bg-gray-50 transition-all">
                                    <i class="fas fa-mask mr-2 text-gray-400"></i>
                                    <span>Anonymous</span>
                                </button>
                            </div>
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">What's happening?</label>
                            <textarea class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-ocean-teal focus:border-transparent outline-none transition-all resize-none h-32"
                                      placeholder="Share an experience, ask for recommendations, or organize a meetup..."
                                      data-bind="post.content"></textarea>
                        </div>

                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Location</label>
                                <input type="text" 
                                       class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-ocean-teal focus:border-transparent outline-none transition-all"
                                       placeholder="Port city"
                                       data-bind="post.location">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Category</label>
                                <select class="w-full px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-ocean-teal focus:border-transparent outline-none transition-all"
                                        data-bind="post.category">
                                    <option>Local Discovery</option>
                                    <option>Maritime Meetup</option>
                                    <option>Port Experience</option>
                                    <option>Community Event</option>
                                    <option>Question/Help</option>
                                </select>
                            </div>
                        </div>

                        <div class="flex items-center space-x-4">
                            <button type="button" class="flex items-center px-4 py-2 bg-gray-50 border border-gray-200 rounded-lg hover:bg-gray-100 transition-colors">
                                <i class="fas fa-camera mr-2 text-gray-500"></i>
                                <span class="text-sm text-gray-600">Add Photo</span>
                            </button>
                            <button type="button" class="flex items-center px-4 py-2 bg-gray-50 border border-gray-200 rounded-lg hover:bg-gray-100 transition-colors">
                                <i class="fas fa-map-marker-alt mr-2 text-gray-500"></i>
                                <span class="text-sm text-gray-600">Pin Location</span>
                            </button>
                        </div>

                        <button type="submit" 
                                class="w-full bg-gradient-to-r from-navy to-ocean-teal text-white py-4 rounded-xl font-semibold text-lg hover:shadow-lg transition-all transform hover:scale-[1.02]"
                                data-event="click:handlePostSubmission">
                            Share with Community 🦆
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </section>
    <!-- @END_COMPONENT: PostingInterface -->

    <!-- @COMPONENT: BottomNavigation -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 maritime-shadow z-50">
        <div class="flex items-center justify-around py-2">
            <button class="flex flex-col items-center p-3 text-ocean-teal">
                <i class="fas fa-compass text-xl mb-1"></i>
                <span class="text-xs font-medium">Discover</span>
            </button>
            <button class="flex flex-col items-center p-3 text-gray-400 hover:text-navy transition-colors">
                <i class="fas fa-map text-xl mb-1"></i>
                <span class="text-xs font-medium">Map</span>
            </button>
            <button class="flex flex-col items-center p-3 text-gray-400 hover:text-navy transition-colors">
                <i class="fas fa-plus-circle text-xl mb-1"></i>
                <span class="text-xs font-medium">Post</span>
            </button>
            <button class="flex flex-col items-center p-3 text-gray-400 hover:text-navy transition-colors">
                <i class="fas fa-users text-xl mb-1"></i>
                <span class="text-xs font-medium">Community</span>
            </button>
            <button class="flex flex-col items-center p-3 text-gray-400 hover:text-navy transition-colors">
                <i class="fas fa-user text-xl mb-1"></i>
                <span class="text-xs font-medium">Profile</span>
            </button>
        </div>
    </nav>
    <!-- @END_COMPONENT: BottomNavigation -->

    <!-- @COMPONENT: Footer -->
    <footer class="bg-navy text-white py-12 pb-20">
        <div class="container mx-auto px-4">
            <div class="grid md:grid-cols-3 gap-8">
                <div>
                    <div class="flex items-center space-x-3 mb-4">
                        <div class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                            <i class="fas fa-anchor text-white"></i>
                        </div>
                        <div>
                            <h4 class="text-xl font-bold">QaaqConnect</h4>
                            <p class="text-sm text-white/80">Maritime Community</p>
                        </div>
                    </div>
                    <p class="text-white/80 text-sm">
                        Connecting sailors with locals for authentic port experiences worldwide. 
                        Part of the QAAQ maritime ecosystem.
                    </p>
                </div>

                <div>
                    <h5 class="font-semibold mb-4">Quick Links</h5>
                    <ul class="space-y-2 text-sm text-white/80">
                        <li><a href="#" class="hover:text-white transition-colors">QAAQ Main Platform</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Maritime Q&A</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Technical Support</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Community Guidelines</a></li>
                    </ul>
                </div>

                <div>
                    <h5 class="font-semibold mb-4">Connect</h5>
                    <div class="flex space-x-4">
                        <a href="#" class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center hover:bg-white/30 transition-colors">
                            <i class="fab fa-whatsapp"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center hover:bg-white/30 transition-colors">
                            <i class="fab fa-telegram"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center hover:bg-white/30 transition-colors">
                            <i class="fab fa-linkedin"></i>
                        </a>
                    </div>
                    <p class="text-xs text-white/60 mt-4">
                        WhatsApp: +905363694997<br>
                        Launch: August 1, 2025
                    </p>
                </div>
            </div>

            <div class="border-t border-white/20 mt-8 pt-8 text-center text-sm text-white/60">
                <p>&copy; 2025 QaaqConnect. Part of the QAAQ Maritime Platform.</p>
            </div>
        </div>
    </footer>
    <!-- @END_COMPONENT: Footer -->

    <script>
        // TODO: Implement registration flow with verification
        // TODO: Add real-time discovery updates
        // TODO: Implement posting functionality
        // TODO: Add location-based search
        // TODO: Connect with QAAQ parent platform API
        // TODO: Implement duck likes system
        // TODO: Add offline functionality for seafarers

        (function() {
            // Initialize app state
            let appState = {
                user: null,
                isRegistered: false,
                isVerified: false,
                currentLocation: null
            };

            // Handle registration form interactions
            document.addEventListener('DOMContentLoaded', function() {
                // Smooth scroll behavior for navigation
                document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                    anchor.addEventListener('click', function (e) {
                        e.preventDefault();
                        const target = document.querySelector(this.getAttribute('href'));
                        if (target) {
                            target.scrollIntoView({ behavior: 'smooth' });
                        }
                    });
                });

                // User type selection buttons
                document.querySelectorAll('[data-event="click:selectUserType"]').forEach(button => {
                    button.addEventListener('click', function() {
                        document.querySelectorAll('[data-event="click:selectUserType"]').forEach(btn => {
                            btn.classList.remove('border-navy', 'bg-navy/5', 'border-ocean-teal', 'bg-ocean-teal/5');
                            btn.classList.add('border-gray-200');
                        });
                        
                        const userType = this.getAttribute('data-value');
                        if (userType === 'sailor') {
                            this.classList.remove('border-gray-200');
                            this.classList.add('border-navy', 'bg-navy/5');
                        } else {
                            this.classList.remove('border-gray-200');
                            this.classList.add('border-ocean-teal', 'bg-ocean-teal/5');
                        }
                        
                        appState.userType = userType;
                    });
                });

                // Duck like buttons
                document.querySelectorAll('.duck-like').forEach(button => {
                    button.addEventListener('click', function() {
                        this.classList.add('scale-125');
                        setTimeout(() => {
                            this.classList.remove('scale-125');
                        }, 200);
                    });
                });
            });
        })();
    </script>
</body>
</html>