<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QaaqConnect - Explore Like a Local</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'navy': '#1B365D',
                        'ocean-teal': '#2E8B8B',
                        'light-teal': '#4FD1C7',
                        'warm-grey': '#6B7280'
                    },
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif']
                    }
                }
            }
        };
    </script>
</head>

<body class="font-inter bg-gray-50 text-gray-900">
    <!-- @COMPONENT: Header [mobile navigation with logo and menu] -->
    <header class="bg-white shadow-sm border-b border-gray-100 sticky top-0 z-50">
        <div class="flex items-center justify-between px-4 py-3">
            <div class="flex items-center space-x-2">
                <div class="w-8 h-8 bg-gradient-to-br from-navy to-ocean-teal rounded-lg flex items-center justify-center">
                    <i class="fas fa-anchor text-white text-sm"></i>
                </div>
                <span class="text-xl font-bold text-navy">QaaqConnect</span>
            </div>
            <div class="flex items-center space-x-3">
                <!-- @FUNCTIONALITY: Search functionality -->
                <button class="p-2 text-warm-grey hover:text-navy transition-colors">
                    <i class="fas fa-search text-lg"></i>
                </button>
                <!-- @FUNCTIONALITY: Profile menu -->
                <button class="p-2 text-warm-grey hover:text-navy transition-colors">
                    <i class="fas fa-user-circle text-lg"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- @END_COMPONENT: Header -->

    <!-- @COMPONENT: MainContent [discovery interface with map and community features] -->
    <main class="relative">
        <!-- Hero Discovery Section -->
        <section class="relative h-screen bg-gradient-to-b from-navy to-ocean-teal overflow-hidden">
            <!-- Map Container -->
            <div class="absolute inset-0 bg-gray-400" id="mapContainer">
                <!-- Map will be rendered here -->
                <div class="absolute inset-0 bg-gradient-to-b from-gray-400 to-gray-500 transition-all duration-1000" id="greyMap">
                    <!-- Grey map state -->
                    <div class="absolute inset-0 flex items-center justify-center">
                        <div class="text-center text-white p-6">
                            <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-map-marker-alt text-2xl"></i>
                            </div>
                            <h1 class="text-2xl font-bold mb-2">Explore like a local 🗺️</h1>
                            <p class="text-lg opacity-90 mb-6">Who's there?</p>
                            
                            <!-- Discovery Button -->
                            <button class="bg-light-teal hover:bg-opacity-90 text-navy font-semibold px-8 py-4 rounded-full text-lg transition-all transform hover:scale-105 shadow-lg" onclick="activateMap()">
                                1234 koi hai
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Active Map State (hidden initially) -->
                <div class="absolute inset-0 opacity-0 transition-opacity duration-1000" id="activeMap">
                    <!-- Map pins and interactive elements -->
                    <div class="absolute top-20 left-6 w-4 h-4 bg-red-500 rounded-full animate-pulse"></div>
                    <div class="absolute top-32 right-8 w-4 h-4 bg-blue-500 rounded-full animate-pulse"></div>
                    <div class="absolute bottom-40 left-12 w-4 h-4 bg-green-500 rounded-full animate-pulse"></div>
                    <div class="absolute bottom-56 right-16 w-4 h-4 bg-yellow-500 rounded-full animate-pulse"></div>
                </div>
            </div>

            <!-- Map Controls -->
            <div class="absolute top-24 right-4 flex flex-col bg-white rounded-lg shadow-lg overflow-hidden opacity-0" id="mapControls">
                <button class="p-3 border-b border-gray-200 hover:bg-gray-50 transition-colors">
                    <i class="fas fa-plus text-navy"></i>
                </button>
                <button class="p-3 hover:bg-gray-50 transition-colors">
                    <i class="fas fa-minus text-navy"></i>
                </button>
            </div>

            <!-- Filter Button -->
            <div class="absolute top-24 left-4 opacity-0" id="filterButton">
                <button class="bg-white rounded-lg shadow-lg px-4 py-3 flex items-center space-x-2 hover:shadow-xl transition-shadow">
                    <i class="fas fa-filter text-navy"></i>
                    <span class="text-navy font-medium">Filters</span>
                    <i class="fas fa-chevron-down text-navy text-sm"></i>
                </button>
            </div>
        </section>

        <!-- Quick Discovery Cards -->
        <section class="px-4 py-6 -mt-16 relative z-10">
            <div class="bg-white rounded-2xl shadow-xl p-6">
                <h2 class="text-xl font-bold text-navy mb-4">Discover Nearby</h2>
                <div class="grid grid-cols-2 gap-4">
                    <!-- @MAP: nearbyUsers.map(user => ( -->
                    <div class="bg-gray-50 rounded-xl p-4 text-center hover:bg-gray-100 transition-colors" data-mock="true">
                        <div class="w-12 h-12 bg-gradient-to-br from-ocean-teal to-light-teal rounded-full mx-auto mb-2 flex items-center justify-center">
                            <i class="fas fa-user text-white"></i>
                        </div>
                        <h3 class="font-semibold text-sm text-navy" data-bind="user.name">Captain Smith</h3>
                        <p class="text-xs text-warm-grey" data-bind="user.distance">0.2 km away</p>
                    </div>
                    <!-- @END_MAP )) -->
                    
                    <div class="bg-gray-50 rounded-xl p-4 text-center hover:bg-gray-100 transition-colors" data-mock="true">
                        <div class="w-12 h-12 bg-gradient-to-br from-navy to-ocean-teal rounded-full mx-auto mb-2 flex items-center justify-center">
                            <i class="fas fa-anchor text-white"></i>
                        </div>
                        <h3 class="font-semibold text-sm text-navy">Engineer Alex</h3>
                        <p class="text-xs text-warm-grey">0.5 km away</p>
                    </div>

                    <div class="bg-gray-50 rounded-xl p-4 text-center hover:bg-gray-100 transition-colors" data-mock="true">
                        <div class="w-12 h-12 bg-gradient-to-br from-light-teal to-ocean-teal rounded-full mx-auto mb-2 flex items-center justify-center">
                            <i class="fas fa-map text-white"></i>
                        </div>
                        <h3 class="font-semibold text-sm text-navy">Local Guide</h3>
                        <p class="text-xs text-warm-grey">0.8 km away</p>
                    </div>

                    <div class="bg-gray-50 rounded-xl p-4 text-center hover:bg-gray-100 transition-colors" data-mock="true">
                        <div class="w-12 h-12 bg-gradient-to-br from-ocean-teal to-navy rounded-full mx-auto mb-2 flex items-center justify-center">
                            <i class="fas fa-users text-white"></i>
                        </div>
                        <h3 class="font-semibold text-sm text-navy">Port Community</h3>
                        <p class="text-xs text-warm-grey">1.2 km away</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Local Experiences Section -->
        <section class="px-4 py-6">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-xl font-bold text-navy">Local Experiences</h2>
                <button class="text-ocean-teal font-medium text-sm">See all</button>
            </div>

            <div class="space-y-4">
                <!-- Experience Card 1 -->
                <div class="bg-white rounded-2xl shadow-sm overflow-hidden hover:shadow-md transition-shadow" data-mock="true">
                    <!-- A group of sailors exploring a bustling port market with local vendors -->
                    <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&h=200" alt="Sailors exploring port market" class="w-full h-32 object-cover" />
                    <div class="p-4">
                        <h3 class="font-semibold text-navy mb-1">Port Market Discovery</h3>
                        <p class="text-sm text-warm-grey mb-2">Explore local markets with fellow seafarers</p>
                        <div class="flex items-center justify-between">
                            <span class="text-xs text-ocean-teal font-medium">🦆 24 likes</span>
                            <button class="text-xs text-warm-grey hover:text-navy">Join group</button>
                        </div>
                    </div>
                </div>

                <!-- Experience Card 2 -->
                <div class="bg-white rounded-2xl shadow-sm overflow-hidden hover:shadow-md transition-shadow" data-mock="true">
                    <!-- Local food discovery tour with maritime professionals -->
                    <img src="https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&h=200" alt="Local food discovery" class="w-full h-32 object-cover" />
                    <div class="p-4">
                        <h3 class="font-semibold text-navy mb-1">Authentic Local Cuisine</h3>
                        <p class="text-sm text-warm-grey mb-2">Discover hidden local restaurants</p>
                        <div class="flex items-center justify-between">
                            <span class="text-xs text-ocean-teal font-medium">🦆 18 likes</span>
                            <button class="text-xs text-warm-grey hover:text-navy">Learn more</button>
                        </div>
                    </div>
                </div>

                <!-- Experience Card 3 -->
                <div class="bg-white rounded-2xl shadow-sm overflow-hidden hover:shadow-md transition-shadow" data-mock="true">
                    <!-- Maritime community gathering at a waterfront cafe -->
                    <img src="https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&h=200" alt="Maritime community meetup" class="w-full h-32 object-cover" />
                    <div class="p-4">
                        <h3 class="font-semibold text-navy mb-1">Waterfront Social</h3>
                        <p class="text-sm text-warm-grey mb-2">Meet local maritime professionals</p>
                        <div class="flex items-center justify-between">
                            <span class="text-xs text-ocean-teal font-medium">🦆 31 likes</span>
                            <button class="text-xs text-warm-grey hover:text-navy">Join event</button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- CPSS Navigation Section -->
        <section class="px-4 py-6 bg-white">
            <h2 class="text-xl font-bold text-navy mb-6">Explore Services</h2>
            
            <!-- CPSS Tree Navigation -->
            <div class="space-y-3">
                <!-- Country Level -->
                <div class="border border-gray-200 rounded-xl overflow-hidden">
                    <button class="w-full p-4 flex items-center justify-between hover:bg-gray-50 transition-colors" onclick="toggleCPSS('singapore')">
                        <div class="flex items-center space-x-3">
                            <span class="text-2xl">🇸🇬</span>
                            <span class="font-semibold text-navy">Singapore</span>
                        </div>
                        <i class="fas fa-chevron-down text-warm-grey transition-transform" id="singapore-chevron"></i>
                    </button>
                    
                    <!-- Port Level -->
                    <div class="hidden bg-gray-50" id="singapore-ports">
                        <div class="border-l-4 border-ocean-teal ml-8">
                            <button class="w-full p-4 flex items-center justify-between hover:bg-gray-100 transition-colors" onclick="toggleCPSS('marina-bay')">
                                <div class="flex items-center space-x-3">
                                    <i class="fas fa-anchor text-ocean-teal"></i>
                                    <span class="font-medium text-navy">Marina Bay</span>
                                </div>
                                <i class="fas fa-chevron-down text-warm-grey transition-transform" id="marina-bay-chevron"></i>
                            </button>
                            
                            <!-- Services Level -->
                            <div class="hidden bg-white" id="marina-bay-services">
                                <div class="border-l-4 border-light-teal ml-8 space-y-1">
                                    <!-- Service Items -->
                                    <div class="p-3 hover:bg-gray-50 transition-colors cursor-pointer" data-mock="true">
                                        <div class="flex items-center space-x-3">
                                            <i class="fas fa-utensils text-light-teal"></i>
                                            <span class="text-navy">Restaurants</span>
                                        </div>
                                    </div>
                                    
                                    <div class="p-3 hover:bg-gray-50 transition-colors cursor-pointer" data-mock="true">
                                        <div class="flex items-center space-x-3">
                                            <i class="fas fa-car text-light-teal"></i>
                                            <span class="text-navy">Transport</span>
                                        </div>
                                    </div>
                                    
                                    <div class="p-3 hover:bg-gray-50 transition-colors cursor-pointer" data-mock="true">
                                        <div class="flex items-center space-x-3">
                                            <i class="fas fa-shopping-bag text-light-teal"></i>
                                            <span class="text-navy">Shopping</span>
                                        </div>
                                    </div>
                                    
                                    <div class="p-3 hover:bg-gray-50 transition-colors cursor-pointer" data-mock="true">
                                        <div class="flex items-center space-x-3">
                                            <i class="fas fa-hospital text-light-teal"></i>
                                            <span class="text-navy">Medical</span>
                                        </div>
                                    </div>
                                    
                                    <!-- QAAQ Mart Service -->
                                    <div class="p-3 hover:bg-gray-50 transition-colors cursor-pointer border-l-4 border-yellow-400" data-mock="true">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center space-x-3">
                                                <i class="fas fa-shipping-fast text-yellow-500"></i>
                                                <span class="text-navy font-medium">QAAQ Mart</span>
                                            </div>
                                            <span class="text-xs bg-yellow-100 text-yellow-700 px-2 py-1 rounded-full">Ship Delivery</span>
                                        </div>
                                        <p class="text-xs text-warm-grey mt-1 ml-6">Pre-order essentials for ship delivery</p>
                                    </div>
                                    
                                    <div class="p-3 hover:bg-gray-50 transition-colors cursor-pointer" data-mock="true">
                                        <div class="flex items-center space-x-3">
                                            <i class="fas fa-exchange-alt text-light-teal"></i>
                                            <span class="text-navy">Currency Exchange</span>
                                        </div>
                                    </div>
                                    
                                    <div class="p-3 hover:bg-gray-50 transition-colors cursor-pointer" data-mock="true">
                                        <div class="flex items-center space-x-3">
                                            <i class="fas fa-sim-card text-light-teal"></i>
                                            <span class="text-navy">SIM Cards</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Additional Countries -->
                <div class="border border-gray-200 rounded-xl overflow-hidden" data-mock="true">
                    <button class="w-full p-4 flex items-center justify-between hover:bg-gray-50 transition-colors">
                        <div class="flex items-center space-x-3">
                            <span class="text-2xl">🇳🇱</span>
                            <span class="font-semibold text-navy">Netherlands</span>
                        </div>
                        <i class="fas fa-chevron-down text-warm-grey"></i>
                    </button>
                </div>

                <div class="border border-gray-200 rounded-xl overflow-hidden" data-mock="true">
                    <button class="w-full p-4 flex items-center justify-between hover:bg-gray-50 transition-colors">
                        <div class="flex items-center space-x-3">
                            <span class="text-2xl">🇺🇸</span>
                            <span class="font-semibold text-navy">United States</span>
                        </div>
                        <i class="fas fa-chevron-down text-warm-grey"></i>
                    </button>
                </div>
            </div>
        </section>

        <!-- Community Feed Section -->
        <section class="px-4 py-6">
            <h2 class="text-xl font-bold text-navy mb-6">Community Feed</h2>
            
            <div class="space-y-6">
                <!-- Community Post 1 -->
                <div class="bg-white rounded-2xl shadow-sm p-4" data-mock="true">
                    <div class="flex items-center space-x-3 mb-3">
                        <div class="w-10 h-10 bg-gradient-to-br from-navy to-ocean-teal rounded-full flex items-center justify-center">
                            <span class="text-white font-semibold text-sm">PG</span>
                        </div>
                        <div>
                            <h4 class="font-semibold text-navy text-sm">Chief Engineer Piyush</h4>
                            <p class="text-xs text-warm-grey">Marina Bay • 2h ago</p>
                        </div>
                    </div>
                    
                    <p class="text-navy mb-3">Anyone know good places for marine spare parts near Marina Bay? Need some urgent hydraulic seals.</p>
                    
                    <!-- Port city waterfront with ships and commercial buildings -->
                    <img src="https://pixabay.com/get/g271101a0b5b06d0e04f22f8c0cfafdfbfb971aed3f0ee8dec5a7f3c965770df212cd809cbb6895c69a97114602f287aee8e0a3827cb459eb660e38bfdeac020d_1280.jpg" alt="Port city waterfront" class="w-full h-32 object-cover rounded-xl mb-3" />
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <button class="flex items-center space-x-1 text-ocean-teal hover:text-navy transition-colors">
                                <span class="text-lg">🦆</span>
                                <span class="text-sm font-medium">12</span>
                            </button>
                            <button class="flex items-center space-x-1 text-warm-grey hover:text-navy transition-colors">
                                <i class="fas fa-comment text-sm"></i>
                                <span class="text-sm">5</span>
                            </button>
                        </div>
                        <button class="text-warm-grey hover:text-navy transition-colors">
                            <i class="fas fa-share text-sm"></i>
                        </button>
                    </div>
                </div>

                <!-- Community Post 2 -->
                <div class="bg-white rounded-2xl shadow-sm p-4" data-mock="true">
                    <div class="flex items-center space-x-3 mb-3">
                        <div class="w-10 h-10 bg-gradient-to-br from-light-teal to-ocean-teal rounded-full flex items-center justify-center">
                            <span class="text-white font-semibold text-sm">AL</span>
                        </div>
                        <div>
                            <h4 class="font-semibold text-navy text-sm">Local Guide Alex</h4>
                            <p class="text-xs text-warm-grey">Sentosa • 4h ago</p>
                        </div>
                    </div>
                    
                    <p class="text-navy mb-3">🍜 Found an amazing local hawker center that's popular with port workers. Great authentic food at sailor-friendly prices!</p>
                    
                    <!-- Bustling Asian street food market with seafarers dining -->
                    <img src="https://images.unsplash.com/photo-1555396273-367ea4eb4db5?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=600&h=200" alt="Local street food market" class="w-full h-32 object-cover rounded-xl mb-3" />
                    
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-4">
                            <button class="flex items-center space-x-1 text-ocean-teal hover:text-navy transition-colors">
                                <span class="text-lg">🦆</span>
                                <span class="text-sm font-medium">28</span>
                            </button>
                            <button class="flex items-center space-x-1 text-warm-grey hover:text-navy transition-colors">
                                <i class="fas fa-comment text-sm"></i>
                                <span class="text-sm">8</span>
                            </button>
                        </div>
                        <button class="text-warm-grey hover:text-navy transition-colors">
                            <i class="fas fa-share text-sm"></i>
                        </button>
                    </div>
                </div>

                <!-- AI Q&A Post -->
                <div class="bg-gradient-to-r from-navy to-ocean-teal rounded-2xl shadow-sm p-4 text-white" data-mock="true">
                    <div class="flex items-center space-x-3 mb-3">
                        <div class="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                            <i class="fas fa-robot text-lg"></i>
                        </div>
                        <div>
                            <h4 class="font-semibold text-sm">QAAQ AI Assistant</h4>
                            <p class="text-xs text-white text-opacity-80">Marina Bay • Local Q&A</p>
                        </div>
                    </div>
                    
                    <p class="mb-3">🤖 Q: "Best time to visit Marina Bay Sands observation deck?"</p>
                    <p class="text-sm text-white text-opacity-90">A: Early morning (8-10 AM) or late afternoon (4-6 PM) for best lighting and fewer crowds. Maritime professionals get 15% discount with valid ID.</p>
                    
                    <div class="flex items-center justify-between mt-3">
                        <button class="flex items-center space-x-1 text-light-teal hover:text-white transition-colors">
                            <span class="text-lg">🦆</span>
                            <span class="text-sm font-medium">15</span>
                        </button>
                        <span class="text-xs text-white text-opacity-70">AI-powered local insights</span>
                    </div>
                </div>
            </div>
        </section>

        <!-- Quick Actions FAB -->
        <div class="fixed bottom-6 right-6 z-40">
            <button class="w-14 h-14 bg-gradient-to-br from-ocean-teal to-light-teal rounded-full shadow-lg flex items-center justify-center text-white hover:shadow-xl transition-all transform hover:scale-105">
                <i class="fas fa-plus text-xl"></i>
            </button>
        </div>
    </main>
    <!-- @END_COMPONENT: MainContent -->

    <!-- @COMPONENT: BottomNavigation [mobile navigation tabs] -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50">
        <div class="flex items-center justify-around py-2">
            <button class="flex flex-col items-center py-2 px-4 text-ocean-teal">
                <i class="fas fa-compass text-lg mb-1"></i>
                <span class="text-xs font-medium">Discover</span>
            </button>
            
            <button class="flex flex-col items-center py-2 px-4 text-warm-grey hover:text-navy transition-colors">
                <i class="fas fa-map text-lg mb-1"></i>
                <span class="text-xs">Map</span>
            </button>
            
            <button class="flex flex-col items-center py-2 px-4 text-warm-grey hover:text-navy transition-colors">
                <i class="fas fa-users text-lg mb-1"></i>
                <span class="text-xs">Community</span>
            </button>
            
            <button class="flex flex-col items-center py-2 px-4 text-warm-grey hover:text-navy transition-colors">
                <i class="fas fa-shopping-cart text-lg mb-1"></i>
                <span class="text-xs">Mart</span>
            </button>
            
            <button class="flex flex-col items-center py-2 px-4 text-warm-grey hover:text-navy transition-colors">
                <i class="fas fa-user text-lg mb-1"></i>
                <span class="text-xs">Profile</span>
            </button>
        </div>
    </nav>
    <!-- @END_COMPONENT: BottomNavigation -->

    <script>
        // Map activation functionality
        function activateMap() {
            const greyMap = document.getElementById('greyMap');
            const activeMap = document.getElementById('activeMap');
            const mapControls = document.getElementById('mapControls');
            const filterButton = document.getElementById('filterButton');
            
            // Fade out grey map and fade in active map
            greyMap.style.opacity = '0';
            setTimeout(() => {
                activeMap.style.opacity = '1';
                mapControls.style.opacity = '1';
                filterButton.style.opacity = '1';
            }, 500);
        }

        // CPSS Tree Navigation
        function toggleCPSS(section) {
            const content = document.getElementById(section + '-ports') || document.getElementById(section + '-services');
            const chevron = document.getElementById(section + '-chevron');
            
            if (content.classList.contains('hidden')) {
                content.classList.remove('hidden');
                chevron.style.transform = 'rotate(180deg)';
            } else {
                content.classList.add('hidden');
                chevron.style.transform = 'rotate(0deg)';
            }
        }

        // TODO: Implement real-time map integration with user location
        // TODO: Implement user discovery and connection features
        // TODO: Implement QAAQ Mart ordering system
        // TODO: Implement community posting and interaction features
        // TODO: Implement AI-powered Q&A system
        // TODO: Implement WhatsApp/social sharing functionality
        // TODO: Implement filter system for user types and nationalities
    </script>
</body>
</html>