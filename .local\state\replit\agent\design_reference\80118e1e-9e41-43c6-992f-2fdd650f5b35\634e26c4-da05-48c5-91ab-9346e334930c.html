<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Koi Maritime - Professional Maritime Document Management</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'navy': '#1B365D',
                        'ocean-teal': '#2D7D7D',
                        'light-teal': '#4A9999',
                        'maritime-blue': '#3B82F6'
                    },
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif']
                    }
                }
            }
        };
    </script>
</head>
<body class="bg-gray-50 font-inter">
    <!-- @COMPONENT: MainLayout -->
    
    <!-- Navigation Header -->
    <!-- @COMPONENT: Header -->
    <header class="bg-navy text-white shadow-lg relative overflow-hidden">
        <div class="absolute inset-0 opacity-10">
            <!-- Maritime wave pattern background -->
            <svg viewBox="0 0 1200 120" fill="currentColor" class="w-full h-full">
                <path d="M0,40 Q300,80 600,40 T1200,40 L1200,120 L0,120 Z"></path>
            </svg>
        </div>
        <div class="container mx-auto px-4 py-4 relative z-10">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <i class="fas fa-anchor text-ocean-teal text-2xl"></i>
                        <span class="text-2xl font-bold">Koi Maritime</span>
                    </div>
                    <nav class="hidden md:flex space-x-6 ml-8">
                        <a href="#dashboard" class="hover:text-ocean-teal transition-colors">Dashboard</a>
                        <a href="#documents" class="hover:text-ocean-teal transition-colors">Documents</a>
                        <a href="#verification" class="hover:text-ocean-teal transition-colors">Verification</a>
                        <a href="#network" class="hover:text-ocean-teal transition-colors">Network</a>
                    </nav>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="bg-ocean-teal hover:bg-light-teal px-4 py-2 rounded-lg transition-colors">
                        <i class="fab fa-whatsapp mr-2"></i>WhatsApp Bot
                    </button>
                    <div class="w-10 h-10 bg-ocean-teal rounded-full flex items-center justify-center">
                        <i class="fas fa-user"></i>
                    </div>
                </div>
            </div>
        </div>
    </header>
    <!-- @END_COMPONENT: Header -->

    <div class="flex min-h-screen">
        <!-- Sidebar Navigation -->
        <!-- @COMPONENT: Sidebar -->
        <aside class="w-64 bg-white shadow-lg border-r border-gray-200">
            <div class="p-6">
                <div class="space-y-6">
                    <div>
                        <h3 class="text-sm font-semibold text-gray-500 uppercase tracking-wider mb-3">Document Management</h3>
                        <ul class="space-y-2">
                            <li><a href="#" class="flex items-center space-x-3 text-navy hover:bg-gray-50 p-2 rounded-lg">
                                <i class="fas fa-folder-open text-ocean-teal"></i>
                                <span>Marine Documents</span>
                            </a></li>
                            <li><a href="#" class="flex items-center space-x-3 text-gray-600 hover:bg-gray-50 p-2 rounded-lg">
                                <i class="fas fa-certificate"></i>
                                <span>CDC Certificates</span>
                            </a></li>
                            <li><a href="#" class="flex items-center space-x-3 text-gray-600 hover:bg-gray-50 p-2 rounded-lg">
                                <i class="fas fa-file-pdf"></i>
                                <span>Generated PDFs</span>
                            </a></li>
                        </ul>
                    </div>
                    
                    <div>
                        <h3 class="text-sm font-semibold text-gray-500 uppercase tracking-wider mb-3">Professional Network</h3>
                        <ul class="space-y-2">
                            <li><a href="#" class="flex items-center space-x-3 text-gray-600 hover:bg-gray-50 p-2 rounded-lg">
                                <i class="fas fa-users"></i>
                                <span>Who's There?</span>
                            </a></li>
                            <li><a href="#" class="flex items-center space-x-3 text-gray-600 hover:bg-gray-50 p-2 rounded-lg">
                                <i class="fas fa-ship"></i>
                                <span>Vessel Connections</span>
                            </a></li>
                            <li><a href="#" class="flex items-center space-x-3 text-gray-600 hover:bg-gray-50 p-2 rounded-lg">
                                <i class="fas fa-share-alt"></i>
                                <span>Share Profile</span>
                            </a></li>
                        </ul>
                    </div>

                    <div>
                        <h3 class="text-sm font-semibold text-gray-500 uppercase tracking-wider mb-3">QAAQ Integration</h3>
                        <ul class="space-y-2">
                            <li><a href="#" class="flex items-center space-x-3 text-gray-600 hover:bg-gray-50 p-2 rounded-lg">
                                <i class="fas fa-link"></i>
                                <span>Sync with QAAQ</span>
                            </a></li>
                            <li><a href="#" class="flex items-center space-x-3 text-gray-600 hover:bg-gray-50 p-2 rounded-lg">
                                <i class="fas fa-check-circle"></i>
                                <span>Verification Status</span>
                            </a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </aside>
        <!-- @END_COMPONENT: Sidebar -->

        <!-- Main Content Area -->
        <main class="flex-1 p-6">
            <!-- Dashboard Overview -->
            <!-- @COMPONENT: DashboardStats -->
            <div class="mb-8">
                <h1 class="text-3xl font-bold text-navy mb-6">Maritime Document Dashboard</h1>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">Total Documents</p>
                                <p class="text-3xl font-bold text-navy" data-bind="documents.count">24</p>
                            </div>
                            <div class="w-12 h-12 bg-ocean-teal bg-opacity-10 rounded-lg flex items-center justify-center">
                                <i class="fas fa-file-alt text-ocean-teal text-xl"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">CDC Certificates</p>
                                <p class="text-3xl font-bold text-navy" data-bind="cdc.count">8</p>
                            </div>
                            <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-certificate text-green-600 text-xl"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">Shared Links</p>
                                <p class="text-3xl font-bold text-navy" data-bind="shared.count">3</p>
                            </div>
                            <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-share-alt text-blue-600 text-xl"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="bg-white p-6 rounded-xl shadow-sm border border-gray-200">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-600">Vessel Connections</p>
                                <p class="text-3xl font-bold text-navy" data-bind="connections.count">12</p>
                            </div>
                            <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                <i class="fas fa-users text-purple-600 text-xl"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- @END_COMPONENT: DashboardStats -->

            <!-- Document Upload & Management Section -->
            <!-- @COMPONENT: DocumentManager -->
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h2 class="text-xl font-semibold text-navy mb-4 flex items-center">
                        <i class="fas fa-upload text-ocean-teal mr-3"></i>
                        Upload Documents
                    </h2>
                    
                    <!-- Drag & Drop Zone -->
                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-ocean-teal transition-colors cursor-pointer">
                        <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                        <p class="text-lg font-medium text-gray-700 mb-2">Drag & drop maritime documents</p>
                        <p class="text-sm text-gray-500 mb-4">CDC, STCW, COC, COE, Medical Certificates</p>
                        <button class="bg-ocean-teal text-white px-6 py-2 rounded-lg hover:bg-light-teal transition-colors">
                            Select Files
                        </button>
                    </div>
                    
                    <!-- Recent Uploads -->
                    <div class="mt-6">
                        <h3 class="font-medium text-gray-700 mb-3">Recent Uploads</h3>
                        <div class="space-y-2">
                            <!-- @MAP: recentUploads.map(upload => ( -->
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                                <div class="flex items-center space-x-3">
                                    <i class="fas fa-file-pdf text-red-500"></i>
                                    <span class="text-sm font-medium" data-bind="upload.name">CDC_Certificate_2024.pdf</span>
                                </div>
                                <span class="text-xs text-gray-500" data-bind="upload.status">Processing OCR...</span>
                            </div>
                            <!-- @END_MAP )) -->
                        </div>
                    </div>
                </div>

                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h2 class="text-xl font-semibold text-navy mb-4 flex items-center">
                        <i class="fas fa-folder-open text-ocean-teal mr-3"></i>
                        Marine Documents Folder
                    </h2>
                    
                    <!-- Document Categories -->
                    <div class="space-y-4">
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="font-medium text-gray-700 flex items-center">
                                    <i class="fas fa-certificate text-green-500 mr-2"></i>
                                    CDC Certificates
                                </h3>
                                <span class="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">3 files</span>
                            </div>
                            
                            <!-- CDC Document Preview -->
                            <div class="space-y-2">
                                <!-- A professional maritime CDC certificate document with official stamps and signatures -->
                                <img src="https://images.unsplash.com/photo-1454165804606-c3d57bc86b40?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300" alt="Maritime CDC Certificate" class="w-full h-32 object-cover rounded border">
                            </div>
                        </div>
                        
                        <div class="border border-gray-200 rounded-lg p-4">
                            <div class="flex items-center justify-between mb-3">
                                <h3 class="font-medium text-gray-700 flex items-center">
                                    <i class="fas fa-file-medical text-blue-500 mr-2"></i>
                                    Medical Certificates
                                </h3>
                                <span class="text-sm text-gray-500 bg-gray-100 px-2 py-1 rounded">2 files</span>
                            </div>
                            
                            <!-- Medical Certificate Preview -->
                            <div class="space-y-2">
                                <!-- Professional medical certificate for maritime workers with health verification -->
                                <img src="https://images.unsplash.com/photo-**********-5c350d0d3c56?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300" alt="Maritime Medical Certificate" class="w-full h-32 object-cover rounded border">
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-6 flex space-x-3">
                        <button class="flex-1 bg-navy text-white py-2 px-4 rounded-lg hover:bg-opacity-90 transition-colors">
                            <i class="fas fa-file-pdf mr-2"></i>Generate PDF
                        </button>
                        <button class="flex-1 bg-ocean-teal text-white py-2 px-4 rounded-lg hover:bg-light-teal transition-colors">
                            <i class="fas fa-share mr-2"></i>Share Link
                        </button>
                    </div>
                </div>
            </div>
            <!-- @END_COMPONENT: DocumentManager -->

            <!-- Professional Network Section -->
            <!-- @COMPONENT: NetworkSection -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-8">
                <h2 class="text-xl font-semibold text-navy mb-6 flex items-center">
                    <i class="fas fa-users text-ocean-teal mr-3"></i>
                    Who's There? - Maritime Professional Network
                </h2>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <!-- Current Location/Vessel -->
                    <div>
                        <h3 class="font-medium text-gray-700 mb-4">Current Location</h3>
                        <div class="bg-gray-50 rounded-lg p-4 mb-4">
                            <div class="flex items-center space-x-3 mb-3">
                                <i class="fas fa-map-marker-alt text-ocean-teal"></i>
                                <span class="font-medium" data-bind="user.location">Port of Hamburg</span>
                            </div>
                            <!-- Panoramic view of Hamburg port with ships and maritime infrastructure -->
                            <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=300" alt="Port of Hamburg with ships and maritime infrastructure" class="w-full h-40 object-cover rounded-lg mb-3">
                            <p class="text-sm text-gray-600" data-bind="user.vessel">Aboard MV Atlantic Pioneer</p>
                        </div>
                        
                        <button class="w-full bg-ocean-teal text-white py-3 px-4 rounded-lg hover:bg-light-teal transition-colors">
                            <i class="fas fa-search mr-2"></i>Discover Maritime Professionals Nearby
                        </button>
                    </div>
                    
                    <!-- Nearby Professionals -->
                    <div>
                        <h3 class="font-medium text-gray-700 mb-4">Nearby Maritime Professionals</h3>
                        <div class="space-y-3">
                            <!-- @MAP: nearbyProfessionals.map(professional => ( -->
                            <div class="flex items-center space-x-4 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                                <!-- Professional maritime worker in uniform on ship deck -->
                                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&h=100" alt="Maritime professional in uniform" class="w-12 h-12 rounded-full object-cover">
                                <div class="flex-1">
                                    <h4 class="font-medium text-gray-800" data-bind="professional.name">Captain Erik Hansen</h4>
                                    <p class="text-sm text-gray-600" data-bind="professional.position">Chief Engineer</p>
                                    <p class="text-xs text-ocean-teal" data-bind="professional.vessel">MV Nordic Star • 0.5km away</p>
                                </div>
                                <button class="text-ocean-teal hover:text-light-teal">
                                    <i class="fab fa-whatsapp text-xl"></i>
                                </button>
                            </div>
                            <!-- @END_MAP )) -->
                            
                            <div class="flex items-center space-x-4 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                                <!-- Professional female maritime officer checking navigation equipment -->
                                <img src="https://pixabay.com/get/gb06b8e5f0188c013b0bb9854e94ea952b672dc2dd92cb5a66a034120c1d40eee6847296b50b478c2830cfec2544ef3c7c8fd6ade2081450f5bf0b010a288e7ee_1280.jpg" alt="Female maritime officer with navigation equipment" class="w-12 h-12 rounded-full object-cover">
                                <div class="flex-1">
                                    <h4 class="font-medium text-gray-800">Navigator Sarah Chen</h4>
                                    <p class="text-sm text-gray-600">Navigation Officer</p>
                                    <p class="text-xs text-ocean-teal">MV Asia Express • 1.2km away</p>
                                </div>
                                <button class="text-ocean-teal hover:text-light-teal">
                                    <i class="fab fa-whatsapp text-xl"></i>
                                </button>
                            </div>
                            
                            <div class="flex items-center space-x-4 p-3 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer">
                                <!-- Maritime engineer working on ship engine room equipment -->
                                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&h=100" alt="Maritime engineer in engine room" class="w-12 h-12 rounded-full object-cover">
                                <div class="flex-1">
                                    <h4 class="font-medium text-gray-800">Engineer Miguel Santos</h4>
                                    <p class="text-sm text-gray-600">2nd Engineer</p>
                                    <p class="text-xs text-ocean-teal">MV Mediterranean • 2.1km away</p>
                                </div>
                                <button class="text-ocean-teal hover:text-light-teal">
                                    <i class="fab fa-whatsapp text-xl"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- @END_COMPONENT: NetworkSection -->

            <!-- WhatsApp Integration Panel -->
            <!-- @COMPONENT: WhatsAppInterface -->
            <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                <h2 class="text-xl font-semibold text-navy mb-6 flex items-center">
                    <i class="fab fa-whatsapp text-green-500 mr-3"></i>
                    WhatsApp Bot Integration
                </h2>
                
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    <div>
                        <h3 class="font-medium text-gray-700 mb-4">Quick Actions via WhatsApp</h3>
                        <div class="space-y-3">
                            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                                <h4 class="font-medium text-green-800 mb-2">Share Your Marine Documents</h4>
                                <p class="text-sm text-green-700 mb-3">Send "/share" to instantly generate a shareable link for your maritime credentials</p>
                                <button class="bg-green-500 text-white px-4 py-2 rounded text-sm hover:bg-green-600 transition-colors">
                                    <i class="fab fa-whatsapp mr-1"></i>Try Now
                                </button>
                            </div>
                            
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                <h4 class="font-medium text-blue-800 mb-2">Upload CDC via WhatsApp</h4>
                                <p class="text-sm text-blue-700 mb-3">Send your CDC photos directly to our bot for automatic OCR processing</p>
                                <button class="bg-blue-500 text-white px-4 py-2 rounded text-sm hover:bg-blue-600 transition-colors">
                                    <i class="fas fa-camera mr-1"></i>Upload CDC
                                </button>
                            </div>
                            
                            <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                                <h4 class="font-medium text-purple-800 mb-2">Find Nearby Mariners</h4>
                                <p class="text-sm text-purple-700 mb-3">Send "/nearby" to discover maritime professionals in your current location</p>
                                <button class="bg-purple-500 text-white px-4 py-2 rounded text-sm hover:bg-purple-600 transition-colors">
                                    <i class="fas fa-users mr-1"></i>Discover
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <h3 class="font-medium text-gray-700 mb-4">WhatsApp Chat Preview</h3>
                        <div class="bg-gray-100 rounded-lg p-4 h-80 overflow-y-auto">
                            <!-- WhatsApp-style chat interface -->
                            <div class="space-y-3">
                                <div class="text-center">
                                    <span class="bg-gray-200 text-gray-600 text-xs px-3 py-1 rounded-full">Today</span>
                                </div>
                                
                                <div class="flex justify-start">
                                    <div class="bg-white p-3 rounded-lg shadow-sm max-w-xs">
                                        <p class="text-sm">👋 Welcome to Koi Maritime Bot!</p>
                                        <p class="text-xs text-gray-500 mt-1">10:30 AM</p>
                                    </div>
                                </div>
                                
                                <div class="flex justify-end">
                                    <div class="bg-ocean-teal text-white p-3 rounded-lg shadow-sm max-w-xs">
                                        <p class="text-sm">/share</p>
                                        <p class="text-xs text-gray-200 mt-1">10:31 AM</p>
                                    </div>
                                </div>
                                
                                <div class="flex justify-start">
                                    <div class="bg-white p-3 rounded-lg shadow-sm max-w-xs">
                                        <p class="text-sm">📄 Here's your shareable maritime profile:</p>
                                        <p class="text-sm text-ocean-teal underline">koimaritime.com/profile/erik-hansen</p>
                                        <p class="text-xs text-gray-500 mt-1">10:31 AM</p>
                                    </div>
                                </div>
                                
                                <div class="flex justify-end">
                                    <div class="bg-ocean-teal text-white p-3 rounded-lg shadow-sm max-w-xs">
                                        <p class="text-sm">/nearby</p>
                                        <p class="text-xs text-gray-200 mt-1">10:35 AM</p>
                                    </div>
                                </div>
                                
                                <div class="flex justify-start">
                                    <div class="bg-white p-3 rounded-lg shadow-sm max-w-xs">
                                        <p class="text-sm">🚢 Found 3 maritime professionals within 2km:</p>
                                        <p class="text-sm">• Sarah Chen - Navigator (1.2km)</p>
                                        <p class="text-sm">• Miguel Santos - Engineer (2.1km)</p>
                                        <p class="text-xs text-gray-500 mt-1">10:35 AM</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-4 p-3 bg-gray-50 rounded-lg">
                            <p class="text-sm text-gray-600 text-center">
                                <i class="fab fa-whatsapp text-green-500 mr-1"></i>
                                Start chatting with our bot: <strong>+90 536 369 4997</strong>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            <!-- @END_COMPONENT: WhatsAppInterface -->
        </main>
    </div>

    <!-- Share Profile Modal (Hidden by default) -->
    <!-- @COMPONENT: ShareProfileModal -->
    <div id="shareModal" class="fixed inset-0 bg-black bg-opacity-50 hidden items-center justify-center z-50">
        <div class="bg-white rounded-xl shadow-2xl max-w-2xl w-full mx-4 max-h-90vh overflow-y-auto">
            <div class="p-6 border-b border-gray-200">
                <h2 class="text-2xl font-bold text-navy">Share Maritime Profile</h2>
                <p class="text-gray-600 mt-1">Generate a professional shareable link for your maritime credentials</p>
            </div>
            
            <div class="p-6">
                <!-- Profile Preview -->
                <div class="bg-gradient-to-br from-navy to-ocean-teal text-white rounded-xl p-6 mb-6">
                    <div class="flex items-center space-x-4 mb-4">
                        <!-- Professional maritime captain in uniform on ship bridge -->
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&h=100" alt="Professional maritime captain in uniform" class="w-16 h-16 rounded-full object-cover border-2 border-white">
                        <div>
                            <h3 class="text-xl font-bold" data-bind="user.name">Captain Erik Hansen</h3>
                            <p class="text-ocean-teal-100" data-bind="user.position">Chief Engineer • 15 years experience</p>
                            <p class="text-sm text-ocean-teal-200" data-bind="user.vessel">Currently aboard MV Atlantic Pioneer</p>
                        </div>
                    </div>
                    
                    <div class="grid grid-cols-3 gap-4 text-center">
                        <div>
                            <p class="text-2xl font-bold" data-bind="user.certificates">12</p>
                            <p class="text-xs text-ocean-teal-200">Certificates</p>
                        </div>
                        <div>
                            <p class="text-2xl font-bold" data-bind="user.vessels">8</p>
                            <p class="text-xs text-ocean-teal-200">Vessels</p>
                        </div>
                        <div>
                            <p class="text-2xl font-bold" data-bind="user.ports">45</p>
                            <p class="text-xs text-ocean-teal-200">Ports</p>
                        </div>
                    </div>
                </div>
                
                <!-- Shareable Link -->
                <div class="bg-gray-50 rounded-lg p-4 mb-4">
                    <label class="block text-sm font-medium text-gray-700 mb-2">Shareable Profile Link</label>
                    <div class="flex space-x-2">
                        <input type="text" value="https://koimaritime.com/profile/erik-hansen-c4f7x9" readonly 
                               class="flex-1 px-3 py-2 border border-gray-300 rounded-lg bg-white text-sm">
                        <button class="bg-ocean-teal text-white px-4 py-2 rounded-lg hover:bg-light-teal transition-colors text-sm">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                </div>
                
                <!-- Privacy Settings -->
                <div class="mb-6">
                    <h4 class="font-medium text-gray-700 mb-3">Privacy Settings</h4>
                    <div class="space-y-3">
                        <label class="flex items-center">
                            <input type="checkbox" checked class="text-ocean-teal rounded mr-3">
                            <span class="text-sm">Show CDC certificates</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" checked class="text-ocean-teal rounded mr-3">
                            <span class="text-sm">Show vessel experience</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" class="text-ocean-teal rounded mr-3">
                            <span class="text-sm">Show contact information</span>
                        </label>
                    </div>
                </div>
                
                <div class="flex space-x-3">
                    <button onclick="hideShareModal()" class="flex-1 bg-gray-200 text-gray-700 py-2 px-4 rounded-lg hover:bg-gray-300 transition-colors">
                        Cancel
                    </button>
                    <button class="flex-1 bg-navy text-white py-2 px-4 rounded-lg hover:bg-opacity-90 transition-colors">
                        <i class="fas fa-share mr-2"></i>Share Profile
                    </button>
                </div>
            </div>
        </div>
    </div>
    <!-- @END_COMPONENT: ShareProfileModal -->

    <script>
        // TODO: Implement business logic, API calls, and state management
        
        (function() {
            // Modal functionality
            window.showShareModal = function() {
                document.getElementById('shareModal').classList.remove('hidden');
                document.getElementById('shareModal').classList.add('flex');
            };
            
            window.hideShareModal = function() {
                document.getElementById('shareModal').classList.add('hidden');
                document.getElementById('shareModal').classList.remove('flex');
            };
            
            // Close modal on background click
            document.getElementById('shareModal').addEventListener('click', function(e) {
                if (e.target === this) {
                    hideShareModal();
                }
            });
            
            // File upload handling
            const uploadZone = document.querySelector('.border-dashed');
            if (uploadZone) {
                uploadZone.addEventListener('dragover', function(e) {
                    e.preventDefault();
                    this.classList.add('border-ocean-teal', 'bg-ocean-teal', 'bg-opacity-5');
                });
                
                uploadZone.addEventListener('dragleave', function(e) {
                    e.preventDefault();
                    this.classList.remove('border-ocean-teal', 'bg-ocean-teal', 'bg-opacity-5');
                });
                
                uploadZone.addEventListener('drop', function(e) {
                    e.preventDefault();
                    this.classList.remove('border-ocean-teal', 'bg-ocean-teal', 'bg-opacity-5');
                    // TODO: Handle file upload
                    console.log('Files dropped:', e.dataTransfer.files);
                });
            }
            
            // Copy link functionality
            document.addEventListener('click', function(e) {
                if (e.target.closest('.fa-copy')) {
                    const input = e.target.closest('.flex').querySelector('input');
                    input.select();
                    document.execCommand('copy');
                    // TODO: Show success notification
                }
            });
        })();
    </script>
</body>
</html>