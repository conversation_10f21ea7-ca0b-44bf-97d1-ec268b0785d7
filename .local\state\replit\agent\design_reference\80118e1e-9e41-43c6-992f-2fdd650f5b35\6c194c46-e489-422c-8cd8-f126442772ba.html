<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>1234 Koi Hai? - Maritime Professional Discovery</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'maritime-navy': '#1B365D',
                        'maritime-teal': '#2DD4BF',
                        'maritime-light': '#F8FAFC',
                        'maritime-gray': '#64748B'
                    },
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif']
                    }
                }
            }
        };
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="font-inter bg-maritime-light">
    <!-- Landing Page -->
    <!-- @COMPONENT: LandingPage -->
    <div id="landing-page" class="min-h-screen bg-gradient-to-br from-maritime-navy via-blue-800 to-maritime-teal">
        <!-- Header -->
        <header class="relative z-10 px-4 py-6">
            <div class="max-w-6xl mx-auto flex justify-between items-center">
                <div class="text-white font-bold text-xl">
                    <i class="fas fa-anchor mr-2"></i>Koi
                </div>
                <div class="hidden md:flex space-x-6 text-white">
                    <a href="#features" class="hover:text-maritime-teal transition-colors">Features</a>
                    <a href="#privacy" class="hover:text-maritime-teal transition-colors">Privacy</a>
                    <a href="#support" class="hover:text-maritime-teal transition-colors">Support</a>
                </div>
                <button class="bg-maritime-teal text-white px-4 py-2 rounded-lg hover:bg-teal-400 transition-colors">
                    Login
                </button>
            </div>
        </header>

        <!-- Hero Section -->
        <section class="relative z-10 px-4 py-16 text-center">
            <div class="max-w-4xl mx-auto">
                <h1 class="text-5xl md:text-7xl font-bold text-white mb-6">
                    1234 <span class="text-maritime-teal">Koi Hai?</span>
                </h1>
                <p class="text-xl md:text-2xl text-blue-100 mb-8 max-w-2xl mx-auto">
                    Connect with maritime professionals nearby. Discover officers and crew members at your port or location.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
                    <button onclick="showDiscoveryApp()" class="bg-maritime-teal text-white px-8 py-4 rounded-xl text-lg font-semibold hover:bg-teal-400 transition-colors shadow-lg">
                        <i class="fas fa-compass mr-2"></i>Start Discovering
                    </button>
                    <a href="https://qaaqit.replit.app/" target="_blank" class="border-2 border-white text-white px-8 py-4 rounded-xl text-lg font-semibold hover:bg-white hover:text-maritime-navy transition-colors">
                        <i class="fas fa-database mr-2"></i>Visit QAAQ
                    </a>
                </div>

                <!-- Feature Preview Cards -->
                <div class="grid md:grid-cols-2 gap-6 max-w-2xl mx-auto">
                    <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-left">
                        <i class="fas fa-map-marker-alt text-maritime-teal text-2xl mb-3"></i>
                        <h3 class="text-white font-semibold text-lg mb-2">Location-Based Discovery</h3>
                        <p class="text-blue-100 text-sm">Find officers and crew within 1-50km radius of your current location.</p>
                    </div>
                    <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6 text-left">
                        <i class="fas fa-ship text-maritime-teal text-2xl mb-3"></i>
                        <h3 class="text-white font-semibold text-lg mb-2">Vessel-Based Connection</h3>
                        <p class="text-blue-100 text-sm">Connect with crew members from the same vessel or nearby ships in port.</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Maritime Professional Images -->
        <section class="relative z-10 px-4 py-12">
            <div class="max-w-6xl mx-auto">
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <!-- Maritime officers meeting casually -->
                    <div class="rounded-xl overflow-hidden shadow-lg">
                        <!-- Maritime officers in casual discussion on ship deck -->
                        <img src="https://images.unsplash.com/photo-1544551763-46a013bb70d5?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300" alt="Maritime officers meeting casually" class="w-full h-32 object-cover" />
                    </div>
                    <div class="rounded-xl overflow-hidden shadow-lg">
                        <!-- Ship officers reviewing charts together -->
                        <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300" alt="Ship crew social networking" class="w-full h-32 object-cover" />
                    </div>
                    <div class="rounded-xl overflow-hidden shadow-lg">
                        <!-- Sailors connecting at busy maritime port -->
                        <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300" alt="Sailors connecting at port" class="w-full h-32 object-cover" />
                    </div>
                    <div class="rounded-xl overflow-hidden shadow-lg">
                        <!-- Maritime professionals networking at port facility -->
                        <img src="https://images.unsplash.com/photo-1544551763-46a013bb70d5?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300" alt="Maritime professionals networking" class="w-full h-32 object-cover" />
                    </div>
                </div>
            </div>
        </section>

        <!-- Ocean Wave Animation -->
        <div class="absolute bottom-0 left-0 right-0 h-24 bg-gradient-to-t from-maritime-teal/20 to-transparent"></div>
    </div>
    <!-- @END_COMPONENT: LandingPage -->

    <!-- Discovery App -->
    <!-- @COMPONENT: DiscoveryApp -->
    <div id="discovery-app" class="hidden min-h-screen bg-maritime-light">
        <!-- Top Navigation -->
        <nav class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-40">
            <div class="max-w-md mx-auto px-4 py-3">
                <div class="flex items-center justify-between">
                    <button onclick="showLandingPage()" class="text-maritime-gray hover:text-maritime-navy">
                        <i class="fas fa-arrow-left text-lg"></i>
                    </button>
                    <h1 class="font-bold text-lg text-maritime-navy">
                        <i class="fas fa-anchor mr-2"></i>Koi Hai?
                    </h1>
                    <button class="text-maritime-gray hover:text-maritime-navy">
                        <i class="fas fa-cog text-lg"></i>
                    </button>
                </div>
            </div>
        </nav>

        <!-- Tab Navigation -->
        <div class="bg-white border-b border-gray-200">
            <div class="max-w-md mx-auto px-4">
                <div class="flex">
                    <button onclick="showTab('officers')" id="officers-tab" class="flex-1 py-4 text-center font-medium border-b-2 border-maritime-teal text-maritime-navy">
                        <i class="fas fa-user-tie mr-2"></i>Officers Nearby
                    </button>
                    <button onclick="showTab('crew')" id="crew-tab" class="flex-1 py-4 text-center font-medium border-b-2 border-transparent text-maritime-gray hover:text-maritime-navy">
                        <i class="fas fa-users mr-2"></i>Crew Nearby
                    </button>
                </div>
            </div>
        </div>

        <!-- Distance Filter -->
        <div class="bg-white border-b border-gray-200">
            <div class="max-w-md mx-auto px-4 py-4">
                <div class="flex items-center justify-between mb-3">
                    <span class="text-sm font-medium text-maritime-gray">Distance Range</span>
                    <span class="text-sm font-bold text-maritime-navy" id="distance-value">25km</span>
                </div>
                <div class="relative">
                    <input type="range" min="1" max="50" value="25" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider" id="distance-slider" oninput="updateDistance(this.value)">
                    <div class="flex justify-between text-xs text-maritime-gray mt-2">
                        <span>1km</span>
                        <span>50km</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Officers Tab Content -->
        <div id="officers-content" class="max-w-md mx-auto px-4 py-6 space-y-4">
            <!-- Profile Card 1 -->
            <div class="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden" data-mock="true">
                <div class="p-4">
                    <div class="flex items-start justify-between mb-3">
                        <div class="flex items-center space-x-3">
                            <div class="w-12 h-12 bg-maritime-navy rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-white"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-lg text-maritime-navy" data-bind="officer.name">Captain Rajesh Kumar</h3>
                                <p class="text-maritime-gray text-sm" data-bind="officer.rank">Chief Engineer</p>
                            </div>
                        </div>
                        <span class="text-xs bg-maritime-teal/10 text-maritime-teal px-2 py-1 rounded-full" data-bind="officer.distance">2.3km</span>
                    </div>
                    <div class="mb-4">
                        <p class="text-sm text-maritime-gray mb-1">
                            <i class="fas fa-ship mr-2"></i><span data-bind="officer.vessel">MV Ocean Explorer</span>
                        </p>
                        <p class="text-sm text-maritime-gray">
                            <i class="fas fa-clock mr-2"></i>Active 5 minutes ago
                        </p>
                    </div>
                    <button class="w-full bg-maritime-teal text-white py-3 rounded-lg font-medium hover:bg-teal-500 transition-colors" data-event="click:sendHello">
                        <i class="fas fa-paper-plane mr-2"></i>Say Hello
                    </button>
                </div>
            </div>

            <!-- Profile Card 2 -->
            <div class="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden" data-mock="true">
                <div class="p-4">
                    <div class="flex items-start justify-between mb-3">
                        <div class="flex items-center space-x-3">
                            <div class="w-12 h-12 bg-maritime-navy rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-white"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-lg text-maritime-navy" data-bind="officer.name">2nd Officer Maria Santos</h3>
                                <p class="text-maritime-gray text-sm" data-bind="officer.rank">Navigation Officer</p>
                            </div>
                        </div>
                        <span class="text-xs bg-maritime-teal/10 text-maritime-teal px-2 py-1 rounded-full" data-bind="officer.distance">1.8km</span>
                    </div>
                    <div class="mb-4">
                        <p class="text-sm text-maritime-gray mb-1">
                            <i class="fas fa-ship mr-2"></i><span data-bind="officer.vessel">MS Baltic Harmony</span>
                        </p>
                        <p class="text-sm text-maritime-gray">
                            <i class="fas fa-clock mr-2"></i>Active 12 minutes ago
                        </p>
                    </div>
                    <button class="w-full bg-maritime-teal text-white py-3 rounded-lg font-medium hover:bg-teal-500 transition-colors" data-event="click:sendHello">
                        <i class="fas fa-paper-plane mr-2"></i>Say Hello
                    </button>
                </div>
            </div>

            <!-- Profile Card 3 -->
            <div class="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden" data-mock="true">
                <div class="p-4">
                    <div class="flex items-start justify-between mb-3">
                        <div class="flex items-center space-x-3">
                            <div class="w-12 h-12 bg-maritime-navy rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-white"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-lg text-maritime-navy" data-bind="officer.name">Chief Engineer Piyush Gupta</h3>
                                <p class="text-maritime-gray text-sm" data-bind="officer.rank">Chief Engineer</p>
                            </div>
                        </div>
                        <span class="text-xs bg-maritime-teal/10 text-maritime-teal px-2 py-1 rounded-full" data-bind="officer.distance">0.5km</span>
                    </div>
                    <div class="mb-4">
                        <p class="text-sm text-maritime-gray mb-1">
                            <i class="fas fa-ship mr-2"></i><span data-bind="officer.vessel">MT Solar Claire</span>
                        </p>
                        <p class="text-sm text-maritime-gray">
                            <i class="fas fa-clock mr-2"></i>Active now
                        </p>
                    </div>
                    <button class="w-full bg-maritime-teal text-white py-3 rounded-lg font-medium hover:bg-teal-500 transition-colors" data-event="click:sendHello">
                        <i class="fas fa-paper-plane mr-2"></i>Say Hello
                    </button>
                </div>
            </div>
        </div>

        <!-- Crew Tab Content -->
        <div id="crew-content" class="hidden max-w-md mx-auto px-4 py-6 space-y-4">
            <!-- @MAP: crewMembers.map(member => ( -->
            <div class="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden" data-mock="true">
                <div class="p-4">
                    <div class="flex items-start justify-between mb-3">
                        <div class="flex items-center space-x-3">
                            <div class="w-12 h-12 bg-maritime-navy rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-white"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-lg text-maritime-navy" data-bind="crew.name">Bosun Ahmed Ali</h3>
                                <p class="text-maritime-gray text-sm" data-bind="crew.rank">Bosun</p>
                            </div>
                        </div>
                        <span class="text-xs bg-maritime-teal/10 text-maritime-teal px-2 py-1 rounded-full" data-bind="crew.distance">0.2km</span>
                    </div>
                    <div class="mb-4">
                        <p class="text-sm text-maritime-gray mb-1">
                            <i class="fas fa-ship mr-2"></i><span data-bind="crew.vessel">MV Ocean Explorer</span>
                        </p>
                        <p class="text-sm text-maritime-gray">
                            <i class="fas fa-clock mr-2"></i>Active 2 minutes ago
                        </p>
                    </div>
                    <button class="w-full bg-maritime-teal text-white py-3 rounded-lg font-medium hover:bg-teal-500 transition-colors" data-event="click:sendHello">
                        <i class="fas fa-paper-plane mr-2"></i>Say Hello
                    </button>
                </div>
            </div>
            <!-- @END_MAP )) -->

            <div class="bg-white rounded-xl shadow-md border border-gray-100 overflow-hidden" data-mock="true">
                <div class="p-4">
                    <div class="flex items-start justify-between mb-3">
                        <div class="flex items-center space-x-3">
                            <div class="w-12 h-12 bg-maritime-navy rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-white"></i>
                            </div>
                            <div>
                                <h3 class="font-semibold text-lg text-maritime-navy" data-bind="crew.name">AB Seaman Carlos Rodriguez</h3>
                                <p class="text-maritime-gray text-sm" data-bind="crew.rank">Able Seaman</p>
                            </div>
                        </div>
                        <span class="text-xs bg-maritime-teal/10 text-maritime-teal px-2 py-1 rounded-full" data-bind="crew.distance">1.1km</span>
                    </div>
                    <div class="mb-4">
                        <p class="text-sm text-maritime-gray mb-1">
                            <i class="fas fa-ship mr-2"></i><span data-bind="crew.vessel">MS Baltic Harmony</span>
                        </p>
                        <p class="text-sm text-maritime-gray">
                            <i class="fas fa-clock mr-2"></i>Active 8 minutes ago
                        </p>
                    </div>
                    <button class="w-full bg-maritime-teal text-white py-3 rounded-lg font-medium hover:bg-teal-500 transition-colors" data-event="click:sendHello">
                        <i class="fas fa-paper-plane mr-2"></i>Say Hello
                    </button>
                </div>
            </div>
        </div>

        <!-- WhatsApp Integration Button -->
        <div class="fixed bottom-6 right-6">
            <button class="bg-green-500 text-white w-14 h-14 rounded-full shadow-lg hover:bg-green-600 transition-colors flex items-center justify-center">
                <i class="fab fa-whatsapp text-xl"></i>
            </button>
        </div>

        <!-- Chat Modal -->
        <!-- @COMPONENT: ChatModal [isOpen, contactName, onClose] -->
        <div id="chat-modal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-end">
            <div class="bg-white w-full max-w-md mx-auto rounded-t-2xl max-h-96 overflow-hidden">
                <div class="bg-maritime-navy p-4 flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                            <i class="fas fa-user text-white"></i>
                        </div>
                        <div>
                            <h3 class="text-white font-medium" id="chat-contact-name">Captain Rajesh Kumar</h3>
                            <p class="text-blue-200 text-sm">Chief Engineer</p>
                        </div>
                    </div>
                    <button onclick="closeChatModal()" class="text-white hover:text-blue-200">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                <div class="p-4 h-48 bg-gray-50 overflow-y-auto">
                    <div class="space-y-3">
                        <div class="bg-white p-3 rounded-lg shadow-sm">
                            <p class="text-sm text-maritime-gray">Hello! I saw you're nearby. Are you also docked at Port Mumbai?</p>
                            <span class="text-xs text-maritime-gray">2 minutes ago</span>
                        </div>
                    </div>
                </div>
                <div class="p-4 border-t border-gray-200">
                    <div class="flex space-x-2">
                        <input type="text" placeholder="Type a message..." class="flex-1 border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-maritime-teal">
                        <button class="bg-maritime-teal text-white px-4 py-2 rounded-lg hover:bg-teal-500 transition-colors">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <!-- @END_COMPONENT: ChatModal -->
    </div>
    <!-- @END_COMPONENT: DiscoveryApp -->

    <!-- Footer -->
    <footer class="bg-maritime-navy text-white py-12">
        <div class="max-w-6xl mx-auto px-4">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <h4 class="font-bold text-lg mb-4">
                        <i class="fas fa-anchor mr-2"></i>Koi Hai?
                    </h4>
                    <p class="text-blue-200 text-sm">Connecting maritime professionals worldwide through smart discovery and networking.</p>
                </div>
                <div>
                    <h5 class="font-semibold mb-4">Quick Links</h5>
                    <ul class="space-y-2 text-sm text-blue-200">
                        <li><a href="#" class="hover:text-white">Privacy Policy</a></li>
                        <li><a href="#" class="hover:text-white">Terms of Service</a></li>
                        <li><a href="#" class="hover:text-white">Account Deletion</a></li>
                    </ul>
                </div>
                <div>
                    <h5 class="font-semibold mb-4">Support</h5>
                    <ul class="space-y-2 text-sm text-blue-200">
                        <li><a href="#" class="hover:text-white">Help Center</a></li>
                        <li><a href="#" class="hover:text-white">Contact Us</a></li>
                        <li><a href="https://qaaqit.replit.app/" class="hover:text-white">QAAQ Platform</a></li>
                    </ul>
                </div>
                <div>
                    <h5 class="font-semibold mb-4">Connect</h5>
                    <div class="flex space-x-4">
                        <a href="https://wa.me/************" class="text-green-400 hover:text-green-300">
                            <i class="fab fa-whatsapp text-xl"></i>
                        </a>
                        <a href="#" class="text-blue-200 hover:text-white">
                            <i class="fab fa-linkedin text-xl"></i>
                        </a>
                    </div>
                    <p class="text-xs text-blue-200 mt-2">WhatsApp: +************</p>
                </div>
            </div>
            <div class="border-t border-blue-800 mt-8 pt-8 text-center text-sm text-blue-200">
                <p>&copy; 2024 Koi Hai? Maritime Discovery. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <!-- JavaScript -->
    <script>
        // @STATE: currentTab:string = 'officers'
        // @STATE: distanceValue:number = 25
        
        (function() {
            // Initialize app
            function showLandingPage() {
                document.getElementById('landing-page').classList.remove('hidden');
                document.getElementById('discovery-app').classList.add('hidden');
            }

            function showDiscoveryApp() {
                document.getElementById('landing-page').classList.add('hidden');
                document.getElementById('discovery-app').classList.remove('hidden');
            }

            function showTab(tabName) {
                // Update tab buttons
                const officersTab = document.getElementById('officers-tab');
                const crewTab = document.getElementById('crew-tab');
                const officersContent = document.getElementById('officers-content');
                const crewContent = document.getElementById('crew-content');

                if (tabName === 'officers') {
                    officersTab.classList.add('border-maritime-teal', 'text-maritime-navy');
                    officersTab.classList.remove('border-transparent', 'text-maritime-gray');
                    crewTab.classList.add('border-transparent', 'text-maritime-gray');
                    crewTab.classList.remove('border-maritime-teal', 'text-maritime-navy');
                    
                    officersContent.classList.remove('hidden');
                    crewContent.classList.add('hidden');
                } else {
                    crewTab.classList.add('border-maritime-teal', 'text-maritime-navy');
                    crewTab.classList.remove('border-transparent', 'text-maritime-gray');
                    officersTab.classList.add('border-transparent', 'text-maritime-gray');
                    officersTab.classList.remove('border-maritime-teal', 'text-maritime-navy');
                    
                    crewContent.classList.remove('hidden');
                    officersContent.classList.add('hidden');
                }
            }

            function updateDistance(value) {
                document.getElementById('distance-value').textContent = value + 'km';
                // TODO: Implement distance filter API call
            }

            function openChatModal(contactName) {
                document.getElementById('chat-contact-name').textContent = contactName;
                document.getElementById('chat-modal').classList.remove('hidden');
            }

            function closeChatModal() {
                document.getElementById('chat-modal').classList.add('hidden');
            }

            // Attach functions to window for onclick handlers
            window.showLandingPage = showLandingPage;
            window.showDiscoveryApp = showDiscoveryApp;
            window.showTab = showTab;
            window.updateDistance = updateDistance;
            window.openChatModal = openChatModal;
            window.closeChatModal = closeChatModal;

            // Add click handlers for Say Hello buttons
            document.addEventListener('click', function(e) {
                if (e.target.closest('[data-event="click:sendHello"]')) {
                    const card = e.target.closest('.bg-white');
                    const contactName = card.querySelector('[data-bind*="name"]').textContent;
                    openChatModal(contactName);
                }
            });

            // Custom slider styling
            const style = document.createElement('style');
            style.textContent = `
                .slider::-webkit-slider-thumb {
                    appearance: none;
                    height: 20px;
                    width: 20px;
                    border-radius: 50%;
                    background: #2DD4BF;
                    cursor: pointer;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
                }
                .slider::-moz-range-thumb {
                    height: 20px;
                    width: 20px;
                    border-radius: 50%;
                    background: #2DD4BF;
                    cursor: pointer;
                    border: none;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
                }
            `;
            document.head.appendChild(style);
        })();
    </script>
</body>
</html>