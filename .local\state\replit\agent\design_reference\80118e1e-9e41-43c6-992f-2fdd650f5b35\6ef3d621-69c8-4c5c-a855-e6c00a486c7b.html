<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>1234 Koi Hai? - Connect with Maritime Professionals Worldwide</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        navy: {
                            50: '#eff6ff',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e40af',
                            900: '#1e3a8a'
                        },
                        teal: {
                            50: '#f0fdfa',
                            500: '#14b8a6',
                            600: '#0d9488',
                            700: '#0f766e',
                            800: '#115e59',
                            900: '#134e4a'
                        }
                    },
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <script src="https://unpkg.com/heroicons@2.0.18/24/outline/index.js" type="module"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/heroicons/2.0.18/24/outline/heroicons-outline.min.css">
</head>
<body class="font-inter bg-gray-50 text-gray-900">

    <!-- Navigation Header -->
    <nav class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo -->
                <div class="flex items-center space-x-3">
                    <!-- Maritime anchor icon placeholder -->
                    <div class="w-10 h-10 bg-gradient-to-br from-navy-700 to-teal-600 rounded-full flex items-center justify-center">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2"></path>
                        </svg>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-navy-900">1234 Koi Hai?</h1>
                        <p class="text-xs text-gray-500">Maritime Connections</p>
                    </div>
                </div>

                <!-- Mobile menu button -->
                <button class="md:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>

                <!-- Desktop Navigation -->
                <div class="hidden md:flex items-center space-x-6">
                    <a href="#" class="text-gray-700 hover:text-navy-700 font-medium">Discover</a>
                    <a href="#" class="text-gray-700 hover:text-navy-700 font-medium">Community</a>
                    <a href="#" class="text-gray-700 hover:text-navy-700 font-medium">QAAQ AI</a>
                    <button class="bg-navy-700 text-white px-4 py-2 rounded-lg hover:bg-navy-800 transition-colors">
                        Join Now
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- QAAQ AI Question Bar -->
    <div class="bg-gradient-to-r from-navy-700 to-teal-600 py-3">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center space-x-3">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                </svg>
                <input 
                    type="text" 
                    placeholder="Ask QAAQ AI: 'Good food in Dubai Terminal 3?' or 'WiFi password Singapore Changi?'"
                    class="flex-1 bg-white/10 text-white placeholder-white/70 border border-white/20 rounded-lg px-4 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-white/30"
                />
                <button class="bg-white/20 text-white px-4 py-2 rounded-lg hover:bg-white/30 transition-colors text-sm font-medium">
                    Ask AI
                </button>
            </div>
        </div>
    </div>

    <!-- Hero Section -->
    <section class="relative bg-gradient-to-br from-navy-900 via-navy-800 to-teal-800 text-white overflow-hidden">
        <!-- Background pattern -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%23ffffff" fill-opacity="0.1"%3E%3Cpath d="M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E');"></div>
        </div>
        
        <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 lg:py-24">
            <div class="grid lg:grid-cols-2 gap-12 items-center">
                <!-- Content -->
                <div class="space-y-8">
                    <div class="space-y-4">
                        <div class="inline-flex items-center space-x-2 bg-white/10 rounded-full px-4 py-2 text-sm">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            <span>Connect at Ports & Airports Worldwide</span>
                        </div>
                        <h1 class="text-4xl lg:text-6xl font-bold leading-tight">
                            Find Maritime <br/>
                            <span class="text-transparent bg-clip-text bg-gradient-to-r from-teal-400 to-blue-400">
                                Professionals
                            </span><br/>
                            Nearby
                        </h1>
                        <p class="text-xl text-white/80 leading-relaxed max-w-2xl">
                            Whether you're at a port or airport, discover fellow sailors, share experiences, and build professional connections that last beyond your journey.
                        </p>
                    </div>

                    <!-- Location Mode Toggle -->
                    <div class="bg-white/10 backdrop-blur-sm rounded-xl p-6 border border-white/20">
                        <h3 class="font-semibold mb-4 text-lg">Choose Your Location Mode</h3>
                        <div class="grid grid-cols-2 gap-4">
                            <button class="bg-teal-600 hover:bg-teal-700 transition-colors rounded-lg p-4 text-left">
                                <div class="flex items-center space-x-3 mb-2">
                                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2"></path>
                                    </svg>
                                    <span class="font-medium">Port Mode</span>
                                </div>
                                <p class="text-sm text-white/80">Find officers & crew at nearby ports</p>
                            </button>
                            <button class="bg-white/10 hover:bg-white/20 transition-colors rounded-lg p-4 text-left border border-white/20">
                                <div class="flex items-center space-x-3 mb-2">
                                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                    </svg>
                                    <span class="font-medium">Airport Mode</span>
                                </div>
                                <p class="text-sm text-white/80">Connect with sailors in transit</p>
                            </button>
                        </div>
                    </div>

                    <!-- CTA Buttons -->
                    <div class="flex flex-col sm:flex-row gap-4">
                        <button class="bg-white text-navy-900 px-8 py-4 rounded-xl font-semibold hover:bg-gray-100 transition-colors flex items-center justify-center space-x-2">
                            <span>Start Connecting</span>
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                            </svg>
                        </button>
                        <button class="border-2 border-white/30 text-white px-8 py-4 rounded-xl font-semibold hover:bg-white/10 transition-colors">
                            Watch Demo
                        </button>
                    </div>
                </div>

                <!-- Image Section -->
                <div class="relative">
                    <!-- Maritime professionals at airport networking -->
                    <img src="https://images.unsplash.com/photo-1436491865332-7a61a109cc05?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&h=600" alt="Maritime professionals networking at airport" class="rounded-2xl shadow-2xl w-full" />
                    
                    <!-- Floating connection cards -->
                    <div class="absolute -top-4 -left-4 bg-white rounded-xl p-4 shadow-lg max-w-xs">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-teal-600 rounded-full flex items-center justify-center text-white text-sm font-semibold">
                                PG
                            </div>
                            <div>
                                <p class="font-semibold text-gray-900 text-sm">Piyush Gupta</p>
                                <p class="text-gray-500 text-xs">Chief Engineer • Terminal 3</p>
                            </div>
                        </div>
                        <p class="text-xs text-gray-600 mt-2">Coffee? 2h layover in Dubai</p>
                    </div>

                    <div class="absolute -bottom-4 -right-4 bg-white rounded-xl p-4 shadow-lg max-w-xs">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="font-semibold text-gray-900 text-sm">5 Sailors Nearby</p>
                                <p class="text-gray-500 text-xs">Within 500m radius</p>
                            </div>
                            <div class="bg-teal-600 text-white rounded-full w-8 h-8 flex items-center justify-center text-xs font-semibold">
                                5
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Core Features Section -->
    <section class="py-16 lg:py-24 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                    Discover & Connect Anywhere
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    From busy port terminals to airport lounges, find your maritime community wherever your journey takes you.
                </p>
            </div>

            <div class="grid lg:grid-cols-3 gap-8">
                <!-- Port Discovery -->
                <div class="bg-gradient-to-br from-navy-50 to-teal-50 rounded-2xl p-8 border border-gray-200">
                    <div class="w-12 h-12 bg-navy-600 rounded-xl flex items-center justify-center mb-6">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Port Discovery</h3>
                    <p class="text-gray-600 mb-6">
                        Find officers and crew members at nearby ports. Share local knowledge, recommend services, and build professional networks dockside.
                    </p>
                    <!-- Port activity feed -->
                    <div class="space-y-3">
                        <div class="bg-white rounded-lg p-3 shadow-sm">
                            <div class="flex items-center space-x-2 text-sm">
                                <div class="w-6 h-6 bg-teal-600 rounded-full text-white text-xs flex items-center justify-center">A</div>
                                <span class="font-medium">Alex M.</span>
                                <span class="text-gray-500">• Port of Rotterdam</span>
                            </div>
                            <p class="text-xs text-gray-600 mt-1">Best crew mess near Europoort? Need recommendations!</p>
                        </div>
                        <div class="bg-white rounded-lg p-3 shadow-sm">
                            <div class="flex items-center space-x-2 text-sm">
                                <div class="w-6 h-6 bg-navy-600 rounded-full text-white text-xs flex items-center justify-center">M</div>
                                <span class="font-medium">Maria S.</span>
                                <span class="text-gray-500">• Singapore Port</span>
                            </div>
                            <p class="text-xs text-gray-600 mt-1">Shore leave buddy needed for city tour tomorrow</p>
                        </div>
                    </div>
                </div>

                <!-- Airport Transit -->
                <div class="bg-gradient-to-br from-teal-50 to-blue-50 rounded-2xl p-8 border border-gray-200">
                    <div class="w-12 h-12 bg-teal-600 rounded-xl flex items-center justify-center mb-6">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">Airport Transit</h3>
                    <p class="text-gray-600 mb-6">
                        Connect with sailors in transit at airports worldwide. Share travel tips, find coffee companions, and make layovers more meaningful.
                    </p>
                    <!-- Airport activity feed -->
                    <div class="space-y-3">
                        <div class="bg-white rounded-lg p-3 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2 text-sm">
                                    <div class="w-6 h-6 bg-navy-600 rounded-full text-white text-xs flex items-center justify-center">J</div>
                                    <span class="font-medium">John D.</span>
                                </div>
                                <span class="text-xs text-teal-600 font-medium">3h layover</span>
                            </div>
                            <p class="text-xs text-gray-600 mt-1">Dubai Terminal 3 - Coffee anyone?</p>
                        </div>
                        <div class="bg-white rounded-lg p-3 shadow-sm">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2 text-sm">
                                    <div class="w-6 h-6 bg-teal-600 rounded-full text-white text-xs flex items-center justify-center">P</div>
                                    <span class="font-medium">Piyush G.</span>
                                </div>
                                <span class="text-xs text-navy-600 font-medium">Gate A24</span>
                            </div>
                            <p class="text-xs text-gray-600 mt-1">Free shower location in Amsterdam Terminal 2</p>
                        </div>
                    </div>
                </div>

                <!-- QAAQ AI Integration -->
                <div class="bg-gradient-to-br from-purple-50 to-indigo-50 rounded-2xl p-8 border border-gray-200">
                    <div class="w-12 h-12 bg-purple-600 rounded-xl flex items-center justify-center mb-6">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-4">QAAQ AI Enhanced</h3>
                    <p class="text-gray-600 mb-6">
                        Our AI now includes airports in the location hierarchy. Get instant answers about terminals, services, and travel tips from maritime professionals.
                    </p>
                    <!-- AI examples -->
                    <div class="space-y-3">
                        <div class="bg-white rounded-lg p-3 shadow-sm border-l-4 border-purple-500">
                            <p class="text-xs font-medium text-gray-900">"WiFi password Singapore Changi Terminal 1?"</p>
                            <p class="text-xs text-gray-500 mt-1">✓ Answered by AI + Community</p>
                        </div>
                        <div class="bg-white rounded-lg p-3 shadow-sm border-l-4 border-teal-500">
                            <p class="text-xs font-medium text-gray-900">"Best food in Dubai Terminal 3 under $20?"</p>
                            <p class="text-xs text-gray-500 mt-1">✓ Real reviews from sailors</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Live Activity Feed -->
    <section class="py-16 lg:py-24 bg-gray-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                    Community Activity Feed
                </h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                    Real posts from maritime professionals sharing experiences, tips, and connections from around the world.
                </p>
            </div>

            <div class="grid lg:grid-cols-2 gap-8">
                <!-- Feed Post 1 -->
                <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
                    <div class="flex items-start space-x-4">
                        <div class="w-12 h-12 bg-gradient-to-br from-navy-600 to-teal-600 rounded-full flex items-center justify-center text-white font-semibold">
                            PG
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center space-x-2">
                                <h4 class="font-semibold text-gray-900">Piyush Gupta</h4>
                                <span class="text-sm text-gray-500">Chief Engineer</span>
                                <span class="text-sm text-gray-400">•</span>
                                <span class="text-sm text-gray-500">2h ago</span>
                            </div>
                            <div class="flex items-center space-x-2 mt-1">
                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                </svg>
                                <span class="text-sm text-gray-500">Dubai International Airport - Terminal 3</span>
                            </div>
                            <p class="text-gray-700 mt-3 leading-relaxed">
                                Found the free shower facility on Level 3 near Gate A24! Clean towels provided, perfect for long layovers. Fellow sailors, save your money! 🚿
                            </p>
                            <!-- Post image placeholder for shower facility -->
                            <img src="https://images.unsplash.com/photo-1551698618-1dfe5d97d256?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=600&h=300" alt="Airport shower facility" class="rounded-xl mt-4 w-full h-48 object-cover" />
                            <div class="flex items-center justify-between mt-4">
                                <div class="flex items-center space-x-6">
                                    <button class="flex items-center space-x-2 text-gray-500 hover:text-teal-600">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                        </svg>
                                        <span class="text-sm">24</span>
                                    </button>
                                    <button class="flex items-center space-x-2 text-gray-500 hover:text-navy-600">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                        </svg>
                                        <span class="text-sm">8</span>
                                    </button>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="bg-teal-100 text-teal-800 px-2 py-1 rounded-full text-xs font-medium">#AirportTips</span>
                                    <span class="bg-navy-100 text-navy-800 px-2 py-1 rounded-full text-xs font-medium">#Dubai</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Feed Post 2 -->
                <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
                    <div class="flex items-start space-x-4">
                        <div class="w-12 h-12 bg-gradient-to-br from-teal-600 to-blue-600 rounded-full flex items-center justify-center text-white font-semibold">
                            AM
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center space-x-2">
                                <h4 class="font-semibold text-gray-900">Alex Martinez</h4>
                                <span class="text-sm text-gray-500">2nd Engineer</span>
                                <span class="text-sm text-gray-400">•</span>
                                <span class="text-sm text-gray-500">4h ago</span>
                            </div>
                            <div class="flex items-center space-x-2 mt-1">
                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2"></path>
                                </svg>
                                <span class="text-sm text-gray-500">Port of Rotterdam - Europoort</span>
                            </div>
                            <p class="text-gray-700 mt-3 leading-relaxed">
                                Anyone at Europoort today? Looking for a shore leave buddy to explore the city. First time in Rotterdam! 🇳🇱
                            </p>
                            <div class="bg-gradient-to-r from-teal-50 to-blue-50 rounded-lg p-4 mt-4 border border-teal-200">
                                <div class="flex items-center space-x-3">
                                    <svg class="w-5 h-5 text-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    <div>
                                        <p class="text-sm font-medium text-gray-900">Shore leave available: 6 hours</p>
                                        <p class="text-xs text-gray-600">Departing at 22:00 local time</p>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center justify-between mt-4">
                                <div class="flex items-center space-x-6">
                                    <button class="flex items-center space-x-2 text-gray-500 hover:text-teal-600">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                        </svg>
                                        <span class="text-sm">12</span>
                                    </button>
                                    <button class="flex items-center space-x-2 text-gray-500 hover:text-navy-600">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                        </svg>
                                        <span class="text-sm">5</span>
                                    </button>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="bg-navy-100 text-navy-800 px-2 py-1 rounded-full text-xs font-medium">#ShoreLeave</span>
                                    <span class="bg-orange-100 text-orange-800 px-2 py-1 rounded-full text-xs font-medium">#Rotterdam</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Feed Post 3 -->
                <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
                    <div class="flex items-start space-x-4">
                        <div class="w-12 h-12 bg-gradient-to-br from-purple-600 to-pink-600 rounded-full flex items-center justify-center text-white font-semibold">
                            MS
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center space-x-2">
                                <h4 class="font-semibold text-gray-900">Maria Santos</h4>
                                <span class="text-sm text-gray-500">Chief Officer</span>
                                <span class="text-sm text-gray-400">•</span>
                                <span class="text-sm text-gray-500">6h ago</span>
                            </div>
                            <div class="flex items-center space-x-2 mt-1">
                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                </svg>
                                <span class="text-sm text-gray-500">Singapore Changi Airport - Terminal 2</span>
                            </div>
                            <p class="text-gray-700 mt-3 leading-relaxed">
                                Pro tip: Terminal 2 has amazing crew rest areas on Level 4. Free wifi, comfortable seating, and great view of the runway! Perfect for long layovers. ✈️
                            </p>
                            <!-- Video placeholder for airport lounge -->
                            <div class="relative mt-4 bg-gray-100 rounded-xl h-48 flex items-center justify-center">
                                <div class="text-center">
                                    <svg class="w-12 h-12 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1.586a1 1 0 01.707.293l2.414 2.414a1 1 0 00.707.293H15M6 6l6 6 6-6"></path>
                                    </svg>
                                    <p class="text-sm text-gray-500">90s Video: Changi Terminal 2 Crew Areas</p>
                                </div>
                                <div class="absolute top-4 right-4 bg-black/70 text-white px-2 py-1 rounded text-xs">
                                    1:30
                                </div>
                            </div>
                            <div class="flex items-center justify-between mt-4">
                                <div class="flex items-center space-x-6">
                                    <button class="flex items-center space-x-2 text-gray-500 hover:text-teal-600">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                        </svg>
                                        <span class="text-sm">31</span>
                                    </button>
                                    <button class="flex items-center space-x-2 text-gray-500 hover:text-navy-600">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                        </svg>
                                        <span class="text-sm">14</span>
                                    </button>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="bg-purple-100 text-purple-800 px-2 py-1 rounded-full text-xs font-medium">#CrewLounge</span>
                                    <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs font-medium">#Singapore</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Feed Post 4 - Quick Connect -->
                <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
                    <div class="flex items-start space-x-4">
                        <div class="w-12 h-12 bg-gradient-to-br from-orange-600 to-red-600 rounded-full flex items-center justify-center text-white font-semibold">
                            JD
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center space-x-2">
                                <h4 class="font-semibold text-gray-900">John Doe</h4>
                                <span class="text-sm text-gray-500">Chief Engineer</span>
                                <span class="text-sm text-gray-400">•</span>
                                <span class="text-sm text-gray-500">1h ago</span>
                            </div>
                            <div class="flex items-center space-x-2 mt-1">
                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8"></path>
                                </svg>
                                <span class="text-sm text-gray-500">Heathrow Airport - Terminal 5</span>
                            </div>
                            <p class="text-gray-700 mt-3 leading-relaxed">
                                3-hour layover in London. Anyone up for a quick coffee? Gate B42 area has good spots! ☕
                            </p>
                            <div class="bg-gradient-to-r from-orange-50 to-yellow-50 rounded-lg p-4 mt-4 border border-orange-200">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="text-sm font-medium text-gray-900 flex items-center space-x-2">
                                            <svg class="w-4 h-4 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            <span>Quick Connect Available</span>
                                        </p>
                                        <p class="text-xs text-gray-600">Next flight: LHR → JFK in 2h 45m</p>
                                    </div>
                                    <button class="bg-orange-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-orange-700 transition-colors">
                                        Coffee?
                                    </button>
                                </div>
                            </div>
                            <div class="flex items-center justify-between mt-4">
                                <div class="flex items-center space-x-6">
                                    <button class="flex items-center space-x-2 text-gray-500 hover:text-teal-600">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                        </svg>
                                        <span class="text-sm">7</span>
                                    </button>
                                    <button class="flex items-center space-x-2 text-gray-500 hover:text-navy-600">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                                        </svg>
                                        <span class="text-sm">3</span>
                                    </button>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="bg-orange-100 text-orange-800 px-2 py-1 rounded-full text-xs font-medium">#QuickConnect</span>
                                    <span class="bg-blue-100 text-blue-800 px-2 py-1 rounded-full text-xs font-medium">#London</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="text-center mt-12">
                <button class="bg-navy-700 text-white px-8 py-4 rounded-xl font-semibold hover:bg-navy-800 transition-colors">
                    Join the Community
                </button>
            </div>
        </div>
    </section>

    <!-- Safety & Features -->
    <section class="py-16 lg:py-24 bg-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid lg:grid-cols-2 gap-16 items-center">
                <!-- Safety Features -->
                <div>
                    <h2 class="text-3xl lg:text-4xl font-bold text-gray-900 mb-6">
                        Safety-First Airport Connections
                    </h2>
                    <p class="text-xl text-gray-600 mb-8">
                        We prioritize safety in all connections, especially in airport environments where security is paramount.
                    </p>
                    <div class="space-y-6">
                        <div class="flex items-start space-x-4">
                            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center flex-shrink-0">
                                <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900 mb-2">Verified Maritime Profiles</h3>
                                <p class="text-gray-600">All users verified through maritime certificates and professional credentials.</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-4">
                            <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                                <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900 mb-2">Public Area Meetups Only</h3>
                                <p class="text-gray-600">All suggested meetup locations are in public areas like airport lounges and terminals.</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-4">
                            <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center flex-shrink-0">
                                <svg class="w-5 h-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="font-semibold text-gray-900 mb-2">Transit Time Transparency</h3>
                                <p class="text-gray-600">Clear display of connection times and departure schedules for planning safe meetups.</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Features Image -->
                <div class="relative">
                    <!-- Maritime crew at airport lounge -->
                    <img src="https://images.unsplash.com/photo-1556388158-158ea5ccacbd?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&h=600" alt="Maritime crew networking in airport lounge" class="rounded-2xl shadow-2xl w-full" />
                    
                    <!-- Floating feature cards -->
                    <div class="absolute -top-6 -right-6 bg-white rounded-xl p-4 shadow-lg max-w-xs">
                        <h4 class="font-semibold text-gray-900 text-sm mb-2">WhatsApp Integration</h4>
                        <p class="text-gray-600 text-xs">Bot works at airports too: +************</p>
                    </div>

                    <div class="absolute -bottom-6 -left-6 bg-white rounded-xl p-4 shadow-lg max-w-xs">
                        <h4 class="font-semibold text-gray-900 text-sm mb-2">Terminal Detection</h4>
                        <p class="text-gray-600 text-xs">Auto-detects your terminal for precise location matching</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section class="bg-gradient-to-br from-navy-900 via-navy-800 to-teal-800 text-white py-16 lg:py-24">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 class="text-3xl lg:text-5xl font-bold mb-6">
                Never Travel Alone Again
            </h2>
            <p class="text-xl text-white/80 max-w-3xl mx-auto mb-12">
                Join thousands of maritime professionals connecting worldwide. From port terminals to airport lounges, your maritime community is everywhere.
            </p>
            
            <!-- Stats -->
            <div class="grid grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
                <div>
                    <div class="text-3xl lg:text-4xl font-bold text-teal-400 mb-2">2,847</div>
                    <p class="text-white/80">Active Members</p>
                </div>
                <div>
                    <div class="text-3xl lg:text-4xl font-bold text-teal-400 mb-2">156</div>
                    <p class="text-white/80">Ports & Airports</p>
                </div>
                <div>
                    <div class="text-3xl lg:text-4xl font-bold text-teal-400 mb-2">1,203</div>
                    <p class="text-white/80">Connections Made</p>
                </div>
                <div>
                    <div class="text-3xl lg:text-4xl font-bold text-teal-400 mb-2">94%</div>
                    <p class="text-white/80">Positive Reviews</p>
                </div>
            </div>

            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <button class="bg-white text-navy-900 px-8 py-4 rounded-xl font-semibold hover:bg-gray-100 transition-colors flex items-center justify-center space-x-2">
                    <span>Start Connecting Now</span>
                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7l5 5m0 0l-5 5m5-5H6"></path>
                    </svg>
                </button>
                <button class="border-2 border-white/30 text-white px-8 py-4 rounded-xl font-semibold hover:bg-white/10 transition-colors">
                    Download App
                </button>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="bg-gray-900 text-white py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid lg:grid-cols-4 gap-8">
                <!-- Company Info -->
                <div class="lg:col-span-2">
                    <div class="flex items-center space-x-3 mb-6">
                        <div class="w-10 h-10 bg-gradient-to-br from-navy-700 to-teal-600 rounded-full flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 12h14M5 12a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v4a2 2 0 01-2 2M5 12a2 2 0 00-2 2v4a2 2 0 002 2h14a2 2 0 002-2v-4a2 2 0 00-2-2"></path>
                            </svg>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold">1234 Koi Hai?</h3>
                            <p class="text-gray-400 text-sm">Maritime Connections Worldwide</p>
                        </div>
                    </div>
                    <p class="text-gray-400 mb-6 max-w-md">
                        Connecting maritime professionals at ports and airports worldwide. Part of the QAAQ ecosystem for maritime engineering knowledge.
                    </p>
                    <div class="flex items-center space-x-4">
                        <a href="#" class="bg-white/10 p-2 rounded-lg hover:bg-white/20 transition-colors">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"/>
                            </svg>
                        </a>
                        <a href="#" class="bg-white/10 p-2 rounded-lg hover:bg-white/20 transition-colors">
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                            </svg>
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h4 class="font-semibold mb-4">Quick Links</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="#" class="hover:text-white transition-colors">Discover</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Community Feed</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">QAAQ AI</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Safety Guidelines</a></li>
                    </ul>
                </div>

                <!-- Contact -->
                <div>
                    <h4 class="font-semibold mb-4">Contact</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li>WhatsApp Bot:</li>
                        <li class="font-mono text-teal-400">+************</li>
                        <li>Email Support:</li>
                        <li class="text-teal-400"><EMAIL></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
                <p class="text-gray-400 text-sm">
                    © 2024 1234 Koi Hai? A QAAQ Sister Application. All rights reserved.
                </p>
                <div class="flex items-center space-x-6 text-sm text-gray-400 mt-4 md:mt-0">
                    <a href="#" class="hover:text-white transition-colors">Privacy Policy</a>
                    <a href="#" class="hover:text-white transition-colors">Terms of Service</a>
                    <a href="#" class="hover:text-white transition-colors">Help Center</a>
                </div>
            </div>
        </div>
    </footer>

    <script>
        // TODO: Implement business logic, API calls, and state management
        (function() {
            // Simple mobile menu toggle
            const mobileMenuButton = document.querySelector('button[class*="md:hidden"]');
            if (mobileMenuButton) {
                mobileMenuButton.addEventListener('click', function() {
                    // TODO: Implement mobile menu toggle functionality
                    console.log('Mobile menu toggle clicked');
                });
            }

            // Location mode toggle
            const locationButtons = document.querySelectorAll('button[class*="bg-teal-600"], button[class*="bg-white/10"]');
            locationButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // TODO: Implement location mode switching logic
                    locationButtons.forEach(btn => {
                        btn.className = btn.className.replace('bg-teal-600', 'bg-white/10');
                    });
                    this.className = this.className.replace('bg-white/10', 'bg-teal-600');
                });
            });

            // QAAQ AI question submission
            const qaaqButton = document.querySelector('button[class*="bg-white/20"]');
            const qaaqInput = document.querySelector('input[placeholder*="Ask QAAQ AI"]');
            
            if (qaaqButton && qaaqInput) {
                qaaqButton.addEventListener('click', function() {
                    const question = qaaqInput.value.trim();
                    if (question) {
                        // TODO: Implement QAAQ AI question submission
                        console.log('QAAQ AI Question:', question);
                        // Reset input
                        qaaqInput.value = '';
                    }
                });

                qaaqInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        qaaqButton.click();
                    }
                });
            }

            // Feed interactions (like, comment buttons)
            const likeButtons = document.querySelectorAll('button[class*="hover:text-teal-600"]');
            const commentButtons = document.querySelectorAll('button[class*="hover:text-navy-600"]');
            
            likeButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // TODO: Implement like functionality
                    const countSpan = this.querySelector('span');
                    if (countSpan) {
                        const currentCount = parseInt(countSpan.textContent);
                        countSpan.textContent = (currentCount + 1).toString();
                    }
                    this.classList.add('text-teal-600');
                });
            });

            commentButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // TODO: Implement comment functionality
                    console.log('Comment button clicked');
                });
            });

            // Quick connect "Coffee?" buttons
            const coffeeButtons = document.querySelectorAll('button[class*="bg-orange-600"]');
            coffeeButtons.forEach(button => {
                if (button.textContent.includes('Coffee?')) {
                    button.addEventListener('click', function() {
                        // TODO: Implement quick connect functionality
                        alert('Quick connect request sent! The other sailor will be notified.');
                    });
                }
            });

            // CTA buttons
            const ctaButtons = document.querySelectorAll('button[class*="bg-navy-700"], button[class*="bg-white"]');
            ctaButtons.forEach(button => {
                if (button.textContent.includes('Start Connecting') || button.textContent.includes('Join')) {
                    button.addEventListener('click', function() {
                        // TODO: Implement user registration/onboarding flow
                        console.log('Join/Connect button clicked');
                    });
                }
            });
        })();
    </script>
</body>
</html>