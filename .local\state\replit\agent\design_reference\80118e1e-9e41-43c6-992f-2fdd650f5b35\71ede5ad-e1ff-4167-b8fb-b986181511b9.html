<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Koi Hai? - Maritime Discovery Network</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'navy': '#1e3a8a',
                        'ocean-teal': '#0891b2',
                        'light-ocean': '#e0f2fe'
                    },
                    fontFamily: {
                        sans: ['Inter', 'system-ui', 'sans-serif']
                    }
                }
            }
        };
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-light-ocean font-sans">
    <!-- Header with QAAQ Integration -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Brand Logo -->
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-navy rounded-full flex items-center justify-center">
                        <i class="fas fa-anchor text-white text-lg"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-navy">Koi Hai?</h1>
                        <p class="text-xs text-slate-600">Maritime Discovery</p>
                    </div>
                </div>
                
                <!-- QAAQ AI Bar -->
                <div class="hidden md:flex flex-1 max-w-md mx-8">
                    <div class="relative w-full">
                        <input type="text" 
                               placeholder="Ask QAAQ AI about marine equipment..."
                               class="w-full px-4 py-2 pl-10 pr-4 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-ocean-teal focus:border-transparent">
                        <i class="fas fa-robot absolute left-3 top-1/2 transform -translate-y-1/2 text-ocean-teal"></i>
                    </div>
                </div>

                <!-- User Menu -->
                <div class="flex items-center space-x-4">
                    <button class="p-2 rounded-full bg-gray-100 hover:bg-gray-200 transition-colors">
                        <i class="fas fa-bell text-slate-600"></i>
                    </button>
                    <div class="w-8 h-8 bg-ocean-teal rounded-full flex items-center justify-center">
                        <span class="text-white text-sm font-semibold">PG</span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- QAAQ AI Mobile -->
    <div class="md:hidden bg-white px-4 py-3 border-b border-gray-200">
        <div class="relative">
            <input type="text" 
                   placeholder="Ask QAAQ AI..."
                   class="w-full px-4 py-2 pl-10 pr-4 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-ocean-teal focus:border-transparent">
            <i class="fas fa-robot absolute left-3 top-1/2 transform -translate-y-1/2 text-ocean-teal"></i>
        </div>
    </div>

    <!-- Main Content -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            
            <!-- Map Section -->
            <div class="lg:col-span-2">
                <div class="bg-white rounded-xl shadow-lg overflow-hidden">
                    <!-- Map Controls Header -->
                    <div class="bg-navy px-6 py-4">
                        <div class="flex items-center justify-between">
                            <h2 class="text-xl font-semibold text-white">Who's There?</h2>
                            
                            <!-- Filter Dropdown -->
                            <div class="relative">
                                <button id="filterToggle" class="flex items-center space-x-2 bg-white/10 hover:bg-white/20 px-3 py-2 rounded-lg transition-colors">
                                    <span class="text-white text-sm">Filters</span>
                                    <i class="fas fa-chevron-down text-white text-xs" id="filterChevron"></i>
                                </button>
                                
                                <!-- Filter Panel (Hidden by default) -->
                                <div id="filterPanel" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-xl border border-gray-200 z-20 hidden">
                                    <div class="p-4 space-y-3">
                                        <label class="flex items-center space-x-2 cursor-pointer">
                                            <input type="checkbox" class="w-4 h-4 text-ocean-teal border-gray-300 rounded focus:ring-ocean-teal">
                                            <span class="text-sm text-slate-700">Officers only</span>
                                        </label>
                                        <label class="flex items-center space-x-2 cursor-pointer">
                                            <input type="checkbox" class="w-4 h-4 text-ocean-teal border-gray-300 rounded focus:ring-ocean-teal">
                                            <span class="text-sm text-slate-700">Crew only</span>
                                        </label>
                                        <label class="flex items-center space-x-2 cursor-pointer">
                                            <input type="checkbox" class="w-4 h-4 text-ocean-teal border-gray-300 rounded focus:ring-ocean-teal">
                                            <span class="text-sm text-slate-700">Same nationality</span>
                                        </label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Distance Controls -->
                        <div class="flex items-center justify-center mt-4 space-x-4">
                            <button id="decreaseDistance" class="w-10 h-10 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center transition-colors">
                                <i class="fas fa-minus text-white text-sm"></i>
                            </button>
                            <div class="bg-white/20 px-4 py-2 rounded-lg">
                                <span id="currentDistance" class="text-white font-semibold">5km</span>
                            </div>
                            <button id="increaseDistance" class="w-10 h-10 bg-white/20 hover:bg-white/30 rounded-full flex items-center justify-center transition-colors">
                                <i class="fas fa-plus text-white text-sm"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- Map Container -->
                    <div class="relative h-96 bg-gradient-to-br from-blue-50 to-ocean-teal/10">
                        <!-- Map Background -->
                        <div class="absolute inset-0 bg-gradient-to-br from-blue-100 to-blue-200"></div>
                        
                        <!-- Sailor Pins -->
                        <div class="absolute top-1/4 left-1/3 transform -translate-x-1/2 -translate-y-1/2">
                            <div class="relative">
                                <div class="w-8 h-8 bg-ocean-teal rounded-full border-2 border-white shadow-lg flex items-center justify-center cursor-pointer hover:scale-110 transition-transform">
                                    <i class="fas fa-user text-white text-xs"></i>
                                </div>
                                <div class="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-navy text-white px-2 py-1 rounded text-xs whitespace-nowrap opacity-0 hover:opacity-100 transition-opacity">
                                    Chief Engineer - 2.1km
                                </div>
                            </div>
                        </div>
                        
                        <div class="absolute top-2/3 right-1/4 transform -translate-x-1/2 -translate-y-1/2">
                            <div class="relative">
                                <div class="w-8 h-8 bg-navy rounded-full border-2 border-white shadow-lg flex items-center justify-center cursor-pointer hover:scale-110 transition-transform">
                                    <i class="fas fa-user text-white text-xs"></i>
                                </div>
                                <div class="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-navy text-white px-2 py-1 rounded text-xs whitespace-nowrap opacity-0 hover:opacity-100 transition-opacity">
                                    2nd Officer - 3.7km
                                </div>
                            </div>
                        </div>
                        
                        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                            <div class="relative">
                                <div class="w-10 h-10 bg-red-500 rounded-full border-3 border-white shadow-lg flex items-center justify-center animate-pulse">
                                    <i class="fas fa-crosshairs text-white"></i>
                                </div>
                                <div class="absolute -bottom-8 left-1/2 transform -translate-x-1/2 bg-red-500 text-white px-2 py-1 rounded text-xs whitespace-nowrap">
                                    Your Location
                                </div>
                            </div>
                        </div>
                        
                        <div class="absolute top-3/4 left-1/4 transform -translate-x-1/2 -translate-y-1/2">
                            <div class="relative">
                                <div class="w-8 h-8 bg-ocean-teal rounded-full border-2 border-white shadow-lg flex items-center justify-center cursor-pointer hover:scale-110 transition-transform">
                                    <i class="fas fa-user text-white text-xs"></i>
                                </div>
                                <div class="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-navy text-white px-2 py-1 rounded text-xs whitespace-nowrap opacity-0 hover:opacity-100 transition-opacity">
                                    3rd Engineer - 4.2km
                                </div>
                            </div>
                        </div>
                        
                        <!-- Distance Circle -->
                        <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-64 h-64 border-2 border-dashed border-ocean-teal/30 rounded-full"></div>
                    </div>
                </div>
            </div>
            
            <!-- Sailors Nearby Panel -->
            <div class="space-y-4">
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h3 class="text-lg font-semibold text-navy mb-4">Sailors Nearby</h3>
                    
                    <!-- Sailor Card 1 -->
                    <div class="border border-gray-200 rounded-lg p-4 mb-4 hover:shadow-md transition-shadow">
                        <div class="flex items-start space-x-4">
                            <!-- Maritime professional working on deck with safety equipment -->
                            <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&h=100" alt="Chief Engineer profile" class="w-12 h-12 rounded-full object-cover">
                            <div class="flex-1">
                                <div class="flex items-center space-x-2">
                                    <h4 class="font-semibold text-slate-800">Rajesh Kumar</h4>
                                    <img src="https://flagcdn.com/w20/in.png" alt="Indian flag" class="w-5 h-3">
                                </div>
                                <p class="text-sm text-ocean-teal font-medium">Chief Engineer</p>
                                <p class="text-xs text-slate-600">MT Solar Claire</p>
                                <p class="text-xs text-slate-500 mt-1">📍 2.1km away</p>
                                <button class="mt-2 px-3 py-1 bg-ocean-teal text-white rounded-full text-xs hover:bg-ocean-teal/90 transition-colors">
                                    Say Hello
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Sailor Card 2 -->
                    <div class="border border-gray-200 rounded-lg p-4 mb-4 hover:shadow-md transition-shadow">
                        <div class="flex items-start space-x-4">
                            <!-- International sailors meeting at maritime conference -->
                            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&h=100" alt="2nd Officer profile" class="w-12 h-12 rounded-full object-cover">
                            <div class="flex-1">
                                <div class="flex items-center space-x-2">
                                    <h4 class="font-semibold text-slate-800">Mike Chen</h4>
                                    <img src="https://flagcdn.com/w20/ph.png" alt="Philippines flag" class="w-5 h-3">
                                </div>
                                <p class="text-sm text-ocean-teal font-medium">2nd Officer</p>
                                <p class="text-xs text-slate-600">MV Pacific Star</p>
                                <p class="text-xs text-slate-500 mt-1">📍 3.7km away</p>
                                <button class="mt-2 px-3 py-1 bg-ocean-teal text-white rounded-full text-xs hover:bg-ocean-teal/90 transition-colors">
                                    Say Hello
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Sailor Card 3 -->
                    <div class="border border-gray-200 rounded-lg p-4 mb-4 hover:shadow-md transition-shadow">
                        <div class="flex items-start space-x-4">
                            <!-- Maritime crew unity during ship operation -->
                            <img src="https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&h=100" alt="3rd Engineer profile" class="w-12 h-12 rounded-full object-cover">
                            <div class="flex-1">
                                <div class="flex items-center space-x-2">
                                    <h4 class="font-semibold text-slate-800">Ahmed Hassan</h4>
                                    <img src="https://flagcdn.com/w20/eg.png" alt="Egypt flag" class="w-5 h-3">
                                </div>
                                <p class="text-sm text-ocean-teal font-medium">3rd Engineer</p>
                                <p class="text-xs text-slate-600">MT Black Star</p>
                                <p class="text-xs text-slate-500 mt-1">📍 4.2km away</p>
                                <button class="mt-2 px-3 py-1 bg-ocean-teal text-white rounded-full text-xs hover:bg-ocean-teal/90 transition-colors">
                                    Say Hello
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <button class="w-full py-2 text-center text-ocean-teal hover:text-ocean-teal/80 transition-colors text-sm font-medium">
                        View All (12 sailors nearby)
                    </button>
                </div>
                
                <!-- CPSS Quick Access -->
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <h3 class="text-lg font-semibold text-navy mb-4">CPSS Groups</h3>
                    <div class="space-y-3">
                        <div class="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                            <div class="w-8 h-8 bg-ocean-teal rounded-full flex items-center justify-center">
                                <i class="fas fa-cog text-white text-sm"></i>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-slate-800">Engine Room</p>
                                <p class="text-xs text-slate-500">23 active discussions</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                            <div class="w-8 h-8 bg-navy rounded-full flex items-center justify-center">
                                <i class="fas fa-compass text-white text-sm"></i>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-slate-800">Navigation</p>
                                <p class="text-xs text-slate-500">15 active discussions</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-3 p-2 hover:bg-gray-50 rounded-lg cursor-pointer transition-colors">
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-ship text-white text-sm"></i>
                            </div>
                            <div>
                                <p class="text-sm font-medium text-slate-800">Deck Operations</p>
                                <p class="text-xs text-slate-500">8 active discussions</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Featured Community Section -->
        <div class="mt-8 bg-white rounded-xl shadow-lg p-6">
            <h3 class="text-xl font-semibold text-navy mb-6">Maritime Community Highlights</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                
                <!-- Community Image 1 -->
                <div class="text-center">
                    <!-- Sailors networking together at maritime conference -->
                    <img src="https://images.unsplash.com/photo-1541888946425-d81bb19240f5?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&h=200" alt="Sailors networking together" class="w-full h-32 object-cover rounded-lg mb-3">
                    <h4 class="font-semibold text-slate-800 text-sm mb-1">Professional Networking</h4>
                    <p class="text-xs text-slate-600">Connect with maritime professionals worldwide</p>
                </div>
                
                <!-- Community Image 2 -->
                <div class="text-center">
                    <!-- Maritime crew unity during emergency drill -->
                    <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&h=200" alt="Maritime crew unity" class="w-full h-32 object-cover rounded-lg mb-3">
                    <h4 class="font-semibold text-slate-800 text-sm mb-1">Crew Collaboration</h4>
                    <p class="text-xs text-slate-600">Foster teamwork and knowledge sharing</p>
                </div>
                
                <!-- Community Image 3 -->
                <div class="text-center">
                    <!-- International sailors meeting at port community gathering -->
                    <img src="https://images.unsplash.com/photo-1570829460005-c840387bb1ca?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&h=200" alt="International sailors meeting" class="w-full h-32 object-cover rounded-lg mb-3">
                    <h4 class="font-semibold text-slate-800 text-sm mb-1">Global Connections</h4>
                    <p class="text-xs text-slate-600">Meet sailors from every corner of the world</p>
                </div>
                
                <!-- Community Image 4 -->
                <div class="text-center">
                    <!-- Port community gatherings with diverse maritime professionals -->
                    <img src="https://images.unsplash.com/photo-1556075798-4825dfaaf498?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&h=200" alt="Port community gatherings" class="w-full h-32 object-cover rounded-lg mb-3">
                    <h4 class="font-semibold text-slate-800 text-sm mb-1">Port Communities</h4>
                    <p class="text-xs text-slate-600">Join local maritime community events</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Mobile Bottom Navigation -->
    <nav class="md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2">
        <div class="flex justify-around items-center">
            <button class="flex flex-col items-center space-y-1 py-2 px-3 text-navy">
                <i class="fas fa-map-marker-alt text-lg"></i>
                <span class="text-xs font-medium">Discover</span>
            </button>
            <button class="flex flex-col items-center space-y-1 py-2 px-3 text-slate-400">
                <i class="fas fa-users text-lg"></i>
                <span class="text-xs">CPSS</span>
            </button>
            <button class="flex flex-col items-center space-y-1 py-2 px-3 text-slate-400">
                <i class="fas fa-question-circle text-lg"></i>
                <span class="text-xs">QAAQ</span>
            </button>
            <button class="flex flex-col items-center space-y-1 py-2 px-3 text-slate-400">
                <i class="fas fa-user text-lg"></i>
                <span class="text-xs">Profile</span>
            </button>
        </div>
    </nav>

    <script>
        // Distance control functionality
        (function() {
            const distances = [1, 5, 10, 50, 100, 500, 1000, 5000];
            let currentIndex = 1; // Start at 5km
            
            const currentDistanceEl = document.getElementById('currentDistance');
            const decreaseBtn = document.getElementById('decreaseDistance');
            const increaseBtn = document.getElementById('increaseDistance');
            
            function updateDistance() {
                currentDistanceEl.textContent = distances[currentIndex] + 'km';
                decreaseBtn.disabled = currentIndex === 0;
                increaseBtn.disabled = currentIndex === distances.length - 1;
                
                // TODO: Update map display with new distance
                // TODO: Refresh nearby sailors list based on new radius
            }
            
            decreaseBtn.addEventListener('click', () => {
                if (currentIndex > 0) {
                    currentIndex--;
                    updateDistance();
                }
            });
            
            increaseBtn.addEventListener('click', () => {
                if (currentIndex < distances.length - 1) {
                    currentIndex++;
                    updateDistance();
                }
            });
            
            // Initialize
            updateDistance();
        })();
        
        // Filter dropdown functionality
        (function() {
            const filterToggle = document.getElementById('filterToggle');
            const filterPanel = document.getElementById('filterPanel');
            const filterChevron = document.getElementById('filterChevron');
            
            let isOpen = false;
            
            filterToggle.addEventListener('click', () => {
                isOpen = !isOpen;
                
                if (isOpen) {
                    filterPanel.classList.remove('hidden');
                    filterChevron.style.transform = 'rotate(180deg)';
                } else {
                    filterPanel.classList.add('hidden');
                    filterChevron.style.transform = 'rotate(0deg)';
                }
            });
            
            // Close dropdown when clicking outside
            document.addEventListener('click', (e) => {
                if (!filterToggle.contains(e.target) && !filterPanel.contains(e.target)) {
                    isOpen = false;
                    filterPanel.classList.add('hidden');
                    filterChevron.style.transform = 'rotate(0deg)';
                }
            });
            
            // Handle filter changes
            const checkboxes = filterPanel.querySelectorAll('input[type="checkbox"]');
            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', () => {
                    // TODO: Apply filters to map and sailor list
                    // TODO: Update sailor pins based on selected filters
                    console.log('Filter applied:', checkbox.nextElementSibling.textContent);
                });
            });
        })();
        
        // Sailor pin interactions
        (function() {
            const sailorPins = document.querySelectorAll('.cursor-pointer');
            
            sailorPins.forEach(pin => {
                pin.addEventListener('click', (e) => {
                    // TODO: Show detailed sailor profile modal
                    // TODO: Implement "Say Hello" functionality
                    console.log('Sailor pin clicked');
                });
            });
        })();
        
        // QAAQ AI integration
        (function() {
            const qaaqInputs = document.querySelectorAll('input[placeholder*="QAAQ"]');
            
            qaaqInputs.forEach(input => {
                input.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        // TODO: Send query to QAAQ AI system
                        // TODO: Display AI response or redirect to QAAQ platform
                        console.log('QAAQ query:', input.value);
                    }
                });
            });
        })();
    </script>
</body>
</html>