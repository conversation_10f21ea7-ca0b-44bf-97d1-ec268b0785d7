<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>1234 Koi Hai? - Maritime Community Discovery</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                    },
                    colors: {
                        'navy': {
                            600: '#1e40af',
                            700: '#1d4ed8',
                            800: '#1e3a8a',
                            900: '#1e293b'
                        },
                        'ocean': {
                            500: '#0891b2',
                            600: '#0e7490',
                            700: '#155e75'
                        },
                        'coral': {
                            500: '#f97316',
                            600: '#ea580c'
                        }
                    }
                }
            }
        };
    </script>
</head>

<!-- @COMPONENT: MainApp [root component for maritime community discovery] -->
<body class="font-inter bg-slate-50 text-slate-900">
    
    <!-- @COMPONENT: Navigation [top navigation with CPSS breadcrumb] -->
    <nav class="bg-white shadow-sm border-b border-slate-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo and Branding -->
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <div class="w-10 h-10 bg-gradient-to-br from-navy-700 to-ocean-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-anchor text-white text-lg"></i>
                        </div>
                        <div>
                            <h1 class="text-xl font-bold text-navy-800">1234 Koi Hai?</h1>
                            <p class="text-xs text-ocean-600">Maritime Discovery</p>
                        </div>
                    </div>
                </div>

                <!-- CPSS Breadcrumb Navigation -->
                <div class="hidden md:flex items-center space-x-2 text-sm text-slate-600">
                    <span class="hover:text-navy-700 cursor-pointer">UAE</span>
                    <i class="fas fa-chevron-right text-slate-400 text-xs"></i>
                    <span class="hover:text-navy-700 cursor-pointer">Dubai</span>
                    <i class="fas fa-chevron-right text-slate-400 text-xs"></i>
                    <span class="hover:text-navy-700 cursor-pointer">Dubai Airport</span>
                    <i class="fas fa-chevron-right text-slate-400 text-xs"></i>
                    <span class="text-navy-700 font-medium">Terminal 3</span>
                </div>

                <!-- User Menu -->
                <div class="flex items-center space-x-4">
                    <button class="p-2 text-slate-600 hover:text-navy-700">
                        <i class="fas fa-bell text-lg"></i>
                    </button>
                    <!-- @COMPONENT: UserAvatar [user profile image] -->
                    <div class="w-8 h-8 bg-gradient-to-br from-coral-500 to-coral-600 rounded-full flex items-center justify-center">
                        <span class="text-white text-sm font-medium">PG</span>
                    </div>
                    <!-- @END_COMPONENT: UserAvatar -->
                </div>
            </div>
        </div>
    </nav>
    <!-- @END_COMPONENT: Navigation -->

    <!-- @COMPONENT: MainContent [main application content area] -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        <!-- @COMPONENT: HeroSection [main discovery interface] -->
        <div class="mb-8">
            <!-- Hero Background -->
            <!-- A bustling port terminal with maritime professionals gathering near modern shipping containers -->
            <div class="relative bg-gradient-to-r from-navy-800 to-ocean-700 rounded-2xl overflow-hidden" 
                 style="background-image: url('https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&h=600'); background-size: cover; background-position: center; background-blend-mode: multiply;">
                <div class="absolute inset-0 bg-navy-800 bg-opacity-60"></div>
                <div class="relative px-8 py-12 text-center">
                    <h1 class="text-4xl md:text-5xl font-bold text-white mb-4">
                        Who's There?
                    </h1>
                    <p class="text-xl text-slate-200 mb-8 max-w-2xl mx-auto">
                        Discover maritime professionals around you. Connect with officers, crew, and port community wherever you are.
                    </p>
                    
                    <!-- @COMPONENT: LocationDetector [location-based discovery] -->
                    <div class="bg-white bg-opacity-10 backdrop-blur-md rounded-2xl p-6 max-w-md mx-auto">
                        <div class="flex items-center justify-center space-x-2 mb-4">
                            <i class="fas fa-location-dot text-coral-400 text-lg"></i>
                            <span class="text-white font-medium">Dubai Airport Terminal 3</span>
                        </div>
                        <div class="grid grid-cols-2 gap-4">
                            <button class="bg-coral-500 hover:bg-coral-600 text-white font-medium py-3 px-4 rounded-xl transition-colors">
                                <i class="fas fa-users-gear mb-1 block"></i>
                                Officers Nearby
                            </button>
                            <button class="bg-ocean-500 hover:bg-ocean-600 text-white font-medium py-3 px-4 rounded-xl transition-colors">
                                <i class="fas fa-ship mb-1 block"></i>
                                Crew Nearby
                            </button>
                        </div>
                    </div>
                    <!-- @END_COMPONENT: LocationDetector -->
                </div>
            </div>
        </div>
        <!-- @END_COMPONENT: HeroSection -->

        <!-- @COMPONENT: DiscoveryCards [discovery results grid] -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            
            <!-- @COMPONENT: NearbyOfficers [left column - nearby maritime professionals] -->
            <div class="lg:col-span-2">
                <div class="flex items-center justify-between mb-6">
                    <h2 class="text-2xl font-bold text-navy-800">
                        <i class="fas fa-users-gear text-coral-500 mr-3"></i>
                        Officers Nearby
                    </h2>
                    <button class="text-ocean-600 hover:text-ocean-700 font-medium">
                        View All
                    </button>
                </div>

                <!-- Officer Cards -->
                <div class="space-y-4">
                    <!-- @MAP: nearbyOfficers.map(officer => ( -->
                    
                    <!-- Officer 1 -->
                    <div class="bg-white rounded-xl shadow-md hover:shadow-lg transition-shadow p-6 border border-slate-200">
                        <div class="flex items-start space-x-4">
                            <div class="w-12 h-12 bg-gradient-to-br from-navy-600 to-navy-700 rounded-full flex items-center justify-center">
                                <span class="text-white font-bold">PG</span>
                            </div>
                            <div class="flex-1">
                                <div class="flex items-center justify-between mb-2">
                                    <h3 class="font-semibold text-navy-800" data-bind="officer.name">Piyush Gupta</h3>
                                    <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">Online</span>
                                </div>
                                <p class="text-ocean-600 font-medium text-sm mb-1" data-bind="officer.rank">Chief Engineer</p>
                                <p class="text-slate-600 text-sm mb-3" data-bind="officer.experience">21+ years experience</p>
                                <div class="flex items-center text-sm text-slate-500 mb-3">
                                    <i class="fas fa-location-dot text-coral-400 mr-1"></i>
                                    <span data-bind="officer.location">Terminal 3, Gate A15</span>
                                    <span class="mx-2">•</span>
                                    <span data-bind="officer.distance">150m away</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="text-xs bg-slate-100 text-slate-700 px-2 py-1 rounded">MAN B&W Expert</span>
                                    <span class="text-xs bg-slate-100 text-slate-700 px-2 py-1 rounded">QAAQ Contributor</span>
                                </div>
                                <div class="flex items-center space-x-3 mt-4">
                                    <button class="bg-coral-500 hover:bg-coral-600 text-white text-sm font-medium px-4 py-2 rounded-lg transition-colors">
                                        <i class="fab fa-whatsapp mr-1"></i>
                                        Connect
                                    </button>
                                    <button class="text-ocean-600 hover:text-ocean-700 text-sm font-medium">
                                        View Profile
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Officer 2 -->
                    <div class="bg-white rounded-xl shadow-md hover:shadow-lg transition-shadow p-6 border border-slate-200">
                        <div class="flex items-start space-x-4">
                            <div class="w-12 h-12 bg-gradient-to-br from-ocean-600 to-ocean-700 rounded-full flex items-center justify-center">
                                <span class="text-white font-bold">AR</span>
                            </div>
                            <div class="flex-1">
                                <div class="flex items-center justify-between mb-2">
                                    <h3 class="font-semibold text-navy-800">Ashok Raja</h3>
                                    <span class="text-xs bg-yellow-100 text-yellow-700 px-2 py-1 rounded-full">Away</span>
                                </div>
                                <p class="text-ocean-600 font-medium text-sm mb-1">Technical Superintendent</p>
                                <p class="text-slate-600 text-sm mb-3">15+ years experience</p>
                                <div class="flex items-center text-sm text-slate-500 mb-3">
                                    <i class="fas fa-location-dot text-coral-400 mr-1"></i>
                                    <span>Terminal 3, Food Court</span>
                                    <span class="mx-2">•</span>
                                    <span>300m away</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="text-xs bg-slate-100 text-slate-700 px-2 py-1 rounded">Tanker Specialist</span>
                                    <span class="text-xs bg-slate-100 text-slate-700 px-2 py-1 rounded">Seven Islands</span>
                                </div>
                                <div class="flex items-center space-x-3 mt-4">
                                    <button class="bg-coral-500 hover:bg-coral-600 text-white text-sm font-medium px-4 py-2 rounded-lg transition-colors">
                                        <i class="fab fa-whatsapp mr-1"></i>
                                        Connect
                                    </button>
                                    <button class="text-ocean-600 hover:text-ocean-700 text-sm font-medium">
                                        View Profile
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Officer 3 -->
                    <div class="bg-white rounded-xl shadow-md hover:shadow-lg transition-shadow p-6 border border-slate-200">
                        <div class="flex items-start space-x-4">
                            <div class="w-12 h-12 bg-gradient-to-br from-coral-500 to-coral-600 rounded-full flex items-center justify-center">
                                <span class="text-white font-bold">CS</span>
                            </div>
                            <div class="flex-1">
                                <div class="flex items-center justify-between mb-2">
                                    <h3 class="font-semibold text-navy-800">Carsten Sommerhage</h3>
                                    <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">Online</span>
                                </div>
                                <p class="text-ocean-600 font-medium text-sm mb-1">Managing Director</p>
                                <p class="text-slate-600 text-sm mb-3">20+ years experience</p>
                                <div class="flex items-center text-sm text-slate-500 mb-3">
                                    <i class="fas fa-location-dot text-coral-400 mr-1"></i>
                                    <span>Terminal 3, Business Lounge</span>
                                    <span class="mx-2">•</span>
                                    <span>500m away</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <span class="text-xs bg-slate-100 text-slate-700 px-2 py-1 rounded">Nordic Hamburg</span>
                                    <span class="text-xs bg-slate-100 text-slate-700 px-2 py-1 rounded">Container Ships</span>
                                </div>
                                <div class="flex items-center space-x-3 mt-4">
                                    <button class="bg-coral-500 hover:bg-coral-600 text-white text-sm font-medium px-4 py-2 rounded-lg transition-colors">
                                        <i class="fab fa-whatsapp mr-1"></i>
                                        Connect
                                    </button>
                                    <button class="text-ocean-600 hover:text-ocean-700 text-sm font-medium">
                                        View Profile
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- @END_MAP )) -->
                </div>
            </div>
            <!-- @END_COMPONENT: NearbyOfficers -->

            <!-- @COMPONENT: LocationGroups [right column - location-based groups and QAAQ integration] -->
            <div class="space-y-8">
                
                <!-- QAAQ AI Question Bar -->
                <div class="bg-white rounded-xl shadow-md p-6 border border-slate-200">
                    <div class="flex items-center space-x-3 mb-4">
                        <div class="w-8 h-8 bg-gradient-to-br from-navy-700 to-ocean-600 rounded-lg flex items-center justify-center">
                            <i class="fas fa-robot text-white text-sm"></i>
                        </div>
                        <h3 class="text-lg font-semibold text-navy-800">QAAQ AI Assistant</h3>
                    </div>
                    <div class="relative">
                        <input type="text" 
                               placeholder="Ask about marine equipment, systems, or local services..."
                               class="w-full px-4 py-3 pr-12 border border-slate-300 rounded-lg focus:ring-2 focus:ring-ocean-500 focus:border-ocean-500 text-sm">
                        <button class="absolute right-3 top-1/2 transform -translate-y-1/2 text-ocean-600 hover:text-ocean-700">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                    <div class="mt-3 text-xs text-slate-500">
                        Auto-categorized to: Dubai Airport > Terminal 3 > Services
                    </div>
                </div>

                <!-- Location Groups -->
                <div class="bg-white rounded-xl shadow-md p-6 border border-slate-200">
                    <h3 class="text-lg font-semibold text-navy-800 mb-4">
                        <i class="fas fa-users text-coral-500 mr-2"></i>
                        Local Groups
                    </h3>
                    
                    <div class="space-y-4">
                        <div class="border border-slate-200 rounded-lg p-4 hover:bg-slate-50 cursor-pointer">
                            <div class="flex items-start justify-between">
                                <div>
                                    <h4 class="font-medium text-navy-800">Dubai Airport Crew Lounge</h4>
                                    <p class="text-sm text-slate-600 mt-1">Transit crew meeting point</p>
                                    <div class="flex items-center space-x-3 mt-2">
                                        <span class="text-xs text-slate-500">
                                            <i class="fas fa-users mr-1"></i>23 members
                                        </span>
                                        <span class="text-xs text-slate-500">
                                            <i class="fas fa-comment mr-1"></i>5 active
                                        </span>
                                    </div>
                                </div>
                                <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">Active</span>
                            </div>
                        </div>

                        <div class="border border-slate-200 rounded-lg p-4 hover:bg-slate-50 cursor-pointer">
                            <div class="flex items-start justify-between">
                                <div>
                                    <h4 class="font-medium text-navy-800">Terminal 3 Food Courts</h4>
                                    <p class="text-sm text-slate-600 mt-1">Best dining recommendations</p>
                                    <div class="flex items-center space-x-3 mt-2">
                                        <span class="text-xs text-slate-500">
                                            <i class="fas fa-users mr-1"></i>156 members
                                        </span>
                                        <span class="text-xs text-slate-500">
                                            <i class="fas fa-comment mr-1"></i>12 active
                                        </span>
                                    </div>
                                </div>
                                <span class="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded-full">Popular</span>
                            </div>
                        </div>

                        <div class="border border-slate-200 rounded-lg p-4 hover:bg-slate-50 cursor-pointer">
                            <div class="flex items-start justify-between">
                                <div>
                                    <h4 class="font-medium text-navy-800">Dubai Taxi Share</h4>
                                    <p class="text-sm text-slate-600 mt-1">Share rides to/from ports</p>
                                    <div class="flex items-center space-x-3 mt-2">
                                        <span class="text-xs text-slate-500">
                                            <i class="fas fa-users mr-1"></i>89 members
                                        </span>
                                        <span class="text-xs text-slate-500">
                                            <i class="fas fa-comment mr-1"></i>3 active
                                        </span>
                                    </div>
                                </div>
                                <span class="text-xs bg-yellow-100 text-yellow-700 px-2 py-1 rounded-full">New</span>
                            </div>
                        </div>
                    </div>

                    <button class="w-full mt-4 text-ocean-600 hover:text-ocean-700 font-medium text-sm border border-ocean-200 hover:border-ocean-300 rounded-lg py-2 transition-colors">
                        <i class="fas fa-plus mr-1"></i>
                        Create New Group
                    </button>
                </div>

                <!-- Recent Community Feedback -->
                <div class="bg-white rounded-xl shadow-md p-6 border border-slate-200">
                    <h3 class="text-lg font-semibold text-navy-800 mb-4">
                        <i class="fas fa-comments text-coral-500 mr-2"></i>
                        Recent Feedback
                    </h3>
                    
                    <div class="space-y-4">
                        <div class="border-l-4 border-ocean-500 pl-4">
                            <p class="text-sm text-slate-700">"Great coffee shop near Gate A12 - perfect for long layovers!"</p>
                            <div class="flex items-center space-x-2 mt-2">
                                <div class="w-6 h-6 bg-gray-300 rounded-full"></div>
                                <span class="text-xs text-slate-500">Anonymous • 2h ago</span>
                                <span class="text-xs text-coral-500">
                                    <i class="fas fa-thumbs-up mr-1"></i>12
                                </span>
                            </div>
                        </div>

                        <div class="border-l-4 border-green-500 pl-4">
                            <p class="text-sm text-slate-700">"WiFi password for Business Lounge is 'Dubai2024' - still working!"</p>
                            <div class="flex items-center space-x-2 mt-2">
                                <div class="w-6 h-6 bg-gradient-to-br from-navy-600 to-navy-700 rounded-full flex items-center justify-center">
                                    <span class="text-white text-xs font-bold">CS</span>
                                </div>
                                <span class="text-xs text-slate-500">C. Sommerhage • 4h ago</span>
                                <span class="text-xs text-coral-500">
                                    <i class="fas fa-thumbs-up mr-1"></i>8
                                </span>
                            </div>
                        </div>
                    </div>

                    <button class="w-full mt-4 text-ocean-600 hover:text-ocean-700 font-medium text-sm border border-ocean-200 hover:border-ocean-300 rounded-lg py-2 transition-colors">
                        <i class="fas fa-plus mr-1"></i>
                        Add Feedback
                    </button>
                </div>
            </div>
            <!-- @END_COMPONENT: LocationGroups -->
        </div>
        <!-- @END_COMPONENT: DiscoveryCards -->

        <!-- @COMPONENT: QuickActions [floating action buttons] -->
        <div class="fixed bottom-6 right-6 space-y-3">
            <button class="w-14 h-14 bg-coral-500 hover:bg-coral-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all flex items-center justify-center">
                <i class="fab fa-whatsapp text-xl"></i>
            </button>
            <button class="w-14 h-14 bg-ocean-600 hover:bg-ocean-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all flex items-center justify-center">
                <i class="fas fa-question text-xl"></i>
            </button>
        </div>
        <!-- @END_COMPONENT: QuickActions -->

    </main>
    <!-- @END_COMPONENT: MainContent -->

    <!-- @COMPONENT: BottomNavigation [mobile bottom navigation] -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-slate-200 md:hidden">
        <div class="grid grid-cols-4 h-16">
            <button class="flex flex-col items-center justify-center space-y-1 text-coral-500">
                <i class="fas fa-compass text-lg"></i>
                <span class="text-xs font-medium">Discover</span>
            </button>
            <button class="flex flex-col items-center justify-center space-y-1 text-slate-400 hover:text-slate-600">
                <i class="fas fa-users text-lg"></i>
                <span class="text-xs">Groups</span>
            </button>
            <button class="flex flex-col items-center justify-center space-y-1 text-slate-400 hover:text-slate-600">
                <i class="fas fa-robot text-lg"></i>
                <span class="text-xs">QAAQ</span>
            </button>
            <button class="flex flex-col items-center justify-center space-y-1 text-slate-400 hover:text-slate-600">
                <i class="fas fa-user text-lg"></i>
                <span class="text-xs">Profile</span>
            </button>
        </div>
    </nav>
    <!-- @END_COMPONENT: BottomNavigation -->

    <script>
        // TODO: Implement location detection and real-time updates
        // TODO: Add WebSocket connection for live officer status
        // TODO: Implement QAAQ AI integration
        // TODO: Add WhatsApp deep linking for connections
        
        (function() {
            // Initialize location detection
            // @FUNCTIONALITY: Should implement geolocation API for precise location-based discovery
            
            // Mock location update every 30 seconds
            setInterval(() => {
                // TODO: Update nearby officers based on location
                console.log('Updating nearby officers...');
            }, 30000);

            // QAAQ integration
            const qaaqInput = document.querySelector('input[placeholder*="Ask about"]');
            if (qaaqInput) {
                qaaqInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        // TODO: Send question to QAAQ API
                        console.log('Sending question to QAAQ:', e.target.value);
                    }
                });
            }

            // Connect button handlers
            document.querySelectorAll('button:contains("Connect")').forEach(button => {
                button.addEventListener('click', (e) => {
                    // TODO: Open WhatsApp chat or show connection modal
                    console.log('Initiating connection...');
                });
            });
        })();
    </script>
</body>
<!-- @END_COMPONENT: MainApp -->
</html>