<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Koi Maritime Network - Connect with Maritime Professionals</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'navy': '#1e3a8a',
                        'ocean-teal': '#0d9488',
                        'light-teal': '#5eead4',
                        'maritime-gray': '#64748b'
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <style>
        .wave-pattern {
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%235eead4' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
        
        .chat-bubble {
            animation: slideUp 0.3s ease-out;
        }
        
        @keyframes slideUp {
            from { transform: translateY(20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        
        .tab-indicator {
            transition: transform 0.3s ease;
        }
    </style>
</head>
<body class="bg-gray-50 font-sans">
    <!-- Navigation Header -->
    <header class="bg-navy text-white sticky top-0 z-50 shadow-lg">
        <div class="max-w-md mx-auto px-4 py-3 flex items-center justify-between">
            <div class="flex items-center space-x-2">
                <div class="w-8 h-8 bg-ocean-teal rounded-full flex items-center justify-center">
                    <i class="fas fa-anchor text-white text-sm"></i>
                </div>
                <h1 class="text-lg font-bold">Koi Maritime</h1>
            </div>
            <div class="flex items-center space-x-3">
                <button class="relative">
                    <i class="fas fa-bell text-xl"></i>
                    <span class="absolute -top-1 -right-1 bg-red-500 text-xs rounded-full w-4 h-4 flex items-center justify-center">3</span>
                </button>
                <button id="profileBtn">
                    <!-- Profile photo of a maritime professional in uniform -->
                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&h=100" 
                         alt="Profile" class="w-8 h-8 rounded-full object-cover border-2 border-light-teal">
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content Container -->
    <div class="max-w-md mx-auto bg-white min-h-screen">
        
        <!-- Welcome Section -->
        <div class="bg-gradient-to-br from-navy to-ocean-teal text-white p-6 wave-pattern">
            <div class="text-center">
                <h2 class="text-2xl font-bold mb-2">Who's There?</h2>
                <p class="text-light-teal text-sm mb-4">Discover maritime professionals around you</p>
                <div class="flex items-center justify-center space-x-4 text-xs">
                    <div class="flex items-center">
                        <i class="fas fa-users mr-1"></i>
                        <span>1,203 Engineers</span>
                    </div>
                    <div class="flex items-center">
                        <i class="fas fa-ship mr-1"></i>
                        <span>847 Active</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Location Permission Banner -->
        <div id="locationBanner" class="bg-yellow-50 border-l-4 border-yellow-400 p-4 m-4 rounded-lg">
            <div class="flex items-start">
                <i class="fas fa-map-marker-alt text-yellow-600 mt-1 mr-3"></i>
                <div class="flex-1 text-sm">
                    <p class="font-medium text-yellow-800 mb-1">Enable Location Services</p>
                    <p class="text-yellow-700 mb-2">Find maritime professionals nearby and connect with your crew.</p>
                    <div class="flex space-x-2">
                        <button id="enableLocation" class="bg-yellow-600 text-white px-3 py-1 rounded text-xs hover:bg-yellow-700">
                            Enable Location
                        </button>
                        <button id="dismissBanner" class="text-yellow-700 px-3 py-1 rounded text-xs hover:bg-yellow-100">
                            Maybe Later
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tab Navigation -->
        <div class="sticky top-16 bg-white border-b border-gray-200 z-40">
            <div class="flex relative">
                <button class="tab-btn flex-1 py-3 px-4 text-center font-medium transition-colors active" data-tab="sailors">
                    <i class="fas fa-user-tie mr-2"></i>Sailors Nearby
                </button>
                <button class="tab-btn flex-1 py-3 px-4 text-center font-medium transition-colors" data-tab="crew">
                    <i class="fas fa-users mr-2"></i>Crew Nearby
                </button>
                <div class="tab-indicator absolute bottom-0 left-0 w-1/2 h-0.5 bg-ocean-teal transform transition-transform"></div>
            </div>
        </div>

        <!-- Sailors Nearby Tab Content -->
        <div id="sailors-tab" class="tab-content p-4">
            <!-- Filter Bar -->
            <div class="flex items-center space-x-2 mb-4">
                <div class="flex-1">
                    <select class="w-full bg-gray-100 border border-gray-300 rounded-lg px-3 py-2 text-sm">
                        <option>All Positions</option>
                        <option>Chief Engineer</option>
                        <option>2nd Engineer</option>
                        <option>3rd Engineer</option>
                        <option>Chief Officer</option>
                        <option>Captain</option>
                    </select>
                </div>
                <button class="bg-ocean-teal text-white px-4 py-2 rounded-lg text-sm">
                    <i class="fas fa-sliders-h"></i>
                </button>
            </div>

            <!-- Sailor Cards -->
            <div class="space-y-4">
                <!-- Sailor Card 1 -->
                <div class="bg-white border border-gray-200 rounded-xl p-4 shadow-sm">
                    <div class="flex items-center space-x-3 mb-3">
                        <!-- Maritime professional in officer uniform -->
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&h=100" 
                             alt="Chief Engineer" class="w-12 h-12 rounded-full object-cover">
                        <div class="flex-1">
                            <h3 class="font-semibold text-gray-900">Rajesh Kumar</h3>
                            <p class="text-ocean-teal text-sm font-medium">Chief Engineer</p>
                            <p class="text-gray-500 text-xs">Engine Department • 0.8 km away</p>
                        </div>
                        <div class="text-right">
                            <div class="flex items-center text-green-600 text-xs mb-1">
                                <i class="fas fa-circle text-xs mr-1"></i>Online
                            </div>
                            <button class="bg-ocean-teal text-white px-3 py-1 rounded-full text-xs hover:bg-ocean-teal/90">
                                Connect
                            </button>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4 text-xs text-gray-600">
                        <span><i class="fas fa-ship mr-1"></i>MT Solar Claire</span>
                        <span><i class="fas fa-calendar mr-1"></i>15+ years exp</span>
                        <span><i class="fas fa-certificate mr-1"></i>MEO Class I</span>
                    </div>
                </div>

                <!-- Sailor Card 2 -->
                <div class="bg-white border border-gray-200 rounded-xl p-4 shadow-sm">
                    <div class="flex items-center space-x-3 mb-3">
                        <!-- Maritime officer on ship deck -->
                        <img src="https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&h=100" 
                             alt="Second Officer" class="w-12 h-12 rounded-full object-cover">
                        <div class="flex-1">
                            <h3 class="font-semibold text-gray-900">Maria Santos</h3>
                            <p class="text-ocean-teal text-sm font-medium">Second Officer</p>
                            <p class="text-gray-500 text-xs">Deck Department • 1.2 km away</p>
                        </div>
                        <div class="text-right">
                            <div class="flex items-center text-yellow-600 text-xs mb-1">
                                <i class="fas fa-circle text-xs mr-1"></i>Away
                            </div>
                            <button class="bg-ocean-teal text-white px-3 py-1 rounded-full text-xs hover:bg-ocean-teal/90">
                                Connect
                            </button>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4 text-xs text-gray-600">
                        <span><i class="fas fa-ship mr-1"></i>MT Nordic Jupiter</span>
                        <span><i class="fas fa-calendar mr-1"></i>8 years exp</span>
                        <span><i class="fas fa-certificate mr-1"></i>OOW Unlimited</span>
                    </div>
                </div>

                <!-- Sailor Card 3 -->
                <div class="bg-white border border-gray-200 rounded-xl p-4 shadow-sm">
                    <div class="flex items-center space-x-3 mb-3">
                        <!-- Senior maritime engineer -->
                        <img src="https://images.unsplash.com/photo-1542909168-82c3e7fdca5c?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&h=100" 
                             alt="Third Engineer" class="w-12 h-12 rounded-full object-cover">
                        <div class="flex-1">
                            <h3 class="font-semibold text-gray-900">Ahmed Hassan</h3>
                            <p class="text-ocean-teal text-sm font-medium">Third Engineer</p>
                            <p class="text-gray-500 text-xs">Engine Department • 2.1 km away</p>
                        </div>
                        <div class="text-right">
                            <div class="flex items-center text-green-600 text-xs mb-1">
                                <i class="fas fa-circle text-xs mr-1"></i>Online
                            </div>
                            <button class="bg-ocean-teal text-white px-3 py-1 rounded-full text-xs hover:bg-ocean-teal/90">
                                Connect
                            </button>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4 text-xs text-gray-600">
                        <span><i class="fas fa-ship mr-1"></i>MV Atlantic Gemini</span>
                        <span><i class="fas fa-calendar mr-1"></i>5 years exp</span>
                        <span><i class="fas fa-certificate mr-1"></i>MEO Class III</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Crew Nearby Tab Content -->
        <div id="crew-tab" class="tab-content p-4 hidden">
            <!-- Crew Team Cards -->
            <div class="space-y-4">
                <!-- Crew Team 1 -->
                <div class="bg-white border border-gray-200 rounded-xl p-4 shadow-sm">
                    <div class="flex items-center justify-between mb-3">
                        <div>
                            <h3 class="font-semibold text-gray-900">MT Sentinel Crew</h3>
                            <p class="text-ocean-teal text-sm">Oil/Chemical Tanker</p>
                            <p class="text-gray-500 text-xs">0.5 km away • Mumbai Port</p>
                        </div>
                        <div class="text-right">
                            <div class="flex -space-x-2 mb-2">
                                <!-- Diverse crew members on ship -->
                                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=40&h=40" 
                                     alt="Crew member" class="w-8 h-8 rounded-full border-2 border-white object-cover">
                                <img src="https://pixabay.com/get/g0e419dfa141de7c963c9f64093b87dc471eb392d33499ca942cb61191b6d00f20ea862bf3046bac9a7ca6b3c4d3661a5c294d4873f20c9cf08139005ba4f3e74_1280.jpg" 
                                     alt="Crew member" class="w-8 h-8 rounded-full border-2 border-white object-cover">
                                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=40&h=40" 
                                     alt="Crew member" class="w-8 h-8 rounded-full border-2 border-white object-cover">
                                <div class="w-8 h-8 rounded-full border-2 border-white bg-gray-300 flex items-center justify-center text-xs">+3</div>
                            </div>
                            <button class="bg-navy text-white px-3 py-1 rounded-full text-xs hover:bg-navy/90">
                                Join Chat
                            </button>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4 text-xs text-gray-600 mb-2">
                        <span><i class="fas fa-users mr-1"></i>18 members</span>
                        <span><i class="fas fa-comments mr-1"></i>Active chat</span>
                        <span><i class="fas fa-anchor mr-1"></i>At berth</span>
                    </div>
                    <div class="bg-gray-50 rounded-lg p-2 text-xs">
                        <p class="text-gray-600">"Anyone free for coffee at the terminal cafe? - Captain Mike"</p>
                        <p class="text-gray-400 mt-1">2 minutes ago</p>
                    </div>
                </div>

                <!-- Crew Team 2 -->
                <div class="bg-white border border-gray-200 rounded-xl p-4 shadow-sm">
                    <div class="flex items-center justify-between mb-3">
                        <div>
                            <h3 class="font-semibold text-gray-900">MV Cargo Express Crew</h3>
                            <p class="text-ocean-teal text-sm">Container Ship</p>
                            <p class="text-gray-500 text-xs">1.8 km away • Loading berth</p>
                        </div>
                        <div class="text-right">
                            <div class="flex -space-x-2 mb-2">
                                <!-- Maritime crew members in safety gear -->
                                <img src="https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&auto=format&fit=crop&w=40&h=40" 
                                     alt="Crew member" class="w-8 h-8 rounded-full border-2 border-white object-cover">
                                <img src="https://images.unsplash.com/photo-1542909168-82c3e7fdca5c?ixlib=rb-4.0.3&auto=format&fit=crop&w=40&h=40" 
                                     alt="Crew member" class="w-8 h-8 rounded-full border-2 border-white object-cover">
                                <div class="w-8 h-8 rounded-full border-2 border-white bg-gray-300 flex items-center justify-center text-xs">+12</div>
                            </div>
                            <button class="bg-navy text-white px-3 py-1 rounded-full text-xs hover:bg-navy/90">
                                Join Chat
                            </button>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4 text-xs text-gray-600 mb-2">
                        <span><i class="fas fa-users mr-1"></i>14 members</span>
                        <span><i class="fas fa-comments mr-1"></i>Active chat</span>
                        <span><i class="fas fa-ship mr-1"></i>Loading cargo</span>
                    </div>
                    <div class="bg-gray-50 rounded-lg p-2 text-xs">
                        <p class="text-gray-600">"Shore leave approved for tomorrow. Meet at the pier at 1400 hrs - Chief Officer"</p>
                        <p class="text-gray-400 mt-1">15 minutes ago</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- WhatsApp Integration Section -->
        <div class="bg-green-50 border border-green-200 rounded-xl p-4 m-4">
            <div class="flex items-center space-x-3 mb-3">
                <div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center">
                    <i class="fab fa-whatsapp text-white text-lg"></i>
                </div>
                <div>
                    <h3 class="font-semibold text-gray-900">WhatsApp Bot Integration</h3>
                    <p class="text-gray-600 text-sm">Get maritime Q&A on WhatsApp</p>
                </div>
            </div>
            <p class="text-sm text-gray-700 mb-3">Connect with our maritime expertise bot for instant technical support and questions.</p>
            <div class="flex space-x-2">
                <button class="bg-green-500 text-white px-4 py-2 rounded-lg text-sm hover:bg-green-600 flex-1">
                    <i class="fab fa-whatsapp mr-2"></i>Connect WhatsApp
                </button>
                <button class="border border-green-500 text-green-600 px-4 py-2 rounded-lg text-sm hover:bg-green-50">
                    Learn More
                </button>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="p-4">
            <h3 class="font-semibold text-gray-900 mb-3">Quick Actions</h3>
            <div class="grid grid-cols-2 gap-3">
                <button class="bg-white border border-gray-200 rounded-xl p-4 text-center hover:shadow-md transition-shadow">
                    <i class="fas fa-file-alt text-ocean-teal text-xl mb-2"></i>
                    <p class="text-sm font-medium">My Documents</p>
                </button>
                <button class="bg-white border border-gray-200 rounded-xl p-4 text-center hover:shadow-md transition-shadow">
                    <i class="fas fa-user-edit text-ocean-teal text-xl mb-2"></i>
                    <p class="text-sm font-medium">Edit Profile</p>
                </button>
                <button class="bg-white border border-gray-200 rounded-xl p-4 text-center hover:shadow-md transition-shadow">
                    <i class="fas fa-shield-alt text-ocean-teal text-xl mb-2"></i>
                    <p class="text-sm font-medium">Privacy Settings</p>
                </button>
                <button class="bg-white border border-gray-200 rounded-xl p-4 text-center hover:shadow-md transition-shadow">
                    <i class="fas fa-question-circle text-ocean-teal text-xl mb-2"></i>
                    <p class="text-sm font-medium">Ask QAAQ AI</p>
                </button>
            </div>
        </div>
    </div>

    <!-- Profile Modal -->
    <div id="profileModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
        <div class="flex items-end justify-center min-h-screen">
            <div class="bg-white w-full max-w-md rounded-t-2xl max-h-[90vh] overflow-y-auto">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-xl font-bold text-gray-900">Profile & Settings</h2>
                        <button id="closeProfile" class="text-gray-500 hover:text-gray-700">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>

                    <!-- Profile Info -->
                    <div class="text-center mb-6">
                        <!-- Maritime professional profile photo -->
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=120&h=120" 
                             alt="Profile" class="w-20 h-20 rounded-full mx-auto mb-3 object-cover">
                        <h3 class="font-bold text-lg text-gray-900">Piyush Gupta</h3>
                        <p class="text-ocean-teal font-medium">Chief Engineer</p>
                        <p class="text-gray-500 text-sm">Mumbai, India • MEO Class I</p>
                    </div>

                    <!-- Menu Options -->
                    <div class="space-y-2">
                        <button class="w-full flex items-center space-x-3 p-3 text-left hover:bg-gray-50 rounded-lg">
                            <i class="fas fa-user text-ocean-teal w-5"></i>
                            <span class="font-medium">Edit Profile</span>
                        </button>
                        <button class="w-full flex items-center space-x-3 p-3 text-left hover:bg-gray-50 rounded-lg">
                            <i class="fas fa-folder text-ocean-teal w-5"></i>
                            <span class="font-medium">Marine Documents</span>
                        </button>
                        <button class="w-full flex items-center space-x-3 p-3 text-left hover:bg-gray-50 rounded-lg">
                            <i class="fas fa-file-alt text-ocean-teal w-5"></i>
                            <span class="font-medium">Resume Builder</span>
                        </button>
                        <button class="w-full flex items-center space-x-3 p-3 text-left hover:bg-gray-50 rounded-lg">
                            <i class="fas fa-eye text-ocean-teal w-5"></i>
                            <span class="font-medium">Privacy Settings</span>
                        </button>
                        <button class="w-full flex items-center space-x-3 p-3 text-left hover:bg-gray-50 rounded-lg">
                            <i class="fas fa-download text-ocean-teal w-5"></i>
                            <span class="font-medium">Export Data</span>
                        </button>
                        
                        <hr class="my-4">
                        
                        <button class="w-full flex items-center space-x-3 p-3 text-left hover:bg-gray-50 rounded-lg">
                            <i class="fas fa-shield-alt text-ocean-teal w-5"></i>
                            <span class="font-medium">Privacy Policy</span>
                        </button>
                        <button class="w-full flex items-center space-x-3 p-3 text-left hover:bg-gray-50 rounded-lg">
                            <i class="fas fa-file-contract text-ocean-teal w-5"></i>
                            <span class="font-medium">Terms of Service</span>
                        </button>
                        <button class="w-full flex items-center space-x-3 p-3 text-left hover:bg-gray-50 rounded-lg">
                            <i class="fas fa-headset text-ocean-teal w-5"></i>
                            <span class="font-medium">Contact Support</span>
                        </button>
                        
                        <hr class="my-4">
                        
                        <button class="w-full flex items-center space-x-3 p-3 text-left hover:bg-red-50 rounded-lg text-red-600">
                            <i class="fas fa-trash-alt w-5"></i>
                            <span class="font-medium">Delete Account</span>
                        </button>
                        <button class="w-full flex items-center space-x-3 p-3 text-left hover:bg-gray-50 rounded-lg">
                            <i class="fas fa-sign-out-alt text-ocean-teal w-5"></i>
                            <span class="font-medium">Sign Out</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Privacy Policy Modal -->
    <div id="privacyModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white w-full max-w-md rounded-2xl max-h-[90vh] overflow-hidden">
                <div class="p-6 border-b">
                    <div class="flex items-center justify-between">
                        <h2 class="text-xl font-bold text-gray-900">Privacy Policy</h2>
                        <button id="closePrivacy" class="text-gray-500 hover:text-gray-700">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>
                </div>
                <div class="p-6 overflow-y-auto max-h-[70vh]">
                    <div class="space-y-4 text-sm text-gray-700">
                        <div>
                            <h3 class="font-semibold text-gray-900 mb-2">Data Collection</h3>
                            <p>We collect only essential information needed to connect maritime professionals, including location data (with your consent), professional credentials, and communication preferences.</p>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-900 mb-2">Location Privacy</h3>
                            <p>Location data is used solely for proximity-based matching. You can disable location sharing at any time in settings. We never share exact coordinates with other users.</p>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-900 mb-2">Data Security</h3>
                            <p>All marine documents are encrypted end-to-end. Profile data is protected with industry-standard security measures. We comply with GDPR and CCPA regulations.</p>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-900 mb-2">Third-Party Integrations</h3>
                            <p>WhatsApp integration requires explicit consent. We only share necessary data to facilitate communication. You control all sharing permissions.</p>
                        </div>
                    </div>
                </div>
                <div class="p-6 border-t">
                    <button id="acceptPrivacy" class="w-full bg-ocean-teal text-white py-2 rounded-lg font-medium hover:bg-ocean-teal/90">
                        Accept & Continue
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Cookie Consent Banner -->
    <div id="cookieBanner" class="fixed bottom-0 left-0 right-0 bg-white border-t shadow-lg z-40 p-4">
        <div class="max-w-md mx-auto">
            <div class="text-sm text-gray-700 mb-3">
                <p class="font-medium mb-1">Cookie Consent</p>
                <p>We use cookies to improve your experience and analyze app usage. Essential cookies are required for core functionality.</p>
            </div>
            <div class="flex space-x-2">
                <button id="acceptCookies" class="bg-ocean-teal text-white px-4 py-2 rounded-lg text-sm hover:bg-ocean-teal/90 flex-1">
                    Accept All
                </button>
                <button id="manageCookies" class="border border-gray-300 text-gray-700 px-4 py-2 rounded-lg text-sm hover:bg-gray-50">
                    Manage
                </button>
                <button id="rejectCookies" class="text-gray-500 px-4 py-2 rounded-lg text-sm hover:bg-gray-50">
                    Reject
                </button>
            </div>
        </div>
    </div>

    <!-- Chat Interface (Hidden by default) -->
    <div id="chatInterface" class="fixed inset-0 bg-white z-50 hidden">
        <div class="flex flex-col h-full">
            <!-- Chat Header -->
            <div class="bg-navy text-white p-4 flex items-center space-x-3">
                <button id="backToMain" class="text-white">
                    <i class="fas fa-arrow-left text-lg"></i>
                </button>
                <div class="flex-1">
                    <h3 class="font-semibold">MT Sentinel Crew</h3>
                    <p class="text-light-teal text-sm">18 members • 5 online</p>
                </div>
                <button class="text-white">
                    <i class="fas fa-video text-lg"></i>
                </button>
            </div>
            
            <!-- Chat Messages -->
            <div class="flex-1 overflow-y-auto p-4 space-y-4">
                <div class="chat-bubble">
                    <div class="flex items-start space-x-2">
                        <!-- Ship captain profile -->
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=40&h=40" 
                             alt="Captain" class="w-8 h-8 rounded-full object-cover">
                        <div class="bg-gray-100 rounded-2xl px-4 py-2 max-w-xs">
                            <p class="text-sm font-medium text-ocean-teal mb-1">Captain Mike</p>
                            <p class="text-sm text-gray-800">Anyone free for coffee at the terminal cafe?</p>
                            <p class="text-xs text-gray-500 mt-1">2 min ago</p>
                        </div>
                    </div>
                </div>
                
                <div class="chat-bubble">
                    <div class="flex items-start space-x-2">
                        <!-- Female maritime officer -->
                        <img src="https://pixabay.com/get/g47b1af62812d6210509de498bd0f1d3939ac6fbface2bce7eca8c499c4378f48ab19d9eb47b6d26e6e18c27a046791d375e80344d63e2cffe5af5b2e81955971_1280.jpg" 
                             alt="Chief Engineer" class="w-8 h-8 rounded-full object-cover">
                        <div class="bg-gray-100 rounded-2xl px-4 py-2 max-w-xs">
                            <p class="text-sm font-medium text-ocean-teal mb-1">Sarah Chen</p>
                            <p class="text-sm text-gray-800">I'm in! Just finished the engine room inspection.</p>
                            <p class="text-xs text-gray-500 mt-1">1 min ago</p>
                        </div>
                    </div>
                </div>
                
                <div class="chat-bubble text-right">
                    <div class="bg-ocean-teal text-white rounded-2xl px-4 py-2 max-w-xs ml-auto">
                        <p class="text-sm">Count me in too! Meet at 1500?</p>
                        <p class="text-xs text-light-teal mt-1">Just now</p>
                    </div>
                </div>
            </div>
            
            <!-- Chat Input -->
            <div class="border-t p-4">
                <div class="flex items-center space-x-2">
                    <button class="text-ocean-teal">
                        <i class="fas fa-plus text-lg"></i>
                    </button>
                    <input type="text" placeholder="Type a message..." 
                           class="flex-1 border border-gray-300 rounded-full px-4 py-2 text-sm focus:outline-none focus:border-ocean-teal">
                    <button class="bg-ocean-teal text-white p-2 rounded-full">
                        <i class="fas fa-paper-plane text-sm"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Tab functionality
        (function() {
            const tabBtns = document.querySelectorAll('.tab-btn');
            const tabContents = document.querySelectorAll('.tab-content');
            const tabIndicator = document.querySelector('.tab-indicator');

            tabBtns.forEach((btn, index) => {
                btn.addEventListener('click', () => {
                    // Remove active class from all tabs
                    tabBtns.forEach(b => b.classList.remove('active', 'text-ocean-teal'));
                    tabContents.forEach(c => c.classList.add('hidden'));
                    
                    // Add active class to clicked tab
                    btn.classList.add('active', 'text-ocean-teal');
                    document.getElementById(btn.dataset.tab + '-tab').classList.remove('hidden');
                    
                    // Move indicator
                    tabIndicator.style.transform = `translateX(${index * 100}%)`;
                });
            });

            // Set initial active tab
            tabBtns[0].classList.add('text-ocean-teal');
        })();

        // Profile modal functionality
        (function() {
            const profileBtn = document.getElementById('profileBtn');
            const profileModal = document.getElementById('profileModal');
            const closeProfile = document.getElementById('closeProfile');

            profileBtn.addEventListener('click', () => {
                profileModal.classList.remove('hidden');
                document.body.style.overflow = 'hidden';
            });

            closeProfile.addEventListener('click', () => {
                profileModal.classList.add('hidden');
                document.body.style.overflow = '';
            });

            profileModal.addEventListener('click', (e) => {
                if (e.target === profileModal) {
                    profileModal.classList.add('hidden');
                    document.body.style.overflow = '';
                }
            });
        })();

        // Location permission handling
        (function() {
            const locationBanner = document.getElementById('locationBanner');
            const enableLocation = document.getElementById('enableLocation');
            const dismissBanner = document.getElementById('dismissBanner');

            enableLocation.addEventListener('click', async () => {
                try {
                    const position = await new Promise((resolve, reject) => {
                        navigator.geolocation.getCurrentPosition(resolve, reject);
                    });
                    
                    locationBanner.innerHTML = `
                        <div class="flex items-center">
                            <i class="fas fa-check-circle text-green-600 mr-3"></i>
                            <div class="text-sm">
                                <p class="font-medium text-green-800">Location enabled successfully!</p>
                                <p class="text-green-700">Now discovering maritime professionals nearby...</p>
                            </div>
                        </div>
                    `;
                    locationBanner.className = 'bg-green-50 border-l-4 border-green-400 p-4 m-4 rounded-lg';
                    
                    setTimeout(() => {
                        locationBanner.style.display = 'none';
                    }, 3000);
                } catch (error) {
                    // TODO: Handle location permission denied
                    console.log('Location permission denied');
                }
            });

            dismissBanner.addEventListener('click', () => {
                locationBanner.style.display = 'none';
            });
        })();

        // Cookie consent handling
        (function() {
            const cookieBanner = document.getElementById('cookieBanner');
            const acceptCookies = document.getElementById('acceptCookies');
            const rejectCookies = document.getElementById('rejectCookies');

            // Show banner after 2 seconds
            setTimeout(() => {
                cookieBanner.style.display = 'block';
            }, 2000);

            acceptCookies.addEventListener('click', () => {
                // TODO: Set cookie preferences
                cookieBanner.style.display = 'none';
            });

            rejectCookies.addEventListener('click', () => {
                // TODO: Set minimal cookie preferences
                cookieBanner.style.display = 'none';
            });
        })();

        // Chat interface functionality
        (function() {
            const chatBtns = document.querySelectorAll('[data-action="join-chat"]');
            const chatInterface = document.getElementById('chatInterface');
            const backToMain = document.getElementById('backToMain');

            // Add event listeners to "Join Chat" buttons
            document.addEventListener('click', (e) => {
                if (e.target.textContent.includes('Join Chat')) {
                    chatInterface.classList.remove('hidden');
                    document.body.style.overflow = 'hidden';
                }
            });

            backToMain.addEventListener('click', () => {
                chatInterface.classList.add('hidden');
                document.body.style.overflow = '';
            });
        })();

        // WhatsApp integration
        (function() {
            const whatsappBtn = document.querySelector('.bg-green-500');
            
            if (whatsappBtn && whatsappBtn.textContent.includes('Connect WhatsApp')) {
                whatsappBtn.addEventListener('click', () => {
                    // TODO: Implement WhatsApp bot integration
                    // This would typically open WhatsApp with pre-filled message
                    const phoneNumber = '+905363694997';
                    const message = 'Hi! I want to connect to Koi Maritime Network for maritime technical support.';
                    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;
                    window.open(whatsappUrl, '_blank');
                });
            }
        })();

        // Quick Actions functionality
        (function() {
            const quickActionBtns = document.querySelectorAll('[class*="grid-cols-2"] button');
            
            quickActionBtns.forEach(btn => {
                btn.addEventListener('click', () => {
                    const action = btn.textContent.trim();
                    // TODO: Implement navigation to respective features
                    console.log(`Navigate to: ${action}`);
                });
            });
        })();

        // Add smooth scrolling and touch interactions for better mobile experience
        document.addEventListener('DOMContentLoaded', () => {
            // Add touch-friendly hover effects for mobile
            const cards = document.querySelectorAll('.bg-white.border');
            cards.forEach(card => {
                card.addEventListener('touchstart', () => {
                    card.style.transform = 'scale(0.98)';
                });
                
                card.addEventListener('touchend', () => {
                    card.style.transform = 'scale(1)';
                });
            });
        });
    </script>
</body>
</html>