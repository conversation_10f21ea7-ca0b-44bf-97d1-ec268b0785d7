<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>1234 Koi Hai? - Maritime Community Platform</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'navy': '#1B365D',
                        'ocean-teal': '#2D7D85',
                        'light-blue': '#E8F4F8',
                        'dark-gray': '#2C3E50'
                    },
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                        'roboto': ['Roboto', 'sans-serif']
                    }
                }
            }
        };
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #1B365D 0%, #2D7D85 100%);
        }
        .card-shadow {
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        .card-hover {
            transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
        }
        .card-hover:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }
        .toggle-switch {
            width: 60px;
            height: 30px;
            background-color: #ccc;
            border-radius: 30px;
            position: relative;
            cursor: pointer;
            transition: background-color 0.3s;
        }
        .toggle-switch.active {
            background-color: #2D7D85;
        }
        .toggle-handle {
            width: 26px;
            height: 26px;
            background-color: white;
            border-radius: 50%;
            position: absolute;
            top: 2px;
            left: 2px;
            transition: transform 0.3s;
        }
        .toggle-switch.active .toggle-handle {
            transform: translateX(30px);
        }
        .media-upload-area {
            border: 2px dashed #2D7D85;
            border-radius: 12px;
            background: rgba(45, 125, 133, 0.05);
            transition: all 0.3s ease;
        }
        .media-upload-area:hover {
            background: rgba(45, 125, 133, 0.1);
            border-color: #1B365D;
        }
    </style>
</head>
<body class="bg-gray-50 font-roboto">
    <!-- Header with QAAQ AI Search -->
    <header class="gradient-bg text-white sticky top-0 z-50 shadow-lg">
        <div class="px-4 py-3">
            <!-- Top Bar with Logo and Profile -->
            <div class="flex items-center justify-between mb-3">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-white rounded-full flex items-center justify-center">
                        <i class="fas fa-anchor text-navy text-xl"></i>
                    </div>
                    <div>
                        <h1 class="font-inter font-bold text-xl">1234 Koi Hai?</h1>
                        <p class="text-xs opacity-90">Maritime Community</p>
                    </div>
                </div>
                <div class="flex items-center space-x-3">
                    <button class="relative">
                        <i class="fas fa-bell text-xl"></i>
                        <span class="absolute -top-1 -right-1 w-3 h-3 bg-red-500 rounded-full"></span>
                    </button>
                    <div class="w-8 h-8 bg-white rounded-full overflow-hidden">
                        <!-- Profile photo placeholder -->
                        <img data-mock="true" src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=32&h=32&fit=crop&crop=face" alt="Profile" class="w-full h-full object-cover">
                    </div>
                </div>
            </div>

            <!-- QAAQ AI Question Bar -->
            <div class="relative">
                <div class="bg-white rounded-full shadow-lg flex items-center px-4 py-3">
                    <i class="fas fa-robot text-ocean-teal text-lg mr-3"></i>
                    <input type="text" placeholder="Ask QAAQ AI about ports, services, or connect with nearby officers..." class="flex-1 text-gray-700 placeholder-gray-500 outline-none text-sm">
                    <button class="bg-ocean-teal text-white rounded-full w-8 h-8 flex items-center justify-center ml-2">
                        <i class="fas fa-paper-plane text-sm"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Discovery Tabs -->
        <div class="bg-white bg-opacity-10 px-4 py-2">
            <div class="flex space-x-1">
                <button class="flex-1 py-2 px-4 rounded-full bg-white bg-opacity-20 text-center text-sm font-medium transition-all duration-200 hover:bg-opacity-30">
                    <i class="fas fa-user-tie mr-2"></i>Officers Nearby
                </button>
                <button class="flex-1 py-2 px-4 rounded-full text-center text-sm font-medium transition-all duration-200 hover:bg-white hover:bg-opacity-20">
                    <i class="fas fa-users mr-2"></i>Crew Nearby
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="pb-20">
        <!-- Post Creation Area -->
        <div class="bg-white m-4 rounded-xl shadow-lg p-4">
            <!-- Anonymous Toggle -->
            <div class="flex items-center justify-between mb-4">
                <span class="text-gray-700 font-medium">Share your experience</span>
                <div class="flex items-center space-x-2">
                    <span class="text-sm text-gray-600">Anonymous</span>
                    <div class="toggle-switch" onclick="toggleAnonymous(this)">
                        <div class="toggle-handle"></div>
                    </div>
                    <span class="text-sm text-gray-600">Identified</span>
                </div>
            </div>

            <!-- Text Input -->
            <textarea placeholder="Share your experience about ports, services, or recommendations..." class="w-full p-3 border border-gray-200 rounded-lg resize-none h-20 focus:outline-none focus:border-ocean-teal" data-mock="true"></textarea>

            <!-- Media Upload Area -->
            <div class="media-upload-area p-6 mt-3 text-center cursor-pointer" onclick="handleMediaUpload()">
                <i class="fas fa-camera text-ocean-teal text-2xl mb-2"></i>
                <p class="text-gray-600 text-sm">Add photos or videos (up to 90 seconds)</p>
                <p class="text-xs text-gray-500 mt-1">Tap to capture or upload media</p>
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-between items-center mt-4">
                <div class="flex space-x-3">
                    <button class="text-ocean-teal hover:bg-light-blue p-2 rounded-lg transition-colors">
                        <i class="fas fa-map-marker-alt mr-1"></i>
                        <span class="text-sm">Add CPSS Location</span>
                    </button>
                </div>
                <button class="bg-ocean-teal text-white px-6 py-2 rounded-full font-medium hover:bg-opacity-90 transition-colors">
                    Share
                </button>
            </div>
        </div>

        <!-- Feed -->
        <div class="space-y-4 px-4">
            <!-- Post 1 - Photo Post -->
            <article class="bg-white rounded-xl shadow-lg card-hover overflow-hidden" data-mock="true">
                <div class="p-4">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center space-x-3">
                            <!-- Professional maritime photo showing officer -->
                            <img src="https://images.unsplash.com/photo-1582213782179-e0d53f98f2ca?w=40&h=40&fit=crop&crop=face" alt="Maritime Officer" class="w-10 h-10 rounded-full object-cover">
                            <div>
                                <h3 class="font-medium text-gray-900">Chief Engineer</h3>
                                <p class="text-xs text-gray-500">Hamburg Port • 2 hours ago</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-1 text-ocean-teal">
                            <i class="fas fa-check-circle text-sm"></i>
                            <span class="text-xs">Verified</span>
                        </div>
                    </div>
                    
                    <p class="text-gray-800 mb-3" data-mock="true">Great WiFi spots near Hamburg Port! These cafes have reliable internet for video calls home. Perfect for shore leave. 📶</p>
                    
                    <!-- Photo showing Hamburg port area with cafes -->
                    <img src="https://pixabay.com/get/ge29fdaaaa147559536bcec4a21c0e3b67dac8830dd7dad01074e2f6c19f4911ea98f440451571c26c12718b7007f573189c21ef04cdb2be10fbe0839946802ad_1280.jpg" alt="Hamburg Port Area Cafes" class="w-full h-48 object-cover rounded-lg mb-3">
                    
                    <div class="flex items-center justify-between">
                        <div class="flex space-x-4">
                            <button class="flex items-center space-x-1 text-gray-600 hover:text-ocean-teal transition-colors">
                                <i class="fas fa-thumbs-up"></i>
                                <span class="text-sm" data-mock="true">24</span>
                            </button>
                            <button class="flex items-center space-x-1 text-gray-600 hover:text-ocean-teal transition-colors">
                                <i class="fas fa-comment"></i>
                                <span class="text-sm" data-mock="true">7</span>
                            </button>
                            <button class="flex items-center space-x-1 text-gray-600 hover:text-ocean-teal transition-colors">
                                <i class="fas fa-share"></i>
                                <span class="text-sm">Share</span>
                            </button>
                        </div>
                        <span class="text-xs text-gray-500">#HamburgPort #WiFi</span>
                    </div>
                </div>
            </article>

            <!-- Post 2 - Video Post (Anonymous) -->
            <article class="bg-white rounded-xl shadow-lg card-hover overflow-hidden" data-mock="true">
                <div class="p-4">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                                <i class="fas fa-user-secret text-gray-600"></i>
                            </div>
                            <div>
                                <h3 class="font-medium text-gray-900">Anonymous Sailor</h3>
                                <p class="text-xs text-gray-500">Rotterdam Port • 5 hours ago</p>
                            </div>
                        </div>
                        <span class="text-xs text-red-500 bg-red-50 px-2 py-1 rounded-full">Warning</span>
                    </div>
                    
                    <p class="text-gray-800 mb-3" data-mock="true">Avoid this taxi service near Rotterdam port. Overcharging and taking longer routes. Video proof attached. 🚫🚕</p>
                    
                    <!-- Video placeholder showing port transportation -->
                    <div class="relative bg-gray-900 rounded-lg mb-3 h-48 flex items-center justify-center">
                        <img src="https://images.unsplash.com/photo-1544620347-c4fd4a3d5957?w=600&h=300&fit=crop" alt="Rotterdam Port Transportation" class="w-full h-full object-cover rounded-lg opacity-80">
                        <div class="absolute inset-0 flex items-center justify-center">
                            <div class="bg-black bg-opacity-50 rounded-full p-4">
                                <i class="fas fa-play text-white text-2xl"></i>
                            </div>
                        </div>
                        <div class="absolute bottom-2 right-2 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded">1:23</div>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex space-x-4">
                            <button class="flex items-center space-x-1 text-gray-600 hover:text-ocean-teal transition-colors">
                                <i class="fas fa-thumbs-up"></i>
                                <span class="text-sm" data-mock="true">18</span>
                            </button>
                            <button class="flex items-center space-x-1 text-gray-600 hover:text-ocean-teal transition-colors">
                                <i class="fas fa-comment"></i>
                                <span class="text-sm" data-mock="true">12</span>
                            </button>
                            <button class="flex items-center space-x-1 text-gray-600 hover:text-ocean-teal transition-colors">
                                <i class="fas fa-exclamation-triangle"></i>
                                <span class="text-sm">Report</span>
                            </button>
                        </div>
                        <span class="text-xs text-gray-500">#RotterdamPort #TaxiWarning</span>
                    </div>
                </div>
            </article>

            <!-- Post 3 - Restaurant Recommendation -->
            <article class="bg-white rounded-xl shadow-lg card-hover overflow-hidden" data-mock="true">
                <div class="p-4">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                                <i class="fas fa-user-secret text-gray-600"></i>
                            </div>
                            <div>
                                <h3 class="font-medium text-gray-900">Anonymous Sailor</h3>
                                <p class="text-xs text-gray-500">Singapore Port • 1 day ago</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-1 text-green-600">
                            <i class="fas fa-star text-sm"></i>
                            <span class="text-xs font-medium">4.8</span>
                        </div>
                    </div>
                    
                    <p class="text-gray-800 mb-3" data-mock="true">Found an amazing Indian restaurant near Singapore port! Authentic food, reasonable prices, and they understand seafarer schedules. Highly recommended! 🍛✨</p>
                    
                    <!-- Photo showing Indian restaurant and food -->
                    <img src="https://images.unsplash.com/photo-1565557623262-b51c2513a641?w=600&h=300&fit=crop" alt="Indian Restaurant Near Singapore Port" class="w-full h-48 object-cover rounded-lg mb-3">
                    
                    <div class="bg-light-blue p-3 rounded-lg mb-3">
                        <div class="flex items-center space-x-2 mb-2">
                            <i class="fas fa-map-marker-alt text-ocean-teal"></i>
                            <span class="text-sm font-medium text-gray-800" data-mock="true">Spice Garden Restaurant</span>
                        </div>
                        <p class="text-xs text-gray-600" data-mock="true">15 min walk from Gate 7, Singapore Port</p>
                        <div class="flex items-center space-x-4 mt-2 text-xs text-gray-600">
                            <span>⏰ Open 24/7</span>
                            <span>💰 Budget-friendly</span>
                            <span>🌟 Seafarer discount</span>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex space-x-4">
                            <button class="flex items-center space-x-1 text-gray-600 hover:text-ocean-teal transition-colors">
                                <i class="fas fa-thumbs-up"></i>
                                <span class="text-sm" data-mock="true">42</span>
                            </button>
                            <button class="flex items-center space-x-1 text-gray-600 hover:text-ocean-teal transition-colors">
                                <i class="fas fa-comment"></i>
                                <span class="text-sm" data-mock="true">15</span>
                            </button>
                            <button class="flex items-center space-x-1 text-gray-600 hover:text-ocean-teal transition-colors">
                                <i class="fas fa-bookmark"></i>
                                <span class="text-sm">Save</span>
                            </button>
                        </div>
                        <span class="text-xs text-gray-500">#SingaporePort #IndianFood</span>
                    </div>
                </div>
            </article>

            <!-- Post 4 - Officers Meeting -->
            <article class="bg-white rounded-xl shadow-lg card-hover overflow-hidden" data-mock="true">
                <div class="p-4">
                    <div class="flex items-center justify-between mb-3">
                        <div class="flex items-center space-x-3">
                            <!-- Maritime officers meeting casually -->
                            <img src="https://images.unsplash.com/photo-1542744173-8e7e53415bb0?w=40&h=40&fit=crop&crop=face" alt="Maritime Officer" class="w-10 h-10 rounded-full object-cover">
                            <div>
                                <h3 class="font-medium text-gray-900">2nd Engineer</h3>
                                <p class="text-xs text-gray-500">Dubai Port • 3 hours ago</p>
                            </div>
                        </div>
                        <div class="flex items-center space-x-1 text-ocean-teal">
                            <i class="fas fa-users text-sm"></i>
                            <span class="text-xs">Group Meet</span>
                        </div>
                    </div>
                    
                    <p class="text-gray-800 mb-3" data-mock="true">Organizing a casual meetup for maritime officers in Dubai this evening. Great networking opportunity and share experiences over coffee! ☕</p>
                    
                    <!-- Photo showing maritime officers meeting casually -->
                    <img src="https://images.unsplash.com/photo-1515187029135-18ee286d815b?w=600&h=300&fit=crop" alt="Maritime Officers Meeting Casually" class="w-full h-48 object-cover rounded-lg mb-3">
                    
                    <div class="bg-gradient-to-r from-navy to-ocean-teal p-3 rounded-lg mb-3 text-white">
                        <div class="flex items-center space-x-2 mb-2">
                            <i class="fas fa-calendar-alt"></i>
                            <span class="text-sm font-medium" data-mock="true">Today, 7:00 PM</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-map-marker-alt"></i>
                            <span class="text-sm" data-mock="true">Marina Cafe, Dubai Marina</span>
                        </div>
                        <div class="mt-2 text-xs opacity-90">
                            <span data-mock="true">8 officers interested • 3 confirmed</span>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between">
                        <div class="flex space-x-4">
                            <button class="flex items-center space-x-1 text-gray-600 hover:text-ocean-teal transition-colors">
                                <i class="fas fa-heart"></i>
                                <span class="text-sm" data-mock="true">21</span>
                            </button>
                            <button class="flex items-center space-x-1 text-gray-600 hover:text-ocean-teal transition-colors">
                                <i class="fas fa-comment"></i>
                                <span class="text-sm" data-mock="true">9</span>
                            </button>
                            <button class="bg-ocean-teal text-white px-4 py-1 rounded-full text-sm hover:bg-opacity-90 transition-colors">
                                Join Meetup
                            </button>
                        </div>
                        <span class="text-xs text-gray-500">#DubaiPort #Networking</span>
                    </div>
                </div>
            </article>
        </div>

        <!-- Load More -->
        <div class="text-center py-8">
            <button class="bg-white text-ocean-teal border border-ocean-teal px-6 py-2 rounded-full font-medium hover:bg-ocean-teal hover:text-white transition-colors">
                Load More Posts
            </button>
        </div>
    </main>

    <!-- Bottom Navigation -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50">
        <div class="flex items-center justify-around py-2">
            <button class="flex flex-col items-center p-2 text-ocean-teal">
                <i class="fas fa-home text-xl mb-1"></i>
                <span class="text-xs">Feed</span>
            </button>
            <button class="flex flex-col items-center p-2 text-gray-400 hover:text-ocean-teal transition-colors">
                <i class="fas fa-search text-xl mb-1"></i>
                <span class="text-xs">Discover</span>
            </button>
            <button class="flex flex-col items-center p-2 text-gray-400 hover:text-ocean-teal transition-colors relative">
                <div class="w-12 h-12 bg-ocean-teal rounded-full flex items-center justify-center absolute -top-6">
                    <i class="fas fa-plus text-white text-xl"></i>
                </div>
                <span class="text-xs mt-6">Post</span>
            </button>
            <button class="flex flex-col items-center p-2 text-gray-400 hover:text-ocean-teal transition-colors">
                <i class="fas fa-compass text-xl mb-1"></i>
                <span class="text-xs">Nearby</span>
            </button>
            <button class="flex flex-col items-center p-2 text-gray-400 hover:text-ocean-teal transition-colors">
                <i class="fas fa-user text-xl mb-1"></i>
                <span class="text-xs">Profile</span>
            </button>
        </div>
    </nav>

    <script>
        (function() {
            // Toggle anonymous posting
            window.toggleAnonymous = function(element) {
                element.classList.toggle('active');
                // TODO: Implement anonymous toggle logic
            };

            // Handle media upload
            window.handleMediaUpload = function() {
                // TODO: Implement media capture/upload functionality
                // Should handle both camera capture and file selection
                console.log('Media upload triggered');
            };

            // Smooth scrolling for better mobile experience
            document.addEventListener('DOMContentLoaded', function() {
                // Add smooth scroll behavior
                document.documentElement.style.scrollBehavior = 'smooth';
                
                // Handle tab switching
                const tabs = document.querySelectorAll('header button');
                tabs.forEach(tab => {
                    tab.addEventListener('click', function() {
                        tabs.forEach(t => t.classList.remove('bg-white', 'bg-opacity-20'));
                        this.classList.add('bg-white', 'bg-opacity-20');
                        // TODO: Implement tab content switching
                    });
                });

                // Handle bottom navigation
                const navButtons = document.querySelectorAll('nav button');
                navButtons.forEach(button => {
                    button.addEventListener('click', function() {
                        navButtons.forEach(b => {
                            b.classList.remove('text-ocean-teal');
                            b.classList.add('text-gray-400');
                        });
                        this.classList.remove('text-gray-400');
                        this.classList.add('text-ocean-teal');
                        // TODO: Implement navigation logic
                    });
                });

                // Add touch feedback for mobile
                const cards = document.querySelectorAll('.card-hover');
                cards.forEach(card => {
                    card.addEventListener('touchstart', function() {
                        this.style.transform = 'scale(0.98)';
                    });
                    card.addEventListener('touchend', function() {
                        this.style.transform = 'scale(1)';
                    });
                });
            });

            // Mock interaction handlers
            document.addEventListener('click', function(e) {
                if (e.target.closest('button')) {
                    const button = e.target.closest('button');
                    if (button.textContent.includes('thumbs-up') || button.querySelector('.fa-thumbs-up')) {
                        // TODO: Implement like functionality
                        console.log('Like button clicked');
                    }
                    if (button.textContent.includes('comment') || button.querySelector('.fa-comment')) {
                        // TODO: Implement comment functionality
                        console.log('Comment button clicked');
                    }
                    if (button.textContent.includes('Share') || button.querySelector('.fa-share')) {
                        // TODO: Implement share functionality
                        console.log('Share button clicked');
                    }
                }
            });
        })();
    </script>
</body>
</html>