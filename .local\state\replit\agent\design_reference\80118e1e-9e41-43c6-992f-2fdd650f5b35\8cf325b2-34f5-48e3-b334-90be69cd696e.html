<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> Maritime - Officer & Crew Discovery</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'navy': {
                            600: '#1e3a8a',
                            700: '#1e40af',
                            800: '#1e3a8a',
                            900: '#1e2a5e'
                        },
                        'ocean': {
                            400: '#06b6d4',
                            500: '#0891b2',
                            600: '#0e7490'
                        }
                    },
                    fontFamily: {
                        'sans': ['Inter', 'sans-serif']
                    }
                }
            }
        };
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link rel="manifest" href="/manifest.json">
    <meta name="theme-color" content="#1e3a8a">
</head>
<body class="bg-slate-50 font-sans">
    <!-- @COMPONENT: MainApp [Full application with navigation and content] -->
    <div class="min-h-screen bg-gradient-to-b from-slate-50 to-slate-100">
        
        <!-- @COMPONENT: Header [Main navigation header] -->
        <header class="bg-navy-800 text-white sticky top-0 z-50 shadow-lg">
            <div class="flex items-center justify-between p-4">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-ocean-400 rounded-full flex items-center justify-center">
                        <i class="fas fa-anchor text-white text-lg"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold">Koi Maritime</h1>
                        <p class="text-xs text-blue-200">Officer & Crew Discovery</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="relative" data-event="click:toggleNotifications">
                        <i class="fas fa-bell text-xl"></i>
                        <span class="absolute -top-1 -right-1 bg-red-500 text-xs rounded-full w-5 h-5 flex items-center justify-center">3</span>
                    </button>
                    <button class="w-8 h-8 bg-ocean-400 rounded-full flex items-center justify-center" data-event="click:openProfile">
                        <i class="fas fa-user text-sm"></i>
                    </button>
                </div>
            </div>
        </header>
        <!-- @END_COMPONENT: Header -->

        <!-- @COMPONENT: MainContent [Tab-based discovery interface] -->
        <main class="pb-20">
            
            <!-- Tab Navigation -->
            <div class="bg-white border-b border-slate-200 sticky top-[72px] z-40">
                <div class="flex">
                    <button class="flex-1 py-4 px-6 text-center border-b-2 border-navy-600 text-navy-600 font-semibold" data-tab="officers">
                        <i class="fas fa-user-tie mb-1 block"></i>
                        Officers Nearby
                    </button>
                    <button class="flex-1 py-4 px-6 text-center border-b-2 border-transparent text-slate-500 font-medium" data-tab="crew">
                        <i class="fas fa-users mb-1 block"></i>
                        Crew Nearby
                    </button>
                </div>
            </div>

            <!-- Officers Tab Content -->
            <div id="officers-tab" class="tab-content">
                <!-- Location Header -->
                <div class="bg-white p-4 border-b border-slate-200">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-map-marker-alt text-ocean-500"></i>
                            <span class="text-sm text-slate-600">Mumbai Port Area</span>
                        </div>
                        <button class="text-sm text-ocean-500 font-medium" data-event="click:changeLocation">
                            Change Location
                        </button>
                    </div>
                    <div class="flex items-center space-x-4 mt-2">
                        <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">
                            <i class="fas fa-circle text-green-500 text-xs mr-1"></i>
                            12 Officers Online
                        </span>
                        <button class="text-xs text-slate-500" data-event="click:toggleFilters">
                            <i class="fas fa-filter mr-1"></i>
                            Filter by Rank
                        </button>
                    </div>
                </div>

                <!-- Officer Cards -->
                <div class="p-4 space-y-4">
                    <!-- Officer Card 1 -->
                    <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-4" data-mock="true">
                        <div class="flex items-start space-x-4">
                            <!-- Professional headshot of a maritime officer in uniform -->
                            <img src="https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150" alt="Chief Engineer Profile" class="w-16 h-16 rounded-full object-cover border-2 border-navy-200">
                            <div class="flex-1">
                                <div class="flex items-center justify-between">
                                    <h3 class="font-semibold text-slate-900" data-bind="officer.name">Rajesh Kumar</h3>
                                    <span class="text-xs bg-navy-100 text-navy-700 px-2 py-1 rounded-full font-medium">2.1 km</span>
                                </div>
                                <div class="flex items-center space-x-2 mt-1">
                                    <span class="bg-amber-100 text-amber-700 px-2 py-1 rounded text-xs font-medium">Chief Engineer</span>
                                    <span class="text-xs text-slate-500">Engine Dept.</span>
                                </div>
                                <p class="text-sm text-slate-600 mt-1" data-bind="officer.vessel">MV Ocean Pioneer</p>
                                <div class="flex items-center justify-between mt-3">
                                    <div class="flex items-center space-x-1 text-xs text-slate-500">
                                        <i class="fas fa-circle text-green-500"></i>
                                        <span>Online</span>
                                        <span>•</span>
                                        <span>15+ years exp.</span>
                                    </div>
                                    <button class="bg-navy-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-navy-700" data-event="click:connectWithOfficer">
                                        Connect
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Officer Card 2 -->
                    <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-4" data-mock="true">
                        <div class="flex items-start space-x-4">
                            <!-- Professional maritime officer headshot -->
                            <img src="https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150" alt="Second Engineer Profile" class="w-16 h-16 rounded-full object-cover border-2 border-navy-200">
                            <div class="flex-1">
                                <div class="flex items-center justify-between">
                                    <h3 class="font-semibold text-slate-900" data-bind="officer.name">James Mitchell</h3>
                                    <span class="text-xs bg-navy-100 text-navy-700 px-2 py-1 rounded-full font-medium">0.8 km</span>
                                </div>
                                <div class="flex items-center space-x-2 mt-1">
                                    <span class="bg-blue-100 text-blue-700 px-2 py-1 rounded text-xs font-medium">2nd Engineer</span>
                                    <span class="text-xs text-slate-500">Engine Dept.</span>
                                </div>
                                <p class="text-sm text-slate-600 mt-1" data-bind="officer.vessel">MV Stellar Wind</p>
                                <div class="flex items-center justify-between mt-3">
                                    <div class="flex items-center space-x-1 text-xs text-slate-500">
                                        <i class="fas fa-circle text-green-500"></i>
                                        <span>Online</span>
                                        <span>•</span>
                                        <span>8 years exp.</span>
                                    </div>
                                    <button class="bg-navy-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-navy-700" data-event="click:connectWithOfficer">
                                        Connect
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Officer Card 3 -->
                    <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-4" data-mock="true">
                        <div class="flex items-start space-x-4">
                            <!-- Maritime officer in professional setting -->
                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150" alt="Third Engineer Profile" class="w-16 h-16 rounded-full object-cover border-2 border-navy-200">
                            <div class="flex-1">
                                <div class="flex items-center justify-between">
                                    <h3 class="font-semibold text-slate-900" data-bind="officer.name">Piyush Gupta</h3>
                                    <span class="text-xs bg-navy-100 text-navy-700 px-2 py-1 rounded-full font-medium">1.5 km</span>
                                </div>
                                <div class="flex items-center space-x-2 mt-1">
                                    <span class="bg-green-100 text-green-700 px-2 py-1 rounded text-xs font-medium">Chief Engineer</span>
                                    <span class="text-xs text-slate-500">Engine Dept.</span>
                                </div>
                                <p class="text-sm text-slate-600 mt-1" data-bind="officer.vessel">MT Solar Claire</p>
                                <div class="flex items-center justify-between mt-3">
                                    <div class="flex items-center space-x-1 text-xs text-slate-500">
                                        <i class="fas fa-circle text-amber-500"></i>
                                        <span>Away</span>
                                        <span>•</span>
                                        <span>21+ years exp.</span>
                                    </div>
                                    <button class="bg-navy-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-navy-700" data-event="click:connectWithOfficer">
                                        Connect
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Officer Card 4 -->
                    <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-4" data-mock="true">
                        <div class="flex items-start space-x-4">
                            <!-- Female maritime officer -->
                            <img src="https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150" alt="Chief Officer Profile" class="w-16 h-16 rounded-full object-cover border-2 border-navy-200">
                            <div class="flex-1">
                                <div class="flex items-center justify-between">
                                    <h3 class="font-semibold text-slate-900" data-bind="officer.name">Sarah Chen</h3>
                                    <span class="text-xs bg-navy-100 text-navy-700 px-2 py-1 rounded-full font-medium">3.2 km</span>
                                </div>
                                <div class="flex items-center space-x-2 mt-1">
                                    <span class="bg-purple-100 text-purple-700 px-2 py-1 rounded text-xs font-medium">Chief Officer</span>
                                    <span class="text-xs text-slate-500">Deck Dept.</span>
                                </div>
                                <p class="text-sm text-slate-600 mt-1" data-bind="officer.vessel">MV Container Star</p>
                                <div class="flex items-center justify-between mt-3">
                                    <div class="flex items-center space-x-1 text-xs text-slate-500">
                                        <i class="fas fa-circle text-green-500"></i>
                                        <span>Online</span>
                                        <span>•</span>
                                        <span>12 years exp.</span>
                                    </div>
                                    <button class="bg-navy-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-navy-700" data-event="click:connectWithOfficer">
                                        Connect
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Crew Tab Content (Hidden by default) -->
            <div id="crew-tab" class="tab-content hidden">
                <!-- Vessel Selection -->
                <div class="bg-white p-4 border-b border-slate-200">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-ship text-ocean-500"></i>
                            <span class="text-sm text-slate-600">MV Ocean Pioneer</span>
                        </div>
                        <button class="text-sm text-ocean-500 font-medium" data-event="click:changeVessel">
                            Change Vessel
                        </button>
                    </div>
                    <div class="flex items-center space-x-4 mt-2">
                        <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded-full">
                            <i class="fas fa-circle text-green-500 text-xs mr-1"></i>
                            24 Crew Online
                        </span>
                        <button class="text-xs text-slate-500" data-event="click:toggleDeptFilters">
                            <i class="fas fa-filter mr-1"></i>
                            Filter by Department
                        </button>
                    </div>
                </div>

                <!-- Crew Cards -->
                <div class="p-4 space-y-4">
                    <!-- @MAP: crewMembers.map(member => ( -->
                    <!-- Crew Card 1 -->
                    <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-4" data-mock="true">
                        <div class="flex items-start space-x-4">
                            <!-- Ship crew member in working uniform -->
                            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150" alt="Able Seaman Profile" class="w-16 h-16 rounded-full object-cover border-2 border-navy-200">
                            <div class="flex-1">
                                <div class="flex items-center justify-between">
                                    <h3 class="font-semibold text-slate-900" data-bind="crew.name">Miguel Santos</h3>
                                    <div class="flex items-center space-x-1">
                                        <i class="fas fa-circle text-green-500 text-xs"></i>
                                        <span class="text-xs text-slate-500">Online</span>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2 mt-1">
                                    <span class="bg-orange-100 text-orange-700 px-2 py-1 rounded text-xs font-medium">Able Seaman</span>
                                    <span class="text-xs text-slate-500">Deck Dept.</span>
                                </div>
                                <p class="text-sm text-slate-600 mt-1" data-bind="crew.vessel">MV Ocean Pioneer</p>
                                <div class="flex items-center justify-between mt-3">
                                    <div class="flex items-center space-x-1 text-xs text-slate-500">
                                        <span>6 years exp.</span>
                                        <span>•</span>
                                        <span>Bridge Team</span>
                                    </div>
                                    <button class="bg-ocean-500 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-ocean-600" data-event="click:connectWithCrew">
                                        Connect
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Crew Card 2 -->
                    <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-4" data-mock="true">
                        <div class="flex items-start space-x-4">
                            <!-- Maritime crew member in safety gear -->
                            <img src="https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150" alt="Oiler Profile" class="w-16 h-16 rounded-full object-cover border-2 border-navy-200">
                            <div class="flex-1">
                                <div class="flex items-center justify-between">
                                    <h3 class="font-semibold text-slate-900" data-bind="crew.name">Lisa Rodriguez</h3>
                                    <div class="flex items-center space-x-1">
                                        <i class="fas fa-circle text-amber-500 text-xs"></i>
                                        <span class="text-xs text-slate-500">Away</span>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2 mt-1">
                                    <span class="bg-blue-100 text-blue-700 px-2 py-1 rounded text-xs font-medium">Motorman</span>
                                    <span class="text-xs text-slate-500">Engine Dept.</span>
                                </div>
                                <p class="text-sm text-slate-600 mt-1" data-bind="crew.vessel">MV Ocean Pioneer</p>
                                <div class="flex items-center justify-between mt-3">
                                    <div class="flex items-center space-x-1 text-xs text-slate-500">
                                        <span>4 years exp.</span>
                                        <span>•</span>
                                        <span>Engine Room</span>
                                    </div>
                                    <button class="bg-ocean-500 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-ocean-600" data-event="click:connectWithCrew">
                                        Connect
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Crew Card 3 -->
                    <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-4" data-mock="true">
                        <div class="flex items-start space-x-4">
                            <!-- Ship cook in galley uniform -->
                            <img src="https://pixabay.com/get/g70793a8b2ae09ea4b599b7439742a7e11fb0f425947a1daefbcd62c5503fb2e4a46e9a527f9cbc02b44713c9afff4483a5f9b108d54a76131327387380c16fa7_1280.jpg" alt="Ship Cook Profile" class="w-16 h-16 rounded-full object-cover border-2 border-navy-200">
                            <div class="flex-1">
                                <div class="flex items-center justify-between">
                                    <h3 class="font-semibold text-slate-900" data-bind="crew.name">Ahmed Hassan</h3>
                                    <div class="flex items-center space-x-1">
                                        <i class="fas fa-circle text-green-500 text-xs"></i>
                                        <span class="text-xs text-slate-500">Online</span>
                                    </div>
                                </div>
                                <div class="flex items-center space-x-2 mt-1">
                                    <span class="bg-red-100 text-red-700 px-2 py-1 rounded text-xs font-medium">Ship Cook</span>
                                    <span class="text-xs text-slate-500">Catering</span>
                                </div>
                                <p class="text-sm text-slate-600 mt-1" data-bind="crew.vessel">MV Ocean Pioneer</p>
                                <div class="flex items-center justify-between mt-3">
                                    <div class="flex items-center space-x-1 text-xs text-slate-500">
                                        <span>10 years exp.</span>
                                        <span>•</span>
                                        <span>Galley Team</span>
                                    </div>
                                    <button class="bg-ocean-500 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-ocean-600" data-event="click:connectWithCrew">
                                        Connect
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!-- @END_MAP )) -->
                </div>
            </div>

        </main>
        <!-- @END_COMPONENT: MainContent -->

        <!-- @COMPONENT: BottomNavigation [Mobile navigation bar] -->
        <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-slate-200 z-40">
            <div class="flex items-center justify-around py-2">
                <button class="flex flex-col items-center py-2 px-4 text-navy-600" data-nav="discovery">
                    <i class="fas fa-compass text-xl mb-1"></i>
                    <span class="text-xs font-medium">Discovery</span>
                </button>
                <button class="flex flex-col items-center py-2 px-4 text-slate-400" data-nav="chat">
                    <i class="fas fa-comments text-xl mb-1"></i>
                    <span class="text-xs">Chat</span>
                </button>
                <button class="flex flex-col items-center py-2 px-4 text-slate-400" data-nav="profile">
                    <i class="fas fa-user text-xl mb-1"></i>
                    <span class="text-xs">Profile</span>
                </button>
                <button class="flex flex-col items-center py-2 px-4 text-slate-400" data-nav="privacy">
                    <i class="fas fa-shield-alt text-xl mb-1"></i>
                    <span class="text-xs">Privacy</span>
                </button>
                <button class="flex flex-col items-center py-2 px-4 text-slate-400" data-nav="qaaq">
                    <i class="fas fa-question-circle text-xl mb-1"></i>
                    <span class="text-xs">QAAQ</span>
                </button>
            </div>
        </nav>
        <!-- @END_COMPONENT: BottomNavigation -->

        <!-- @COMPONENT: ChatInterface [WhatsApp-style chat overlay] -->
        <div id="chat-overlay" class="fixed inset-0 bg-slate-50 z-50 hidden">
            <div class="flex flex-col h-full">
                <!-- Chat Header -->
                <div class="bg-navy-800 text-white p-4 flex items-center space-x-3">
                    <button class="text-white" data-event="click:closeChat">
                        <i class="fas fa-arrow-left text-xl"></i>
                    </button>
                    <!-- Maritime officer in uniform for chat profile -->
                    <img src="https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&h=100" alt="Chat Contact" class="w-10 h-10 rounded-full object-cover">
                    <div class="flex-1">
                        <h3 class="font-semibold" data-bind="chat.contactName">Rajesh Kumar</h3>
                        <p class="text-sm text-blue-200">Chief Engineer • Online</p>
                    </div>
                    <button class="text-white">
                        <i class="fas fa-phone text-lg"></i>
                    </button>
                </div>

                <!-- Chat Messages -->
                <div class="flex-1 overflow-y-auto p-4 space-y-4">
                    <!-- Received Message -->
                    <div class="flex space-x-3">
                        <img src="https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&auto=format&fit=crop&w=50&h=50" alt="Contact" class="w-8 h-8 rounded-full object-cover flex-shrink-0">
                        <div class="bg-white rounded-xl rounded-tl-none p-3 max-w-xs shadow-sm">
                            <p class="text-sm">Hi! I saw you're also working in Mumbai port area. Are you free for a technical discussion about MAN engines?</p>
                            <span class="text-xs text-slate-500 mt-1 block">2:45 PM</span>
                        </div>
                    </div>

                    <!-- Sent Message -->
                    <div class="flex justify-end">
                        <div class="bg-navy-600 text-white rounded-xl rounded-tr-none p-3 max-w-xs">
                            <p class="text-sm">Hello! Yes, absolutely. I have experience with MAN B&W engines. What specific issue are you facing?</p>
                            <span class="text-xs text-blue-200 mt-1 block">2:47 PM</span>
                        </div>
                    </div>

                    <!-- Received Message -->
                    <div class="flex space-x-3">
                        <img src="https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&auto=format&fit=crop&w=50&h=50" alt="Contact" class="w-8 h-8 rounded-full object-cover flex-shrink-0">
                        <div class="bg-white rounded-xl rounded-tl-none p-3 max-w-xs shadow-sm">
                            <p class="text-sm">We're having intermittent high exhaust temperatures on cylinder 3. Already checked fuel injectors. Any suggestions?</p>
                            <span class="text-xs text-slate-500 mt-1 block">2:48 PM</span>
                        </div>
                    </div>

                    <!-- WhatsApp Bot Message -->
                    <div class="flex space-x-3">
                        <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center flex-shrink-0">
                            <i class="fab fa-whatsapp text-white text-sm"></i>
                        </div>
                        <div class="bg-green-50 border border-green-200 rounded-xl rounded-tl-none p-3 max-w-xs">
                            <p class="text-sm text-green-800">🤖 <strong>Koi Maritime Bot:</strong> As experienced Chief Engineers, you might want to check the turbocharger nozzle ring for fouling and verify exhaust valve clearances. This is a common issue with MAN engines.</p>
                            <span class="text-xs text-green-600 mt-1 block">2:49 PM</span>
                        </div>
                    </div>
                </div>

                <!-- Chat Input -->
                <div class="bg-white border-t border-slate-200 p-4">
                    <div class="flex items-center space-x-3">
                        <button class="text-slate-400">
                            <i class="fas fa-paperclip text-lg"></i>
                        </button>
                        <input type="text" placeholder="Type a message..." class="flex-1 bg-slate-100 rounded-full px-4 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-navy-500">
                        <button class="bg-navy-600 text-white w-10 h-10 rounded-full flex items-center justify-center">
                            <i class="fas fa-paper-plane text-sm"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <!-- @END_COMPONENT: ChatInterface -->

        <!-- @COMPONENT: ProfileModal [User profile and settings] -->
        <div id="profile-modal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
            <div class="fixed inset-x-0 bottom-0 bg-white rounded-t-2xl max-h-[90vh] overflow-y-auto">
                <div class="p-6">
                    <!-- Profile Header -->
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-xl font-bold text-slate-900">My Profile</h2>
                        <button class="text-slate-400" data-event="click:closeProfile">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>

                    <!-- Profile Info -->
                    <div class="flex items-center space-x-4 mb-6">
                        <!-- User profile photo - maritime professional -->
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150" alt="My Profile" class="w-20 h-20 rounded-full object-cover border-4 border-navy-200">
                        <div class="flex-1">
                            <h3 class="font-bold text-lg text-slate-900" data-bind="user.name">Piyush Gupta</h3>
                            <p class="text-slate-600">Chief Engineer</p>
                            <p class="text-sm text-slate-500">21+ years experience</p>
                            <div class="flex items-center space-x-2 mt-2">
                                <span class="bg-green-100 text-green-700 px-2 py-1 rounded text-xs">Online</span>
                                <span class="bg-navy-100 text-navy-700 px-2 py-1 rounded text-xs">Verified</span>
                            </div>
                        </div>
                    </div>

                    <!-- Profile Sections -->
                    <div class="space-y-4">
                        <!-- Documents -->
                        <div class="border rounded-lg p-4">
                            <h4 class="font-semibold text-slate-900 mb-3 flex items-center">
                                <i class="fas fa-folder-open text-navy-600 mr-2"></i>
                                Marine Documents
                            </h4>
                            <div class="space-y-2">
                                <div class="flex items-center justify-between py-2">
                                    <span class="text-sm text-slate-600">COC - MEO Class I</span>
                                    <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">Valid</span>
                                </div>
                                <div class="flex items-center justify-between py-2">
                                    <span class="text-sm text-slate-600">STCW Certificates</span>
                                    <span class="text-xs bg-green-100 text-green-700 px-2 py-1 rounded">Valid</span>
                                </div>
                                <div class="flex items-center justify-between py-2">
                                    <span class="text-sm text-slate-600">Medical Certificate</span>
                                    <span class="text-xs bg-amber-100 text-amber-700 px-2 py-1 rounded">Expires Soon</span>
                                </div>
                            </div>
                        </div>

                        <!-- Resume Builder -->
                        <div class="border rounded-lg p-4">
                            <h4 class="font-semibold text-slate-900 mb-3 flex items-center">
                                <i class="fas fa-file-alt text-navy-600 mr-2"></i>
                                Resume Builder
                            </h4>
                            <p class="text-sm text-slate-600 mb-3">Create and manage your maritime resume</p>
                            <button class="bg-navy-600 text-white px-4 py-2 rounded-lg text-sm font-medium">
                                Edit Resume
                            </button>
                        </div>

                        <!-- Privacy Settings -->
                        <div class="border rounded-lg p-4">
                            <h4 class="font-semibold text-slate-900 mb-3 flex items-center">
                                <i class="fas fa-shield-alt text-navy-600 mr-2"></i>
                                Privacy & Settings
                            </h4>
                            <div class="space-y-3">
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-slate-600">Profile Visibility</span>
                                    <select class="text-sm border rounded px-2 py-1">
                                        <option>Officers Only</option>
                                        <option>All Crew</option>
                                        <option>Private</option>
                                    </select>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span class="text-sm text-slate-600">Location Sharing</span>
                                    <label class="relative inline-flex items-center cursor-pointer">
                                        <input type="checkbox" class="sr-only peer" checked>
                                        <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-navy-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-navy-600"></div>
                                    </label>
                                </div>
                            </div>
                        </div>

                        <!-- Compliance Links -->
                        <div class="border rounded-lg p-4">
                            <h4 class="font-semibold text-slate-900 mb-3 flex items-center">
                                <i class="fas fa-gavel text-navy-600 mr-2"></i>
                                Legal & Compliance
                            </h4>
                            <div class="space-y-2">
                                <button class="w-full text-left text-sm text-slate-600 py-2 hover:text-navy-600" data-event="click:showPrivacyPolicy">
                                    Privacy Policy
                                </button>
                                <button class="w-full text-left text-sm text-slate-600 py-2 hover:text-navy-600" data-event="click:showTermsOfService">
                                    Terms of Service
                                </button>
                                <button class="w-full text-left text-sm text-slate-600 py-2 hover:text-navy-600" data-event="click:exportData">
                                    Export My Data
                                </button>
                                <button class="w-full text-left text-sm text-red-600 py-2 hover:text-red-700" data-event="click:deleteAccount">
                                    Delete Account
                                </button>
                            </div>
                        </div>

                        <!-- QAAQ Integration -->
                        <div class="border rounded-lg p-4 bg-gradient-to-r from-navy-50 to-ocean-50">
                            <h4 class="font-semibold text-slate-900 mb-3 flex items-center">
                                <i class="fas fa-link text-navy-600 mr-2"></i>
                                Connect to QAAQ
                            </h4>
                            <p class="text-sm text-slate-600 mb-3">Link your profile to QAAQ for technical Q&A discussions</p>
                            <button class="bg-gradient-to-r from-navy-600 to-ocean-500 text-white px-4 py-2 rounded-lg text-sm font-medium">
                                Connect to QAAQ
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- @END_COMPONENT: ProfileModal -->

        <!-- @COMPONENT: PrivacyCompliancePage [GDPR/CCPA compliance] -->
        <div id="privacy-page" class="fixed inset-0 bg-white z-50 hidden overflow-y-auto">
            <div class="min-h-screen">
                <!-- Header -->
                <div class="bg-navy-800 text-white p-4 sticky top-0">
                    <div class="flex items-center space-x-3">
                        <button class="text-white" data-event="click:backToMain">
                            <i class="fas fa-arrow-left text-xl"></i>
                        </button>
                        <h1 class="text-xl font-bold">Privacy & Data Protection</h1>
                    </div>
                </div>

                <div class="p-6 space-y-6">
                    <!-- Privacy Overview -->
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <h2 class="font-bold text-blue-900 mb-2">Your Privacy Matters</h2>
                        <p class="text-sm text-blue-800">We're committed to protecting your personal data and ensuring transparency in how we collect, use, and store your information.</p>
                    </div>

                    <!-- Data Collection -->
                    <div class="border rounded-lg p-4">
                        <h3 class="font-semibold text-slate-900 mb-3">Data We Collect</h3>
                        <div class="space-y-3 text-sm text-slate-600">
                            <div class="flex items-start space-x-3">
                                <i class="fas fa-user text-navy-600 mt-1"></i>
                                <div>
                                    <strong>Profile Information:</strong> Name, rank, vessel, contact details, maritime certificates
                                </div>
                            </div>
                            <div class="flex items-start space-x-3">
                                <i class="fas fa-map-marker-alt text-navy-600 mt-1"></i>
                                <div>
                                    <strong>Location Data:</strong> Approximate location for nearby crew discovery (only when enabled)
                                </div>
                            </div>
                            <div class="flex items-start space-x-3">
                                <i class="fas fa-comments text-navy-600 mt-1"></i>
                                <div>
                                    <strong>Communication:</strong> Messages, connections, and interaction history
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Your Rights -->
                    <div class="border rounded-lg p-4">
                        <h3 class="font-semibold text-slate-900 mb-3">Your Data Rights</h3>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="space-y-2">
                                <button class="w-full bg-navy-100 text-navy-800 p-3 rounded-lg text-left hover:bg-navy-200" data-event="click:accessData">
                                    <i class="fas fa-eye mr-2"></i>
                                    <strong>Access Your Data</strong>
                                    <p class="text-xs mt-1">Download a copy of all your personal data</p>
                                </button>
                                <button class="w-full bg-ocean-100 text-ocean-800 p-3 rounded-lg text-left hover:bg-ocean-200" data-event="click:correctData">
                                    <i class="fas fa-edit mr-2"></i>
                                    <strong>Correct Information</strong>
                                    <p class="text-xs mt-1">Update or correct your personal information</p>
                                </button>
                            </div>
                            <div class="space-y-2">
                                <button class="w-full bg-amber-100 text-amber-800 p-3 rounded-lg text-left hover:bg-amber-200" data-event="click:restrictProcessing">
                                    <i class="fas fa-pause mr-2"></i>
                                    <strong>Restrict Processing</strong>
                                    <p class="text-xs mt-1">Limit how we use your data</p>
                                </button>
                                <button class="w-full bg-red-100 text-red-800 p-3 rounded-lg text-left hover:bg-red-200" data-event="click:deleteData">
                                    <i class="fas fa-trash mr-2"></i>
                                    <strong>Delete Account</strong>
                                    <p class="text-xs mt-1">Permanently remove all your data</p>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Cookie Consent -->
                    <div class="border rounded-lg p-4">
                        <h3 class="font-semibold text-slate-900 mb-3">Cookie Preferences</h3>
                        <div class="space-y-3">
                            <div class="flex items-center justify-between">
                                <div>
                                    <strong class="text-sm">Essential Cookies</strong>
                                    <p class="text-xs text-slate-600">Required for basic app functionality</p>
                                </div>
                                <div class="bg-gray-300 rounded-full w-12 h-6 flex items-center justify-start px-1">
                                    <div class="bg-white w-4 h-4 rounded-full"></div>
                                </div>
                            </div>
                            <div class="flex items-center justify-between">
                                <div>
                                    <strong class="text-sm">Analytics Cookies</strong>
                                    <p class="text-xs text-slate-600">Help us improve the app experience</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only peer" checked>
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-navy-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-navy-600"></div>
                                </label>
                            </div>
                            <div class="flex items-center justify-between">
                                <div>
                                    <strong class="text-sm">Marketing Cookies</strong>
                                    <p class="text-xs text-slate-600">Personalized recommendations</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-navy-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-navy-600"></div>
                                </label>
                            </div>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <div class="border rounded-lg p-4 bg-slate-50">
                        <h3 class="font-semibold text-slate-900 mb-3">Contact Our Privacy Team</h3>
                        <div class="space-y-2 text-sm text-slate-600">
                            <p><strong>Email:</strong> <EMAIL></p>
                            <p><strong>Data Protection Officer:</strong> <EMAIL></p>
                            <p><strong>Address:</strong> Maritime Privacy Office, QAAQ IT Solutions</p>
                            <p class="text-xs mt-3">Response time: Within 30 days as required by GDPR</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- @END_COMPONENT: PrivacyCompliancePage -->

    </div>
    <!-- @END_COMPONENT: MainApp -->

    <!-- @FUNCTIONALITY: This mobile-first PWA includes location-based officer discovery, vessel-based crew networking, WhatsApp-style chat, comprehensive privacy controls, and integration with the parent QAAQ platform -->

    <script>
        // TODO: Implement business logic, API calls, or state management
        
        (function() {
            // Tab switching functionality
            const tabButtons = document.querySelectorAll('[data-tab]');
            const tabContents = document.querySelectorAll('.tab-content');
            
            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const targetTab = button.dataset.tab;
                    
                    // Update button states
                    tabButtons.forEach(btn => {
                        btn.classList.remove('border-navy-600', 'text-navy-600');
                        btn.classList.add('border-transparent', 'text-slate-500');
                    });
                    button.classList.remove('border-transparent', 'text-slate-500');
                    button.classList.add('border-navy-600', 'text-navy-600');
                    
                    // Update content visibility
                    tabContents.forEach(content => {
                        content.classList.add('hidden');
                    });
                    document.getElementById(`${targetTab}-tab`).classList.remove('hidden');
                });
            });

            // Navigation functionality
            const navButtons = document.querySelectorAll('[data-nav]');
            navButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const target = button.dataset.nav;
                    
                    // Reset all nav buttons
                    navButtons.forEach(btn => {
                        btn.classList.remove('text-navy-600');
                        btn.classList.add('text-slate-400');
                    });
                    
                    // Activate clicked button
                    button.classList.remove('text-slate-400');
                    button.classList.add('text-navy-600');
                    
                    // Handle navigation
                    if (target === 'chat') {
                        document.getElementById('chat-overlay').classList.remove('hidden');
                    } else if (target === 'profile') {
                        document.getElementById('profile-modal').classList.remove('hidden');
                    } else if (target === 'privacy') {
                        document.getElementById('privacy-page').classList.remove('hidden');
                    } else if (target === 'qaaq') {
                        // Redirect to parent QAAQ app
                        window.open('https://qaaqit.replit.app/', '_blank');
                    }
                });
            });

            // Modal close functionality
            document.addEventListener('click', (e) => {
                if (e.target.dataset.event === 'click:closeChat') {
                    document.getElementById('chat-overlay').classList.add('hidden');
                }
                if (e.target.dataset.event === 'click:closeProfile') {
                    document.getElementById('profile-modal').classList.add('hidden');
                }
                if (e.target.dataset.event === 'click:backToMain') {
                    document.getElementById('privacy-page').classList.add('hidden');
                }
            });

            // Connect button functionality
            document.addEventListener('click', (e) => {
                if (e.target.dataset.event === 'click:connectWithOfficer' || e.target.dataset.event === 'click:connectWithCrew') {
                    // Show connection success animation
                    e.target.innerHTML = '<i class="fas fa-check mr-1"></i>Connected';
                    e.target.classList.remove('bg-navy-600', 'bg-ocean-500', 'hover:bg-navy-700', 'hover:bg-ocean-600');
                    e.target.classList.add('bg-green-500');
                    
                    setTimeout(() => {
                        document.getElementById('chat-overlay').classList.remove('hidden');
                    }, 500);
                }
            });

            // PWA Installation prompt
            let deferredPrompt;
            window.addEventListener('beforeinstallprompt', (e) => {
                e.preventDefault();
                deferredPrompt = e;
                
                // Show install banner
                const installBanner = document.createElement('div');
                installBanner.className = 'fixed top-20 left-4 right-4 bg-navy-600 text-white p-4 rounded-lg shadow-lg z-40';
                installBanner.innerHTML = `
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="font-semibold">Install Koi Maritime</p>
                            <p class="text-sm text-blue-200">Get quick access from your home screen</p>
                        </div>
                        <button class="bg-white text-navy-600 px-3 py-1 rounded font-medium text-sm" onclick="installPWA()">Install</button>
                    </div>
                `;
                document.body.appendChild(installBanner);
                
                setTimeout(() => {
                    installBanner.remove();
                }, 8000);
            });

            window.installPWA = () => {
                if (deferredPrompt) {
                    deferredPrompt.prompt();
                    deferredPrompt.userChoice.then((result) => {
                        deferredPrompt = null;
                    });
                }
            };
        })();
    </script>
</body>
</html>