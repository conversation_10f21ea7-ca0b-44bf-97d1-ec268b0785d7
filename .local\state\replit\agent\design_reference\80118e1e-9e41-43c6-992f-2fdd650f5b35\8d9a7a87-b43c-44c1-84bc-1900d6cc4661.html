<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Koi - Maritime Professional Discovery</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'navy': '#1e3a8a',
                        'ocean-teal': '#0891b2',
                        'light-teal': '#67e8f9',
                        'maritime-gray': '#64748b'
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif']
                    }
                }
            }
        };
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50 font-sans">
    <!-- @COMPONENT: AppHeader -->
    <header class="bg-navy text-white px-4 py-3 flex items-center justify-between sticky top-0 z-50 shadow-lg">
        <div class="flex items-center space-x-3">
            <div class="w-8 h-8 bg-ocean-teal rounded-full flex items-center justify-center">
                <i class="fas fa-fish text-white text-lg"></i>
            </div>
            <h1 class="text-xl font-bold">Koi</h1>
        </div>
        <div class="flex items-center space-x-4">
            <button class="p-2 hover:bg-blue-800 rounded-lg transition-colors">
                <i class="fas fa-sliders-h text-lg"></i>
            </button>
            <button class="p-2 hover:bg-blue-800 rounded-lg transition-colors">
                <i class="fas fa-user-circle text-lg"></i>
            </button>
        </div>
    </header>
    <!-- @END_COMPONENT: AppHeader -->

    <!-- @COMPONENT: DiscoveryInterface -->
    <main class="relative h-screen">
        <!-- Map Container -->
        <div class="relative w-full h-full bg-blue-100">
            <!-- Simulated Map Background -->
            <!-- A bird's eye view of a busy maritime port with ships and docks -->
            <div class="w-full h-full bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&h=1080');">
                <div class="absolute inset-0 bg-blue-900 bg-opacity-30"></div>
            </div>

            <!-- Sailor Markers on Map -->
            <!-- @STATE: sailors:array = [] -->
            <!-- @MAP: sailors.map(sailor => ( -->
            <div class="absolute top-1/4 left-1/3 transform -translate-x-1/2 -translate-y-1/2 cursor-pointer" data-bind="sailor.location">
                <div class="w-12 h-12 bg-ocean-teal rounded-full border-4 border-white shadow-lg flex items-center justify-center animate-pulse">
                    <i class="fas fa-anchor text-white text-lg"></i>
                </div>
                <div class="absolute -top-2 -right-2 w-6 h-6 bg-green-500 rounded-full border-2 border-white"></div>
            </div>

            <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 cursor-pointer">
                <div class="w-12 h-12 bg-navy rounded-full border-4 border-white shadow-lg flex items-center justify-center">
                    <i class="fas fa-ship text-white text-lg"></i>
                </div>
                <div class="absolute -top-2 -right-2 w-6 h-6 bg-green-500 rounded-full border-2 border-white"></div>
            </div>

            <div class="absolute top-3/4 left-1/4 transform -translate-x-1/2 -translate-y-1/2 cursor-pointer">
                <div class="w-12 h-12 bg-orange-500 rounded-full border-4 border-white shadow-lg flex items-center justify-center">
                    <i class="fas fa-hard-hat text-white text-lg"></i>
                </div>
                <div class="absolute -top-2 -right-2 w-6 h-6 bg-yellow-500 rounded-full border-2 border-white"></div>
            </div>

            <div class="absolute top-1/3 right-1/4 transform -translate-x-1/2 -translate-y-1/2 cursor-pointer">
                <div class="w-12 h-12 bg-purple-600 rounded-full border-4 border-white shadow-lg flex items-center justify-center">
                    <i class="fas fa-compass text-white text-lg"></i>
                </div>
                <div class="absolute -top-2 -right-2 w-6 h-6 bg-green-500 rounded-full border-2 border-white"></div>
            </div>
            <!-- @END_MAP )) -->

            <!-- Current User Location -->
            <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                <div class="w-4 h-4 bg-red-500 rounded-full border-2 border-white shadow-lg animate-pulse"></div>
                <div class="absolute inset-0 w-4 h-4 bg-red-500 rounded-full animate-ping opacity-75"></div>
            </div>

            <!-- Distance Radius Indicator -->
            <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 pointer-events-none">
                <div class="w-64 h-64 border-2 border-ocean-teal border-dashed rounded-full opacity-30"></div>
            </div>
        </div>

        <!-- Discovery Controls -->
        <div class="absolute top-20 left-4 right-4 z-40">
            <!-- Who's There Header -->
            <div class="bg-white rounded-xl shadow-lg p-4 mb-4">
                <h2 class="text-lg font-bold text-navy mb-2">Who's there?</h2>
                <p class="text-maritime-gray text-sm">Discover maritime professionals nearby</p>
                <div class="flex items-center justify-between mt-3">
                    <span class="text-sm font-medium text-navy">12 sailors within</span>
                    <span class="text-ocean-teal font-bold" data-bind="discoveryRadius">5km</span>
                </div>
            </div>

            <!-- Distance Slider -->
            <div class="bg-white rounded-xl shadow-lg p-4">
                <div class="flex items-center justify-between mb-3">
                    <label class="text-sm font-medium text-navy">Discovery Range</label>
                    <span class="text-ocean-teal font-bold" data-bind="rangeValue">5km</span>
                </div>
                <div class="relative">
                    <input 
                        type="range" 
                        min="1" 
                        max="50" 
                        value="5" 
                        class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                        data-event="input:updateDiscoveryRange"
                    >
                    <div class="flex justify-between text-xs text-gray-500 mt-1">
                        <span>1km</span>
                        <span>25km</span>
                        <span>50km</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sailor Profile Cards -->
        <!-- @COMPONENT: SailorProfileCard -->
        <div class="absolute bottom-20 left-0 right-0 z-30">
            <div class="px-4">
                <div class="bg-white rounded-xl shadow-xl overflow-hidden">
                    <div class="relative">
                        <!-- Professional maritime worker in uniform on a ship deck -->
                        <img src="https://images.unsplash.com/photo-1566576912321-d58ddd7a6088?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300" alt="Maritime professional" class="w-full h-48 object-cover">
                        <div class="absolute top-3 right-3 flex space-x-2">
                            <div class="w-8 h-8 bg-green-500 rounded-full border-2 border-white flex items-center justify-center">
                                <i class="fas fa-check text-white text-xs"></i>
                            </div>
                        </div>
                        <div class="absolute bottom-3 left-3 bg-black bg-opacity-50 rounded-lg px-2 py-1">
                            <span class="text-white text-sm font-medium">2.3km away</span>
                        </div>
                    </div>
                    <div class="p-4">
                        <div class="flex items-start justify-between mb-3">
                            <div>
                                <h3 class="text-lg font-bold text-navy" data-bind="sailor.name">Captain James Rodriguez</h3>
                                <p class="text-maritime-gray text-sm" data-bind="sailor.rank">Chief Engineer</p>
                            </div>
                            <div class="text-right">
                                <div class="text-ocean-teal font-semibold text-sm">15+ years</div>
                                <div class="text-gray-500 text-xs">Experience</div>
                            </div>
                        </div>
                        
                        <div class="flex items-center space-x-3 mb-3">
                            <div class="flex items-center space-x-1">
                                <i class="fas fa-ship text-ocean-teal text-sm"></i>
                                <span class="text-sm text-gray-700" data-bind="sailor.vessel">Container Vessel</span>
                            </div>
                            <div class="flex items-center space-x-1">
                                <i class="fas fa-map-marker-alt text-red-500 text-sm"></i>
                                <span class="text-sm text-gray-700" data-bind="sailor.port">Port of Hamburg</span>
                            </div>
                        </div>

                        <div class="flex items-center space-x-2 mb-4">
                            <div class="bg-blue-100 text-navy px-2 py-1 rounded-full text-xs font-medium">Diesel Engines</div>
                            <div class="bg-blue-100 text-navy px-2 py-1 rounded-full text-xs font-medium">QAAQ Expert</div>
                            <div class="bg-green-100 text-green-700 px-2 py-1 rounded-full text-xs font-medium">Online</div>
                        </div>

                        <div class="flex space-x-3">
                            <button class="flex-1 bg-ocean-teal text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-600 transition-colors flex items-center justify-center space-x-2" data-event="click:sendGreeting">
                                <i class="fas fa-comment-dots"></i>
                                <span>Say Hello</span>
                            </button>
                            <button class="bg-green-500 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-600 transition-colors flex items-center justify-center" data-event="click:openWhatsApp">
                                <i class="fab fa-whatsapp text-xl"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- @END_COMPONENT: SailorProfileCard -->

        <!-- Quick Actions Floating Button -->
        <div class="absolute bottom-32 right-4 z-40">
            <button class="w-14 h-14 bg-ocean-teal text-white rounded-full shadow-lg hover:bg-blue-600 transition-colors flex items-center justify-center" data-event="click:showQuickActions">
                <i class="fas fa-plus text-xl"></i>
            </button>
        </div>
    </main>
    <!-- @END_COMPONENT: DiscoveryInterface -->

    <!-- @COMPONENT: BottomNavigation -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50">
        <div class="flex items-center justify-around py-2">
            <button class="flex flex-col items-center py-2 px-4 text-ocean-teal" data-event="click:navigateToDiscover">
                <i class="fas fa-compass text-xl mb-1"></i>
                <span class="text-xs font-medium">Discover</span>
            </button>
            <button class="flex flex-col items-center py-2 px-4 text-gray-500 hover:text-ocean-teal transition-colors" data-event="click:navigateToConnections">
                <i class="fas fa-users text-xl mb-1"></i>
                <span class="text-xs font-medium">Connections</span>
            </button>
            <button class="flex flex-col items-center py-2 px-4 text-gray-500 hover:text-ocean-teal transition-colors" data-event="click:navigateToChats">
                <i class="fas fa-comments text-xl mb-1"></i>
                <span class="text-xs font-medium">Chats</span>
            </button>
            <button class="flex flex-col items-center py-2 px-4 text-gray-500 hover:text-ocean-teal transition-colors" data-event="click:navigateToQAAQ">
                <i class="fas fa-question-circle text-xl mb-1"></i>
                <span class="text-xs font-medium">QAAQ</span>
            </button>
        </div>
    </nav>
    <!-- @END_COMPONENT: BottomNavigation -->

    <!-- @COMPONENT: ConnectionModal -->
    <div id="connectionModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
        <div class="bg-white rounded-xl max-w-md w-full p-6">
            <div class="text-center mb-6">
                <div class="w-20 h-20 bg-ocean-teal rounded-full mx-auto mb-4 flex items-center justify-center">
                    <i class="fas fa-handshake text-white text-2xl"></i>
                </div>
                <h3 class="text-xl font-bold text-navy mb-2">Start a Conversation</h3>
                <p class="text-maritime-gray">Choose how you'd like to connect with Captain James</p>
            </div>
            
            <div class="space-y-3 mb-6">
                <button class="w-full bg-ocean-teal text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-600 transition-colors flex items-center justify-center space-x-3" data-event="click:sendDirectMessage">
                    <i class="fas fa-comment-dots text-lg"></i>
                    <span>Send Direct Message</span>
                </button>
                <button class="w-full bg-green-500 text-white py-3 px-4 rounded-lg font-medium hover:bg-green-600 transition-colors flex items-center justify-center space-x-3" data-event="click:connectWhatsApp">
                    <i class="fab fa-whatsapp text-lg"></i>
                    <span>Connect via WhatsApp</span>
                </button>
            </div>
            
            <div class="bg-blue-50 rounded-lg p-4 mb-4">
                <h4 class="font-semibold text-navy mb-2">Quick Ice Breakers:</h4>
                <div class="space-y-2">
                    <button class="w-full text-left text-sm text-gray-700 hover:text-ocean-teal transition-colors" data-event="click:sendIceBreaker" data-message="Hello! I see you're also a maritime engineer. Would love to connect!">
                        "Hello! I see you're also a maritime engineer. Would love to connect!"
                    </button>
                    <button class="w-full text-left text-sm text-gray-700 hover:text-ocean-teal transition-colors" data-event="click:sendIceBreaker" data-message="Hi there! Are you currently in port? Maybe we can grab coffee!">
                        "Hi there! Are you currently in port? Maybe we can grab coffee!"
                    </button>
                    <button class="w-full text-left text-sm text-gray-700 hover:text-ocean-teal transition-colors" data-event="click:sendIceBreaker" data-message="Hey! I noticed you're a QAAQ expert. Could use some advice on engine troubleshooting.">
                        "Hey! I noticed you're a QAAQ expert. Could use some advice on engine troubleshooting."
                    </button>
                </div>
            </div>
            
            <button class="w-full border border-gray-300 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-50 transition-colors" data-event="click:closeModal">
                Cancel
            </button>
        </div>
    </div>
    <!-- @END_COMPONENT: ConnectionModal -->

    <!-- @COMPONENT: ChatInterface -->
    <div id="chatInterface" class="fixed inset-0 bg-white z-50 hidden flex flex-col">
        <div class="bg-navy text-white px-4 py-3 flex items-center space-x-3">
            <button class="p-2 hover:bg-blue-800 rounded-lg transition-colors" data-event="click:closeChatInterface">
                <i class="fas fa-arrow-left text-lg"></i>
            </button>
            <div class="flex items-center space-x-3 flex-1">
                <!-- Maritime professional headshot for chat interface -->
                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&h=100" alt="Captain James" class="w-10 h-10 rounded-full object-cover border-2 border-ocean-teal">
                <div>
                    <h3 class="font-semibold" data-bind="chat.contactName">Captain James Rodriguez</h3>
                    <p class="text-sm opacity-75">Online • 2.3km away</p>
                </div>
            </div>
            <button class="p-2 hover:bg-blue-800 rounded-lg transition-colors" data-event="click:openWhatsAppChat">
                <i class="fab fa-whatsapp text-lg"></i>
            </button>
        </div>
        
        <div class="flex-1 overflow-y-auto bg-gray-50 p-4 space-y-4">
            <!-- @FUNCTIONALITY: This chat should implement real-time messaging with maritime professionals -->
            
            <!-- Received Message -->
            <div class="flex items-start space-x-3">
                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=50&h=50" alt="Captain James" class="w-8 h-8 rounded-full object-cover">
                <div class="bg-white rounded-lg p-3 max-w-xs shadow-sm">
                    <p class="text-gray-800" data-mock="true">Hello! Thanks for reaching out. I see you're also in the maritime industry. What brings you to Hamburg?</p>
                    <span class="text-xs text-gray-500 mt-1 block">2 min ago</span>
                </div>
            </div>
            
            <!-- Sent Message -->
            <div class="flex items-start space-x-3 justify-end">
                <div class="bg-ocean-teal text-white rounded-lg p-3 max-w-xs">
                    <p data-mock="true">Hi Captain! I'm a 2nd Engineer on a container vessel. Just docked this morning. Always great to connect with fellow maritime professionals!</p>
                    <span class="text-xs opacity-75 mt-1 block">1 min ago</span>
                </div>
            </div>
            
            <!-- System Message -->
            <div class="flex justify-center">
                <div class="bg-blue-100 text-navy rounded-lg px-3 py-2 text-sm">
                    <i class="fas fa-shield-alt mr-1"></i>
                    <span data-mock="true">Both users verified through QAAQ maritime credentials</span>
                </div>
            </div>
        </div>
        
        <div class="bg-white border-t border-gray-200 p-4">
            <div class="flex items-center space-x-3">
                <button class="p-2 text-gray-500 hover:text-ocean-teal transition-colors" data-event="click:attachFile">
                    <i class="fas fa-paperclip text-lg"></i>
                </button>
                <div class="flex-1 relative">
                    <input 
                        type="text" 
                        placeholder="Type a message..." 
                        class="w-full border border-gray-300 rounded-lg px-4 py-2 pr-12 focus:ring-2 focus:ring-ocean-teal focus:border-transparent"
                        data-event="keydown:sendMessageOnEnter"
                    >
                    <button class="absolute right-2 top-1/2 transform -translate-y-1/2 p-1 text-gray-500 hover:text-ocean-teal transition-colors" data-event="click:sendMessage">
                        <i class="fas fa-paper-plane text-lg"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    <!-- @END_COMPONENT: ChatInterface -->

    <!-- @COMPONENT: SettingsPanel -->
    <div id="settingsPanel" class="fixed inset-y-0 right-0 w-80 bg-white shadow-xl z-50 transform translate-x-full transition-transform duration-300">
        <div class="h-full flex flex-col">
            <div class="bg-navy text-white px-4 py-3 flex items-center justify-between">
                <h3 class="font-bold text-lg">Discovery Settings</h3>
                <button class="p-2 hover:bg-blue-800 rounded-lg transition-colors" data-event="click:closeSettingsPanel">
                    <i class="fas fa-times text-lg"></i>
                </button>
            </div>
            
            <div class="flex-1 overflow-y-auto p-4 space-y-6">
                <!-- Privacy Controls -->
                <div>
                    <h4 class="font-semibold text-navy mb-3">Privacy & Visibility</h4>
                    <div class="space-y-3">
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-700">Show my location</span>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" checked class="sr-only peer" data-event="change:toggleLocationSharing">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-ocean-teal"></div>
                            </label>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-700">Show online status</span>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" checked class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-ocean-teal"></div>
                            </label>
                        </div>
                        <div class="flex items-center justify-between">
                            <span class="text-sm text-gray-700">Show QAAQ expertise</span>
                            <label class="relative inline-flex items-center cursor-pointer">
                                <input type="checkbox" checked class="sr-only peer">
                                <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-ocean-teal"></div>
                            </label>
                        </div>
                    </div>
                </div>
                
                <!-- Discovery Filters -->
                <div>
                    <h4 class="font-semibold text-navy mb-3">Discovery Filters</h4>
                    <div class="space-y-3">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Minimum Experience</label>
                            <select class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-ocean-teal focus:border-transparent">
                                <option>Any experience level</option>
                                <option>1+ years</option>
                                <option>5+ years</option>
                                <option>10+ years</option>
                                <option>15+ years</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Vessel Type</label>
                            <select class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-ocean-teal focus:border-transparent">
                                <option>All vessel types</option>
                                <option>Container Vessel</option>
                                <option>Tanker</option>
                                <option>Bulk Carrier</option>
                                <option>Cruise Ship</option>
                                <option>Naval Vessel</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Maritime Rank</label>
                            <select class="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-ocean-teal focus:border-transparent">
                                <option>Any rank</option>
                                <option>Captain</option>
                                <option>Chief Engineer</option>
                                <option>First Officer</option>
                                <option>Second Engineer</option>
                                <option>Third Engineer</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <!-- QAAQ Integration -->
                <div>
                    <h4 class="font-semibold text-navy mb-3">QAAQ Integration</h4>
                    <div class="bg-blue-50 rounded-lg p-3 mb-3">
                        <div class="flex items-center space-x-2 mb-2">
                            <i class="fas fa-check-circle text-green-500"></i>
                            <span class="text-sm font-medium text-navy">Connected to QAAQ</span>
                        </div>
                        <p class="text-xs text-gray-600">Your maritime credentials and expertise are verified</p>
                    </div>
                    <button class="w-full bg-ocean-teal text-white py-2 px-4 rounded-lg font-medium hover:bg-blue-600 transition-colors text-sm">
                        Sync QAAQ Profile
                    </button>
                </div>
            </div>
        </div>
    </div>
    <!-- @END_COMPONENT: SettingsPanel -->

    <style>
        .slider::-webkit-slider-thumb {
            appearance: none;
            height: 20px;
            width: 20px;
            border-radius: 50%;
            background: #0891b2;
            cursor: pointer;
            border: 2px solid #ffffff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .slider::-moz-range-thumb {
            height: 20px;
            width: 20px;
            border-radius: 50%;
            background: #0891b2;
            cursor: pointer;
            border: 2px solid #ffffff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
    </style>

    <script>
        // TODO: Implement business logic, API calls, and state management for production
        (function() {
            // Simulated interaction handlers - replace with actual implementation
            
            // Settings panel toggle
            document.addEventListener('click', function(e) {
                if (e.target.closest('[data-event*="showQuickActions"]')) {
                    document.getElementById('settingsPanel').style.transform = 'translateX(0)';
                }
                if (e.target.closest('[data-event*="closeSettingsPanel"]')) {
                    document.getElementById('settingsPanel').style.transform = 'translateX(100%)';
                }
            });
            
            // Connection modal
            document.addEventListener('click', function(e) {
                if (e.target.closest('[data-event*="sendGreeting"]')) {
                    document.getElementById('connectionModal').classList.remove('hidden');
                }
                if (e.target.closest('[data-event*="closeModal"]')) {
                    document.getElementById('connectionModal').classList.add('hidden');
                }
                if (e.target.closest('[data-event*="sendDirectMessage"]')) {
                    document.getElementById('connectionModal').classList.add('hidden');
                    document.getElementById('chatInterface').classList.remove('hidden');
                }
            });
            
            // Chat interface
            document.addEventListener('click', function(e) {
                if (e.target.closest('[data-event*="closeChatInterface"]')) {
                    document.getElementById('chatInterface').classList.add('hidden');
                }
            });
            
            // Range slider update
            document.addEventListener('input', function(e) {
                if (e.target.type === 'range') {
                    const value = e.target.value;
                    document.querySelectorAll('[data-bind="rangeValue"]').forEach(el => {
                        el.textContent = value + 'km';
                    });
                }
            });
        })();
    </script>
</body>
</html>