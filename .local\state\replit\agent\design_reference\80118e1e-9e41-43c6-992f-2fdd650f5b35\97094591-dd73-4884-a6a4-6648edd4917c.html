<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QaaqConnect - Maritime Professional Network</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'navy': '#1e3a8a',
                        'ocean-teal': '#0891b2',
                        'deep-blue': '#1e40af',
                        'light-blue': '#e0f2fe',
                        'maritime-gray': '#64748b'
                    },
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .map-container {
            height: 60vh;
            min-height: 400px;
            background: linear-gradient(135deg, #0891b2 0%, #1e3a8a 100%);
            position: relative;
            overflow: hidden;
        }
        
        .sailor-pin {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 3px solid #fff;
            box-shadow: 0 2px 10px rgba(0,0,0,0.3);
            cursor: pointer;
            transition: transform 0.2s;
        }
        
        .sailor-pin:hover {
            transform: scale(1.1);
        }
        
        .user-pin {
            width: 50px;
            height: 50px;
            background: #059669;
            border: 4px solid #fff;
            border-radius: 50%;
            position: relative;
        }
        
        .user-pin::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 20px;
            height: 20px;
            background: #fff;
            border-radius: 50%;
        }
        
        .profile-card {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
        
        .maritime-gradient {
            background: linear-gradient(135deg, #1e3a8a 0%, #0891b2 100%);
        }
        
        .wave-pattern {
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
    </style>
</head>
<body class="font-inter bg-gray-50">
    <!-- Header with QAAQ AI Question Bar -->
    <header class="maritime-gradient text-white px-4 py-3 shadow-lg">
        <div class="max-w-6xl mx-auto">
            <div class="flex items-center justify-between mb-3">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-white rounded-full flex items-center justify-center">
                        <i class="fas fa-anchor text-navy text-xl"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold">QaaqConnect</h1>
                        <p class="text-sm opacity-90">Maritime Professional Network</p>
                    </div>
                </div>
                <button class="p-2 hover:bg-white/10 rounded-lg transition-colors">
                    <i class="fas fa-bars text-lg"></i>
                </button>
            </div>
            
            <!-- QAAQ AI Question Bar -->
            <div class="relative">
                <div class="flex items-center bg-white/10 backdrop-blur-sm rounded-xl px-4 py-3">
                    <i class="fas fa-robot text-ocean-teal mr-3"></i>
                    <input type="text" placeholder="Ask QAAQ AI: 'My Alfa Laval purifier keeps stopping...'" 
                           class="flex-1 bg-transparent text-white placeholder-white/70 outline-none">
                    <button class="ml-3 px-4 py-2 bg-ocean-teal hover:bg-cyan-600 rounded-lg transition-colors">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
            
            <!-- Tagline -->
            <div class="text-center mt-3">
                <p class="text-lg font-medium opacity-90">"1234 Koi Hai?"</p>
                <p class="text-sm opacity-75">Find maritime professionals near you</p>
            </div>
        </div>
    </header>

    <!-- Map Container -->
    <div class="map-container relative" id="mapContainer">
        <!-- Distance Controls -->
        <div class="absolute top-4 left-4 z-10">
            <div class="bg-white rounded-lg shadow-lg p-2 flex items-center space-x-2">
                <button class="w-8 h-8 bg-gray-100 hover:bg-gray-200 rounded-lg flex items-center justify-center transition-colors" onclick="decreaseRadius()">
                    <i class="fas fa-minus text-sm"></i>
                </button>
                <span class="px-3 py-1 font-medium text-navy" id="radiusDisplay">50km</span>
                <button class="w-8 h-8 bg-gray-100 hover:bg-gray-200 rounded-lg flex items-center justify-center transition-colors" onclick="increaseRadius()">
                    <i class="fas fa-plus text-sm"></i>
                </button>
            </div>
        </div>

        <!-- Filter Dropdown -->
        <div class="absolute top-4 right-4 z-10">
            <div class="relative">
                <button class="bg-white rounded-lg shadow-lg p-3 flex items-center space-x-2 hover:bg-gray-50 transition-colors" onclick="toggleFilters()">
                    <i class="fas fa-filter text-navy"></i>
                    <i class="fas fa-chevron-down text-sm text-maritime-gray" id="filterChevron"></i>
                </button>
                
                <!-- Filter Menu -->
                <div class="absolute top-12 right-0 bg-white rounded-lg shadow-xl border border-gray-200 p-4 w-56 hidden" id="filterMenu">
                    <h3 class="font-semibold text-navy mb-3">Filter Sailors</h3>
                    <div class="space-y-3">
                        <label class="flex items-center space-x-3 cursor-pointer">
                            <input type="checkbox" class="w-4 h-4 text-ocean-teal rounded border-gray-300 focus:ring-ocean-teal">
                            <span class="text-sm">Officers only</span>
                        </label>
                        <label class="flex items-center space-x-3 cursor-pointer">
                            <input type="checkbox" class="w-4 h-4 text-ocean-teal rounded border-gray-300 focus:ring-ocean-teal">
                            <span class="text-sm">Crew only</span>
                        </label>
                        <label class="flex items-center space-x-3 cursor-pointer">
                            <input type="checkbox" class="w-4 h-4 text-ocean-teal rounded border-gray-300 focus:ring-ocean-teal">
                            <span class="text-sm">Same nationality</span>
                        </label>
                    </div>
                </div>
            </div>
        </div>

        <!-- Map Content -->
        <div class="absolute inset-0 wave-pattern">
            <!-- User Pin (Center) -->
            <div class="user-pin absolute" style="top: 50%; left: 50%; transform: translate(-50%, -50%);"></div>
            
            <!-- User Location Label -->
            <div class="absolute bg-white rounded-lg shadow-lg px-3 py-2 text-sm font-medium text-navy" 
                 style="top: 45%; left: 52%; transform: translateX(-50%);">
                <i class="fas fa-location-dot text-green-500 mr-1"></i>
                You are here
            </div>

            <!-- Sailor Pins -->
            <!-- A diverse group of maritime professionals networking at a port -->
            <div class="sailor-pin absolute cursor-pointer" style="top: 35%; left: 60%;" onclick="showProfile('captain-smith')">
                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=80&h=80" 
                     alt="Maritime professional" class="w-full h-full rounded-full object-cover">
            </div>

            <!-- International maritime officers meeting for professional discussion -->
            <div class="sailor-pin absolute cursor-pointer" style="top: 40%; left: 35%;" onclick="showProfile('engineer-patel')">
                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=80&h=80" 
                     alt="Maritime engineer" class="w-full h-full rounded-full object-cover">
            </div>

            <!-- Maritime crew members collaborating at a shipping port -->
            <div class="sailor-pin absolute cursor-pointer" style="top: 65%; left: 45%;" onclick="showProfile('officer-chen')">
                <img src="https://pixabay.com/get/ge7f8947d31db0aaae27bb4abddd4d9756187d3ad7966f9c7782152e4beda0fc18283b5a2bb80445865c8b060e2b4338590b8fc752134db9a0ee67778ae041da9_1280.jpg" 
                     alt="Maritime officer" class="w-full h-full rounded-full object-cover">
            </div>

            <!-- Port community members gathering for maritime industry networking -->
            <div class="sailor-pin absolute cursor-pointer" style="top: 30%; left: 40%;" onclick="showProfile('chief-martinez')">
                <img src="https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&auto=format&fit=crop&w=80&h=80" 
                     alt="Chief engineer" class="w-full h-full rounded-full object-cover">
            </div>

            <!-- Sailors from different countries meeting at an international port -->
            <div class="sailor-pin absolute cursor-pointer" style="top: 55%; left: 70%;" onclick="showProfile('bosun-olsen')">
                <img src="https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?ixlib=rb-4.0.3&auto=format&fit=crop&w=80&h=80" 
                     alt="Bosun" class="w-full h-full rounded-full object-cover">
            </div>

            <!-- Maritime professionals building connections at a port gathering -->
            <div class="sailor-pin absolute cursor-pointer" style="top: 25%; left: 65%;" onclick="showProfile('radio-officer-kim')">
                <img src="https://images.unsplash.com/photo-1507591064344-4c6ce005b128?ixlib=rb-4.0.3&auto=format&fit=crop&w=80&h=80" 
                     alt="Radio officer" class="w-full h-full rounded-full object-cover">
            </div>
        </div>

        <!-- Location Permission Banner -->
        <div class="absolute bottom-4 left-4 right-4 bg-yellow-50 border border-yellow-200 rounded-lg p-3 flex items-center space-x-3" id="locationBanner">
            <i class="fas fa-location-crosshairs text-yellow-600"></i>
            <div class="flex-1">
                <p class="text-sm font-medium text-yellow-800">Enable location to find sailors near you</p>
                <p class="text-xs text-yellow-600">We need your location to show nearby maritime professionals</p>
            </div>
            <button class="px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg text-sm font-medium transition-colors" onclick="requestLocation()">
                Allow
            </button>
            <button class="p-1 text-yellow-600 hover:text-yellow-800" onclick="dismissLocationBanner()">
                <i class="fas fa-times text-sm"></i>
            </button>
        </div>
    </div>

    <!-- Profile Card Modal -->
    <div class="fixed inset-0 bg-black/50 z-50 hidden" id="profileModal" onclick="closeProfile()">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="profile-card rounded-xl shadow-2xl max-w-sm w-full p-6" onclick="event.stopPropagation()" id="profileCard">
                <!-- Profile content will be dynamically inserted here -->
            </div>
        </div>
    </div>

    <!-- CPSS Navigation -->
    <div class="bg-white border-t border-gray-200 px-4 py-3">
        <div class="max-w-6xl mx-auto">
            <div class="flex items-center justify-between text-sm">
                <span class="text-maritime-gray">CPSS Navigation</span>
                <div class="flex items-center space-x-4">
                    <button class="text-ocean-teal hover:text-deep-blue font-medium transition-colors">Groups</button>
                    <button class="text-ocean-teal hover:text-deep-blue font-medium transition-colors">Feedback</button>
                    <button class="text-ocean-teal hover:text-deep-blue font-medium transition-colors">Support</button>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <main class="max-w-6xl mx-auto px-4 py-6">
        <!-- Welcome Section -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
            <div class="flex items-start space-x-4">
                <!-- Maritime professionals networking at an international port conference -->
                <img src="https://images.unsplash.com/photo-1521737604893-d14cc237f11d?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&h=300" 
                     alt="Maritime networking" class="w-24 h-24 rounded-xl object-cover flex-shrink-0">
                <div class="flex-1">
                    <h2 class="text-xl font-bold text-navy mb-2">Welcome to QaaqConnect</h2>
                    <p class="text-maritime-gray mb-4">Connect with maritime professionals in your area. Share knowledge, build relationships, and advance your career at sea.</p>
                    <div class="flex items-center space-x-6 text-sm">
                        <div class="flex items-center space-x-2">
                            <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                            <span class="text-maritime-gray">847 sailors online</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-map-marker-alt text-ocean-teal"></i>
                            <span class="text-maritime-gray">156 ports covered</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Activity -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
            <h3 class="text-lg font-semibold text-navy mb-4 flex items-center">
                <i class="fas fa-clock text-ocean-teal mr-2"></i>
                Recent Activity
            </h3>
            
            <div class="space-y-4">
                <div class="flex items-start space-x-3 p-3 hover:bg-gray-50 rounded-lg transition-colors">
                    <!-- Maritime crew members working together in unity -->
                    <img src="https://images.unsplash.com/photo-1568602471122-7832951cc4c5?ixlib=rb-4.0.3&auto=format&fit=crop&w=60&h=60" 
                         alt="Maritime crew member" class="w-10 h-10 rounded-full object-cover">
                    <div class="flex-1">
                        <p class="text-sm"><span class="font-medium">Chief Engineer Rodriguez</span> joined nearby</p>
                        <p class="text-xs text-maritime-gray">Currently on MV Atlantic Pioneer • 2.3km away</p>
                        <p class="text-xs text-maritime-gray">2 minutes ago</p>
                    </div>
                    <button class="text-ocean-teal hover:text-deep-blue text-sm font-medium">
                        Say Hello
                    </button>
                </div>

                <div class="flex items-start space-x-3 p-3 hover:bg-gray-50 rounded-lg transition-colors">
                    <!-- International sailors meeting for professional development -->
                    <img src="https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-4.0.3&auto=format&fit=crop&w=60&h=60" 
                         alt="Maritime professional" class="w-10 h-10 rounded-full object-cover">
                    <div class="flex-1">
                        <p class="text-sm"><span class="font-medium">2nd Officer Kim</span> answered a QAAQ question</p>
                        <p class="text-xs text-maritime-gray">"Wartsila 6L32 Generator Power Drop Solutions"</p>
                        <p class="text-xs text-maritime-gray">15 minutes ago</p>
                    </div>
                    <button class="text-ocean-teal hover:text-deep-blue text-sm font-medium">
                        View
                    </button>
                </div>

                <div class="flex items-start space-x-3 p-3 hover:bg-gray-50 rounded-lg transition-colors">
                    <!-- Port community members gathered for maritime industry discussion -->
                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=60&h=60" 
                         alt="Maritime officer" class="w-10 h-10 rounded-full object-cover">
                    <div class="flex-1">
                        <p class="text-sm"><span class="font-medium">Captain Thompson</span> shared experience</p>
                        <p class="text-xs text-maritime-gray">Heavy weather operations in North Atlantic</p>
                        <p class="text-xs text-maritime-gray">1 hour ago</p>
                    </div>
                    <button class="text-ocean-teal hover:text-deep-blue text-sm font-medium">
                        Read
                    </button>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <button class="bg-white rounded-xl shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow text-center">
                <i class="fas fa-question-circle text-ocean-teal text-2xl mb-2"></i>
                <p class="text-sm font-medium text-navy">Ask QAAQ</p>
                <p class="text-xs text-maritime-gray">Get AI answers</p>
            </button>
            
            <button class="bg-white rounded-xl shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow text-center">
                <i class="fas fa-users text-ocean-teal text-2xl mb-2"></i>
                <p class="text-sm font-medium text-navy">Find Groups</p>
                <p class="text-xs text-maritime-gray">Join discussions</p>
            </button>
            
            <button class="bg-white rounded-xl shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow text-center">
                <i class="fas fa-ship text-ocean-teal text-2xl mb-2"></i>
                <p class="text-sm font-medium text-navy">Vessel Info</p>
                <p class="text-xs text-maritime-gray">Share location</p>
            </button>
            
            <button class="bg-white rounded-xl shadow-sm border border-gray-200 p-4 hover:shadow-md transition-shadow text-center">
                <i class="fas fa-handshake text-ocean-teal text-2xl mb-2"></i>
                <p class="text-sm font-medium text-navy">Network</p>
                <p class="text-xs text-maritime-gray">Build connections</p>
            </button>
        </div>

        <!-- Featured Connections -->
        <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-semibold text-navy mb-4 flex items-center">
                <i class="fas fa-star text-ocean-teal mr-2"></i>
                Featured Maritime Professionals
            </h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <!-- Experienced maritime captain with leadership qualities -->
                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&h=200" 
                         alt="Maritime captain" class="w-full h-32 object-cover rounded-lg mb-3">
                    <h4 class="font-semibold text-navy">Captain James Wilson</h4>
                    <p class="text-sm text-maritime-gray mb-2">Master Mariner • 25+ years experience</p>
                    <p class="text-xs text-maritime-gray mb-3">Currently: MV Global Trader • 4.2km away</p>
                    <button class="w-full bg-ocean-teal hover:bg-cyan-600 text-white py-2 rounded-lg text-sm font-medium transition-colors">
                        Connect
                    </button>
                </div>

                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <!-- Maritime engineer working on advanced ship systems -->
                    <img src="https://images.unsplash.com/photo-1507591064344-4c6ce005b128?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&h=200" 
                         alt="Maritime engineer" class="w-full h-32 object-cover rounded-lg mb-3">
                    <h4 class="font-semibold text-navy">Chief Engineer Patel</h4>
                    <p class="text-sm text-maritime-gray mb-2">Marine Engineer • MAN B&W Specialist</p>
                    <p class="text-xs text-maritime-gray mb-3">Currently: MV Pacific Dawn • 1.8km away</p>
                    <button class="w-full bg-ocean-teal hover:bg-cyan-600 text-white py-2 rounded-lg text-sm font-medium transition-colors">
                        Connect
                    </button>
                </div>

                <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                    <!-- Professional maritime officer with international experience -->
                    <img src="https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&h=200" 
                         alt="Maritime officer" class="w-full h-32 object-cover rounded-lg mb-3">
                    <h4 class="font-semibold text-navy">2nd Officer Chen</h4>
                    <p class="text-sm text-maritime-gray mb-2">Navigation Officer • ECDIS Expert</p>
                    <p class="text-xs text-maritime-gray mb-3">Currently: MV Nordic Star • 3.1km away</p>
                    <button class="w-full bg-ocean-teal hover:bg-cyan-600 text-white py-2 rounded-lg text-sm font-medium transition-colors">
                        Connect
                    </button>
                </div>
            </div>
        </div>
    </main>

    <!-- Bottom Navigation -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2">
        <div class="flex items-center justify-around">
            <button class="flex flex-col items-center space-y-1 text-ocean-teal">
                <i class="fas fa-map-marked-alt text-lg"></i>
                <span class="text-xs font-medium">Discover</span>
            </button>
            <button class="flex flex-col items-center space-y-1 text-maritime-gray hover:text-ocean-teal transition-colors">
                <i class="fas fa-comments text-lg"></i>
                <span class="text-xs">Messages</span>
            </button>
            <button class="flex flex-col items-center space-y-1 text-maritime-gray hover:text-ocean-teal transition-colors">
                <i class="fas fa-users text-lg"></i>
                <span class="text-xs">Network</span>
            </button>
            <button class="flex flex-col items-center space-y-1 text-maritime-gray hover:text-ocean-teal transition-colors">
                <i class="fas fa-user text-lg"></i>
                <span class="text-xs">Profile</span>
            </button>
        </div>
    </nav>

    <script>
        // Sample sailor data
        const sailorProfiles = {
            'captain-smith': {
                name: 'Captain Smith',
                position: 'Master Mariner',
                vessel: 'MV Atlantic Explorer',
                distance: '2.3km',
                experience: '22 years',
                nationality: 'British',
                avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=120&h=120'
            },
            'engineer-patel': {
                name: 'Chief Engineer Patel',
                position: 'Chief Engineer',
                vessel: 'MV Pacific Dawn',
                distance: '1.8km',
                experience: '18 years',
                nationality: 'Indian',
                avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=120&h=120'
            },
            'officer-chen': {
                name: '2nd Officer Chen',
                position: '2nd Officer',
                vessel: 'MV Nordic Star',
                distance: '3.1km',
                experience: '12 years',
                nationality: 'Chinese',
                avatar: 'https://images.unsplash.com/photo-1566492031773-4f4e44671d66?ixlib=rb-4.0.3&auto=format&fit=crop&w=120&h=120'
            },
            'chief-martinez': {
                name: 'Chief Martinez',
                position: 'Chief Engineer',
                vessel: 'MV Global Trader',
                distance: '4.2km',
                experience: '25 years',
                nationality: 'Spanish',
                avatar: 'https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&auto=format&fit=crop&w=120&h=120'
            },
            'bosun-olsen': {
                name: 'Bosun Olsen',
                position: 'Bosun',
                vessel: 'MV Scandinavian Pride',
                distance: '5.7km',
                experience: '15 years',
                nationality: 'Norwegian',
                avatar: 'https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?ixlib=rb-4.0.3&auto=format&fit=crop&w=120&h=120'
            },
            'radio-officer-kim': {
                name: 'Radio Officer Kim',
                position: 'Radio Officer',
                vessel: 'MV Seoul Express',
                distance: '6.1km',
                experience: '8 years',
                nationality: 'Korean',
                avatar: 'https://images.unsplash.com/photo-1507591064344-4c6ce005b128?ixlib=rb-4.0.3&auto=format&fit=crop&w=120&h=120'
            }
        };

        let currentRadius = 50;

        function showProfile(sailorId) {
            const sailor = sailorProfiles[sailorId];
            if (!sailor) return;

            const profileCard = document.getElementById('profileCard');
            profileCard.innerHTML = `
                <div class="text-center">
                    <img src="${sailor.avatar}" alt="${sailor.name}" class="w-20 h-20 rounded-full mx-auto mb-4 border-4 border-ocean-teal">
                    <h3 class="text-xl font-bold text-navy mb-1">${sailor.name}</h3>
                    <p class="text-maritime-gray mb-2">${sailor.position}</p>
                    <div class="bg-light-blue rounded-lg p-3 mb-4">
                        <div class="flex items-center justify-between text-sm mb-2">
                            <span class="text-maritime-gray">Vessel:</span>
                            <span class="font-medium text-navy">${sailor.vessel}</span>
                        </div>
                        <div class="flex items-center justify-between text-sm mb-2">
                            <span class="text-maritime-gray">Distance:</span>
                            <span class="font-medium text-navy">${sailor.distance} away</span>
                        </div>
                        <div class="flex items-center justify-between text-sm mb-2">
                            <span class="text-maritime-gray">Experience:</span>
                            <span class="font-medium text-navy">${sailor.experience}</span>
                        </div>
                        <div class="flex items-center justify-between text-sm">
                            <span class="text-maritime-gray">Nationality:</span>
                            <span class="font-medium text-navy">${sailor.nationality}</span>
                        </div>
                    </div>
                    <button class="w-full bg-ocean-teal hover:bg-cyan-600 text-white py-3 rounded-lg font-medium transition-colors mb-2">
                        <i class="fas fa-comments mr-2"></i>
                        Say Hello
                    </button>
                    <button class="w-full bg-gray-100 hover:bg-gray-200 text-navy py-3 rounded-lg font-medium transition-colors">
                        <i class="fas fa-user-plus mr-2"></i>
                        Add to Network
                    </button>
                </div>
            `;

            document.getElementById('profileModal').classList.remove('hidden');
        }

        function closeProfile() {
            document.getElementById('profileModal').classList.add('hidden');
        }

        function toggleFilters() {
            const filterMenu = document.getElementById('filterMenu');
            const chevron = document.getElementById('filterChevron');
            
            if (filterMenu.classList.contains('hidden')) {
                filterMenu.classList.remove('hidden');
                chevron.classList.replace('fa-chevron-down', 'fa-chevron-up');
            } else {
                filterMenu.classList.add('hidden');
                chevron.classList.replace('fa-chevron-up', 'fa-chevron-down');
            }
        }

        function decreaseRadius() {
            if (currentRadius > 1) {
                if (currentRadius <= 10) currentRadius -= 1;
                else if (currentRadius <= 50) currentRadius -= 5;
                else if (currentRadius <= 100) currentRadius -= 10;
                else if (currentRadius <= 500) currentRadius -= 50;
                else currentRadius -= 100;
                
                updateRadiusDisplay();
            }
        }

        function increaseRadius() {
            if (currentRadius < 5000) {
                if (currentRadius < 10) currentRadius += 1;
                else if (currentRadius < 50) currentRadius += 5;
                else if (currentRadius < 100) currentRadius += 10;
                else if (currentRadius < 500) currentRadius += 50;
                else currentRadius += 100;
                
                updateRadiusDisplay();
            }
        }

        function updateRadiusDisplay() {
            document.getElementById('radiusDisplay').textContent = currentRadius + 'km';
            // TODO: Update map view with new radius
        }

        function requestLocation() {
            // TODO: Implement geolocation API
            if (navigator.geolocation) {
                navigator.geolocation.getCurrentPosition(
                    function(position) {
                        console.log('Location obtained:', position.coords);
                        dismissLocationBanner();
                        // TODO: Update map with user location
                    },
                    function(error) {
                        console.error('Geolocation error:', error);
                        // TODO: Show manual location entry form
                    }
                );
            }
        }

        function dismissLocationBanner() {
            document.getElementById('locationBanner').style.display = 'none';
        }

        // Close filter menu when clicking outside
        document.addEventListener('click', function(event) {
            const filterMenu = document.getElementById('filterMenu');
            const filterButton = event.target.closest('[onclick="toggleFilters()"]');
            
            if (!filterButton && !filterMenu.contains(event.target)) {
                filterMenu.classList.add('hidden');
                document.getElementById('filterChevron').classList.replace('fa-chevron-up', 'fa-chevron-down');
            }
        });

        // Initialize map and location on page load
        (function() {
            // TODO: Initialize real map (Leaflet, Google Maps, etc.)
            console.log('Map initialized with 50km radius');
            
            // Auto-request location after a brief delay
            setTimeout(() => {
                if (navigator.geolocation) {
                    navigator.geolocation.getCurrentPosition(
                        function(position) {
                            console.log('Auto location obtained');
                            dismissLocationBanner();
                        },
                        function(error) {
                            console.log('Auto location failed, showing banner');
                        }
                    );
                }
            }, 2000);
        })();
    </script>
</body>
</html>