<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QaaqConnect - Maritime Social Network</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'navy': {
                            50: '#eff6ff',
                            100: '#dbeafe',
                            200: '#bfdbfe',
                            300: '#93c5fd',
                            400: '#60a5fa',
                            500: '#3b82f6',
                            600: '#2563eb',
                            700: '#1d4ed8',
                            800: '#1e3a8a',
                            900: '#1e40af'
                        },
                        'ocean': {
                            50: '#ecfeff',
                            100: '#cffafe',
                            200: '#a5f3fc',
                            300: '#67e8f9',
                            400: '#22d3ee',
                            500: '#06b6d4',
                            600: '#0891b2',
                            700: '#0e7490',
                            800: '#155e75',
                            900: '#164e63'
                        }
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        .map-transition {
            transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
        }
        .muted-map {
            filter: grayscale(100%) brightness(0.8);
        }
        .active-map {
            filter: grayscale(0%) brightness(1);
        }
        .pulse-button {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.05); }
        }
        .sailor-pin {
            transition: all 0.3s ease;
        }
        .sailor-pin.muted {
            filter: grayscale(100%);
            opacity: 0.6;
        }
        .sailor-pin.active {
            filter: grayscale(0%);
            opacity: 1;
        }
    </style>
</head>
<body class="bg-slate-50 font-sans">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-slate-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-3">
                    <div class="bg-navy-800 p-2 rounded-lg">
                        <i class="fas fa-anchor text-white text-xl"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-navy-800">QaaqConnect</h1>
                        <p class="text-xs text-slate-500">Maritime Social Network</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="p-2 text-slate-600 hover:text-navy-800 transition-colors">
                        <i class="fas fa-bell text-lg"></i>
                    </button>
                    <div class="w-8 h-8 bg-ocean-600 rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-white text-sm"></i>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="relative">
        <!-- Hero Section with Map -->
        <div class="relative h-screen bg-slate-100">
            <!-- Map Container -->
            <div id="mapContainer" class="absolute inset-0 map-transition muted-map">
                <!-- A panoramic view of a busy maritime port with ships and cranes in muted tones -->
                <div class="w-full h-full bg-cover bg-center relative" 
                     style="background-image: url('https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1920&h=1080');">
                    
                    <!-- Map Overlay -->
                    <div class="absolute inset-0 bg-slate-400 bg-opacity-40"></div>
                    
                    <!-- Sailor Pins -->
                    <div class="absolute inset-0">
                        <!-- Pin 1 -->
                        <div class="sailor-pin muted absolute top-1/4 left-1/3 transform -translate-x-1/2 -translate-y-1/2">
                            <div class="bg-navy-800 p-3 rounded-full shadow-lg border-4 border-white">
                                <i class="fas fa-user text-white text-sm"></i>
                            </div>
                            <div class="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-l-transparent border-r-transparent border-t-navy-800"></div>
                        </div>
                        
                        <!-- Pin 2 -->
                        <div class="sailor-pin muted absolute top-2/3 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                            <div class="bg-ocean-600 p-3 rounded-full shadow-lg border-4 border-white">
                                <i class="fas fa-user text-white text-sm"></i>
                            </div>
                            <div class="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-l-transparent border-r-transparent border-t-ocean-600"></div>
                        </div>
                        
                        <!-- Pin 3 -->
                        <div class="sailor-pin muted absolute top-1/2 left-2/3 transform -translate-x-1/2 -translate-y-1/2">
                            <div class="bg-orange-500 p-3 rounded-full shadow-lg border-4 border-white">
                                <i class="fas fa-user text-white text-sm"></i>
                            </div>
                            <div class="absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-l-transparent border-r-transparent border-t-orange-500"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Search Overlay -->
            <div class="absolute inset-0 flex flex-col items-center justify-center z-10 px-4">
                <div class="bg-white rounded-2xl shadow-2xl p-8 max-w-md w-full mx-4">
                    <div class="text-center mb-6">
                        <div class="bg-navy-800 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                            <i class="fas fa-compass text-white text-2xl"></i>
                        </div>
                        <h2 class="text-2xl font-bold text-navy-800 mb-2">Find Your Crew</h2>
                        <p class="text-slate-600">Connect with fellow sailors in your area</p>
                    </div>
                    
                    <!-- Location Input -->
                    <div class="mb-6">
                        <div class="relative">
                            <i class="fas fa-map-marker-alt absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400"></i>
                            <input type="text" placeholder="Current location (50km radius)" 
                                   class="w-full pl-10 pr-4 py-3 border border-slate-300 rounded-lg focus:ring-2 focus:ring-ocean-500 focus:border-ocean-500 outline-none">
                        </div>
                    </div>
                    
                    <!-- Main CTA Button -->
                    <button id="activateMapBtn" class="w-full bg-gradient-to-r from-navy-800 to-ocean-600 text-white py-4 rounded-lg font-semibold text-lg pulse-button hover:from-navy-900 hover:to-ocean-700 transition-all duration-300 shadow-lg">
                        🔍 1234 koi hai?
                    </button>
                    
                    <p class="text-center text-sm text-slate-500 mt-3">Discover sailors near you</p>
                </div>
            </div>

            <!-- Zoom Controls -->
            <div class="absolute bottom-24 right-4 z-20 flex flex-col space-y-2">
                <button id="zoomIn" class="bg-white p-3 rounded-lg shadow-lg text-slate-600 hover:text-navy-800 transition-colors disabled:opacity-50" disabled>
                    <i class="fas fa-plus"></i>
                </button>
                <button id="zoomOut" class="bg-white p-3 rounded-lg shadow-lg text-slate-600 hover:text-navy-800 transition-colors disabled:opacity-50" disabled>
                    <i class="fas fa-minus"></i>
                </button>
            </div>
        </div>

        <!-- Activity Feed Section -->
        <div id="activityFeed" class="hidden max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="mb-8">
                <h3 class="text-2xl font-bold text-navy-800 mb-2">Recent Activities</h3>
                <p class="text-slate-600">See what your maritime community is up to</p>
            </div>

            <div class="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                <!-- Activity Card 1 -->
                <div class="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden hover:shadow-md transition-shadow">
                    <!-- Sailors enjoying shore leave activities at a waterfront restaurant -->
                    <img src="https://pixabay.com/get/g7fd89b9a90b1a6d4027d2e63154291973b43ce84fad6ca7e84c931b04d67546f58ad38d24b7c0daa087a8bef06208a415b567ae80ff1a639841039d6c7e21577_1280.jpg" 
                         alt="Sailors dining at waterfront restaurant" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <div class="flex items-center mb-3">
                            <div class="w-10 h-10 bg-navy-800 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-user text-white text-sm"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold text-navy-800">Captain Piyush</h4>
                                <p class="text-sm text-slate-500">2 hours ago • Mumbai Port</p>
                            </div>
                        </div>
                        <p class="text-slate-700 mb-4">Great shore leave dinner with the crew! Who else is in Mumbai port this week? 🍽️⚓</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <button class="flex items-center space-x-1 text-slate-500 hover:text-ocean-600 transition-colors">
                                    <span>🦆</span>
                                    <span class="text-sm">12</span>
                                </button>
                                <button class="flex items-center space-x-1 text-slate-500 hover:text-ocean-600 transition-colors">
                                    <i class="fas fa-comment text-sm"></i>
                                    <span class="text-sm">5</span>
                                </button>
                            </div>
                            <button class="text-slate-500 hover:text-ocean-600 transition-colors">
                                <i class="fas fa-share text-sm"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Activity Card 2 -->
                <div class="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden hover:shadow-md transition-shadow">
                    <!-- Maritime crew socializing at a harbor bar with ships in background -->
                    <img src="https://pixabay.com/get/g5c9f64934f3eacf24add8e956d3320c069ed32bb608f83c83c23aef2ff3a6e4ca4b82ad9cd33dac09951df9c8cfaf63948f7e4c7cc5246eaf4f8ab428eb16669_1280.jpg" 
                         alt="Maritime crew socializing at harbor bar" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <div class="flex items-center mb-3">
                            <div class="w-10 h-10 bg-ocean-600 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-user text-white text-sm"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold text-navy-800">Chief Engineer Rahman</h4>
                                <p class="text-sm text-slate-500">4 hours ago • Singapore Port</p>
                            </div>
                        </div>
                        <p class="text-slate-700 mb-4">QAAQ question: Anyone familiar with Alfa Laval MAPX 207 purifier issues? Need help with vibration problems 🔧</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <button class="flex items-center space-x-1 text-slate-500 hover:text-ocean-600 transition-colors">
                                    <span>🦆</span>
                                    <span class="text-sm">8</span>
                                </button>
                                <button class="flex items-center space-x-1 text-slate-500 hover:text-ocean-600 transition-colors">
                                    <i class="fas fa-comment text-sm"></i>
                                    <span class="text-sm">15</span>
                                </button>
                            </div>
                            <button class="text-slate-500 hover:text-ocean-600 transition-colors">
                                <i class="fas fa-share text-sm"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Activity Card 3 -->
                <div class="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden hover:shadow-md transition-shadow">
                    <!-- Sailors exploring a coastal city with historic maritime architecture -->
                    <img src="https://pixabay.com/get/g0b514a9c0ce61449fe1ed0058425e43bed5a4e9cf055c7cc8426cdc3ecc09d92630bebf80e312135bb2266751feab9d60aa0d3e6685e11975b838fdafdf71240_1280.jpg" 
                         alt="Sailors exploring coastal city" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <div class="flex items-center mb-3">
                            <div class="w-10 h-10 bg-orange-500 rounded-full flex items-center justify-center mr-3">
                                <i class="fas fa-user text-white text-sm"></i>
                            </div>
                            <div>
                                <h4 class="font-semibold text-navy-800">2nd Officer Maria</h4>
                                <p class="text-sm text-slate-500">6 hours ago • Barcelona Port</p>
                            </div>
                        </div>
                        <p class="text-slate-700 mb-4">Exploring Barcelona's maritime quarter! Join me for coffee if you're in port ☕🏛️</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <button class="flex items-center space-x-1 text-slate-500 hover:text-ocean-600 transition-colors">
                                    <span>🦆</span>
                                    <span class="text-sm">24</span>
                                </button>
                                <button class="flex items-center space-x-1 text-slate-500 hover:text-ocean-600 transition-colors">
                                    <i class="fas fa-comment text-sm"></i>
                                    <span class="text-sm">7</span>
                                </button>
                            </div>
                            <button class="text-slate-500 hover:text-ocean-600 transition-colors">
                                <i class="fas fa-share text-sm"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- QAAQ Integration Section -->
        <div id="qaaqSection" class="hidden bg-navy-800 text-white py-16">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-12">
                    <h3 class="text-3xl font-bold mb-4">Powered by QAAQ AI</h3>
                    <p class="text-navy-200 text-lg">Your maritime knowledge companion</p>
                </div>
                
                <div class="bg-navy-700 rounded-2xl p-8 max-w-4xl mx-auto">
                    <div class="flex items-start space-x-4 mb-6">
                        <div class="bg-ocean-600 p-3 rounded-full flex-shrink-0">
                            <i class="fas fa-robot text-white text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <div class="bg-navy-600 rounded-2xl p-4 mb-4">
                                <p class="text-navy-100">QAAQ hai na. That is a nice question about the Alfa Laval purifier. Let me help you troubleshoot this vibration issue...</p>
                            </div>
                            <div class="flex space-x-2">
                                <button class="bg-navy-600 hover:bg-navy-500 px-4 py-2 rounded-lg text-sm transition-colors">
                                    <i class="fas fa-thumbs-up mr-1"></i> Helpful
                                </button>
                                <button class="bg-navy-600 hover:bg-navy-500 px-4 py-2 rounded-lg text-sm transition-colors">
                                    <i class="fas fa-share mr-1"></i> Share
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="border-t border-navy-600 pt-6">
                        <h4 class="font-semibold mb-4">Related QAAQ Topics:</h4>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                            <a href="#" class="bg-navy-600 hover:bg-navy-500 p-4 rounded-lg transition-colors block">
                                <h5 class="font-medium mb-2">🔧 Main Propulsion</h5>
                                <p class="text-sm text-navy-200">Engine troubleshooting & maintenance</p>
                            </a>
                            <a href="#" class="bg-navy-600 hover:bg-navy-500 p-4 rounded-lg transition-colors block">
                                <h5 class="font-medium mb-2">⚙️ Purifiers</h5>
                                <p class="text-sm text-navy-200">Alfa Laval & other purifier systems</p>
                            </a>
                            <a href="#" class="bg-navy-600 hover:bg-navy-500 p-4 rounded-lg transition-colors block">
                                <h5 class="font-medium mb-2">🌊 BWTS Systems</h5>
                                <p class="text-sm text-navy-200">Ballast water treatment solutions</p>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- CPSS Groups Section -->
        <div id="cpssSection" class="hidden bg-slate-50 py-16">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-12">
                    <h3 class="text-3xl font-bold text-navy-800 mb-4">CPSS Groups</h3>
                    <p class="text-slate-600 text-lg">Connect with Port-Specific Sailor Communities</p>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                    <!-- Port leisure experiences at a modern marina with yachts -->
                    <div class="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden hover:shadow-md transition-shadow">
                        <img src="https://images.unsplash.com/photo-1544551763-46a013bb70d5?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=400&h=300" 
                             alt="Modern marina with yachts" class="w-full h-32 object-cover">
                        <div class="p-4">
                            <h4 class="font-semibold text-navy-800 mb-2">Mumbai Port</h4>
                            <p class="text-sm text-slate-600 mb-3">127 active sailors</p>
                            <button class="w-full bg-ocean-600 text-white py-2 rounded-lg text-sm hover:bg-ocean-700 transition-colors">
                                Join Group
                            </button>
                        </div>
                    </div>
                    
                    <!-- Port leisure experiences at a busy container terminal -->
                    <div class="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden hover:shadow-md transition-shadow">
                        <img src="https://images.unsplash.com/photo-1605649487212-47bdab064df7?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=400&h=300" 
                             alt="Busy container terminal" class="w-full h-32 object-cover">
                        <div class="p-4">
                            <h4 class="font-semibold text-navy-800 mb-2">Singapore Port</h4>
                            <p class="text-sm text-slate-600 mb-3">89 active sailors</p>
                            <button class="w-full bg-ocean-600 text-white py-2 rounded-lg text-sm hover:bg-ocean-700 transition-colors">
                                Join Group
                            </button>
                        </div>
                    </div>
                    
                    <!-- Port leisure experiences at a traditional fishing harbor -->
                    <div class="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden hover:shadow-md transition-shadow">
                        <img src="https://pixabay.com/get/g40a900225793a16001283bc163cc867da92c5632df1df58bcf6a9dcad54a9b5f5716577e458d3d3d99afecd55d39fbb5fa61706d3658be29f17677a3fe8485c9_1280.jpg" 
                             alt="Traditional fishing harbor" class="w-full h-32 object-cover">
                        <div class="p-4">
                            <h4 class="font-semibold text-navy-800 mb-2">Barcelona Port</h4>
                            <p class="text-sm text-slate-600 mb-3">156 active sailors</p>
                            <button class="w-full bg-ocean-600 text-white py-2 rounded-lg text-sm hover:bg-ocean-700 transition-colors">
                                Join Group
                            </button>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden hover:shadow-md transition-shadow">
                        <div class="w-full h-32 bg-gradient-to-br from-navy-800 to-ocean-600 flex items-center justify-center">
                            <i class="fas fa-plus text-white text-3xl"></i>
                        </div>
                        <div class="p-4">
                            <h4 class="font-semibold text-navy-800 mb-2">Create Group</h4>
                            <p class="text-sm text-slate-600 mb-3">Start your port community</p>
                            <button class="w-full bg-slate-600 text-white py-2 rounded-lg text-sm hover:bg-slate-700 transition-colors">
                                Create New
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Bottom Navigation (Mobile) -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-slate-200 z-50 md:hidden">
        <div class="flex items-center justify-around py-2">
            <button class="flex flex-col items-center p-2 text-ocean-600">
                <i class="fas fa-compass text-lg"></i>
                <span class="text-xs mt-1">Discover</span>
            </button>
            <button class="flex flex-col items-center p-2 text-slate-500">
                <i class="fas fa-users text-lg"></i>
                <span class="text-xs mt-1">Groups</span>
            </button>
            <button class="flex flex-col items-center p-2 text-slate-500">
                <i class="fas fa-question-circle text-lg"></i>
                <span class="text-xs mt-1">QAAQ</span>
            </button>
            <button class="flex flex-col items-center p-2 text-slate-500">
                <i class="fas fa-user text-lg"></i>
                <span class="text-xs mt-1">Profile</span>
            </button>
        </div>
    </nav>

    <script>
        (function() {
            let isMapActive = false;
            
            const mapContainer = document.getElementById('mapContainer');
            const activateBtn = document.getElementById('activateMapBtn');
            const zoomInBtn = document.getElementById('zoomIn');
            const zoomOutBtn = document.getElementById('zoomOut');
            const sailorPins = document.querySelectorAll('.sailor-pin');
            const activityFeed = document.getElementById('activityFeed');
            const qaaqSection = document.getElementById('qaaqSection');
            const cpssSection = document.getElementById('cpssSection');
            
            function activateMap() {
                isMapActive = true;
                
                // Remove muted state and add active state
                mapContainer.classList.remove('muted-map');
                mapContainer.classList.add('active-map');
                
                // Activate sailor pins
                sailorPins.forEach((pin, index) => {
                    setTimeout(() => {
                        pin.classList.remove('muted');
                        pin.classList.add('active');
                    }, index * 200);
                });
                
                // Enable zoom controls
                zoomInBtn.disabled = false;
                zoomOutBtn.disabled = false;
                zoomInBtn.classList.remove('disabled:opacity-50');
                zoomOutBtn.classList.remove('disabled:opacity-50');
                
                // Update button text
                activateBtn.innerHTML = '<i class="fas fa-search mr-2"></i>Searching...';
                activateBtn.disabled = true;
                
                // Show sections after delay
                setTimeout(() => {
                    activityFeed.classList.remove('hidden');
                    qaaqSection.classList.remove('hidden');
                    cpssSection.classList.remove('hidden');
                    
                    // Update button to show results
                    activateBtn.innerHTML = '<i class="fas fa-check mr-2"></i>3 sailors found nearby';
                    activateBtn.classList.remove('pulse-button');
                    activateBtn.classList.add('bg-emerald-500', 'hover:bg-emerald-600');
                    activateBtn.classList.remove('bg-gradient-to-r', 'from-navy-800', 'to-ocean-600', 'hover:from-navy-900', 'hover:to-ocean-700');
                }, 1500);
            }
            
            // Event listeners
            activateBtn.addEventListener('click', activateMap);
            
            // Zoom controls (placeholder functionality)
            zoomInBtn.addEventListener('click', function() {
                if (!isMapActive) return;
                // TODO: Implement zoom in functionality
                console.log('Zoom in');
            });
            
            zoomOutBtn.addEventListener('click', function() {
                if (!isMapActive) return;
                // TODO: Implement zoom out functionality  
                console.log('Zoom out');
            });
            
            // Add click handlers for sailor pins
            sailorPins.forEach((pin, index) => {
                pin.addEventListener('click', function() {
                    if (!isMapActive) return;
                    
                    // TODO: Show sailor profile or chat interface
                    console.log(`Clicked sailor pin ${index + 1}`);
                    
                    // Visual feedback
                    pin.style.transform = 'scale(1.1)';
                    setTimeout(() => {
                        pin.style.transform = 'scale(1)';
                    }, 200);
                });
            });
            
            // Add smooth scrolling for anchor links
            document.addEventListener('click', function(e) {
                if (e.target.tagName === 'A' && e.target.getAttribute('href')?.startsWith('#')) {
                    e.preventDefault();
                    const targetId = e.target.getAttribute('href').substring(1);
                    const targetElement = document.getElementById(targetId);
                    if (targetElement) {
                        targetElement.scrollIntoView({ behavior: 'smooth' });
                    }
                }
            });
        })();
    </script>
</body>
</html>