<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QaaqConnect - Maritime Community</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'navy': '#1B365D',
                        'ocean': '#20B2AA',
                        'ocean-light': '#7ECECA',
                        'grey-reg': '#F5F5F5',
                        'warm-grey': '#666666'
                    },
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif']
                    }
                }
            }
        };
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="font-inter bg-white">

    <!-- @COMPONENT: App Container -->
    <!-- @STATE: currentScreen:string = 'registration' -->
    <!-- @STATE: isRegistered:boolean = false -->
    <!-- @STATE: userType:string = '' -->
    
    <div id="app-container" class="min-h-screen">
        
        <!-- Registration Screen -->
        <div id="registration-screen" class="min-h-screen bg-grey-reg">
            <!-- @COMPONENT: RegistrationHeader -->
            <div class="bg-white shadow-sm px-4 py-6">
                <div class="text-center">
                    <div class="flex items-center justify-center mb-2">
                        <i class="fas fa-anchor text-navy text-3xl mr-2"></i>
                        <h1 class="text-2xl font-bold text-navy">QaaqConnect</h1>
                    </div>
                    <p class="text-warm-grey text-sm">Maritime Community Platform</p>
                </div>
            </div>
            
            <!-- Registration Form -->
            <div class="px-6 py-8">
                <div class="text-center mb-8">
                    <h2 class="text-3xl font-bold text-navy mb-2">Ahoy! Join the crew 🚢</h2>
                    <p class="text-warm-grey">Connect with sailors and locals worldwide</p>
                </div>
                
                <!-- @COMPONENT: QuickSignupForm -->
                <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
                    <form data-event="submit:handleRegistration" data-implementation="Should validate and create user account">
                        <!-- Name Field -->
                        <div class="mb-4">
                            <label class="block text-navy font-medium mb-2">Name/Nickname</label>
                            <input 
                                type="text" 
                                placeholder="Captain Jack"
                                data-bind="user.name"
                                class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-ocean focus:border-transparent transition-all"
                                data-mock="true"
                            >
                        </div>
                        
                        <!-- Contact Field -->
                        <div class="mb-4">
                            <label class="block text-navy font-medium mb-2">Phone/Email</label>
                            <input 
                                type="text" 
                                placeholder="****** 567 8900 or <EMAIL>"
                                data-bind="user.contact"
                                class="w-full px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-ocean focus:border-transparent transition-all"
                                data-mock="true"
                            >
                        </div>
                        
                        <!-- User Type Selection -->
                        <div class="mb-6">
                            <label class="block text-navy font-medium mb-3">I'm a:</label>
                            <div class="grid grid-cols-2 gap-3">
                                <button 
                                    type="button"
                                    data-event="click:selectUserType"
                                    data-implementation="Toggle sailor selection"
                                    class="user-type-btn p-4 border-2 border-gray-200 rounded-lg text-center transition-all hover:border-ocean"
                                    data-type="sailor"
                                >
                                    <i class="fas fa-ship text-2xl text-navy mb-2"></i>
                                    <div class="font-medium text-navy">Sailor 🚢</div>
                                    <div class="text-sm text-warm-grey">Maritime Professional</div>
                                </button>
                                <button 
                                    type="button"
                                    data-event="click:selectUserType"
                                    data-implementation="Toggle local selection"
                                    class="user-type-btn p-4 border-2 border-gray-200 rounded-lg text-center transition-all hover:border-ocean"
                                    data-type="local"
                                >
                                    <i class="fas fa-home text-2xl text-navy mb-2"></i>
                                    <div class="font-medium text-navy">Local 🏠</div>
                                    <div class="text-sm text-warm-grey">Port City Resident</div>
                                </button>
                            </div>
                        </div>
                        
                        <!-- Submit Button -->
                        <button 
                            type="submit"
                            data-event="click:handleRegistration"
                            data-implementation="Should validate form and register user"
                            class="w-full bg-ocean hover:bg-ocean-light text-white font-bold py-4 rounded-lg transition-all transform hover:scale-105 shadow-lg"
                        >
                            Let's Go! ⚓
                        </button>
                    </form>
                </div>
                
                <!-- QAAQ Integration Link -->
                <div class="bg-white rounded-xl shadow-lg p-4 text-center">
                    <p class="text-sm text-warm-grey mb-2">Already using QAAQ?</p>
                    <button 
                        data-event="click:connectQAAQ"
                        data-implementation="Link to existing QAAQ account"
                        class="text-ocean font-medium hover:underline"
                    >
                        Connect your QAAQ account →
                    </button>
                </div>
            </div>
        </div>
        <!-- @END_COMPONENT: RegistrationScreen -->
        
        <!-- Discovery Home Screen -->
        <div id="home-screen" class="min-h-screen bg-white hidden">
            <!-- @COMPONENT: HomeHeader -->
            <div class="bg-gradient-to-r from-navy to-ocean text-white px-4 py-6">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center">
                        <div class="w-10 h-10 bg-white rounded-full flex items-center justify-center mr-3">
                            <i class="fas fa-anchor text-navy"></i>
                        </div>
                        <div>
                            <h1 class="font-bold text-lg">Welcome back!</h1>
                            <p class="text-sm opacity-90" data-bind="user.name">Captain Jack</p>
                        </div>
                    </div>
                    <button 
                        data-event="click:openProfile"
                        data-implementation="Open user profile settings"
                        class="w-10 h-10 bg-white bg-opacity-20 rounded-full flex items-center justify-center"
                    >
                        <i class="fas fa-user text-white"></i>
                    </button>
                </div>
                
                <!-- Location Prompt -->
                <div class="bg-white bg-opacity-20 rounded-lg p-4">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="font-medium">Explore like a local 🗺️</h3>
                            <p class="text-sm opacity-90">Discover your port city</p>
                        </div>
                        <button 
                            data-event="click:activateDiscovery"
                            data-implementation="Activate discovery mode and show nearby connections"
                            class="bg-white text-navy px-4 py-2 rounded-lg font-medium hover:bg-gray-50 transition-all"
                        >
                            1234 koi hai
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Map Section -->
            <div class="relative">
                <!-- A panoramic view of a busy port city with ships and local activities -->
                <div 
                    class="h-64 bg-cover bg-center relative"
                    style="background-image: url('https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&h=600');"
                >
                    <div class="absolute inset-0 bg-black bg-opacity-30"></div>
                    <div class="absolute bottom-4 left-4 text-white">
                        <h3 class="font-bold text-lg">Your Port: Mumbai</h3>
                        <p class="text-sm opacity-90">23 sailors • 45 locals nearby</p>
                    </div>
                    
                    <!-- Location Pins -->
                    <div class="absolute top-1/2 left-1/3 transform -translate-x-1/2 -translate-y-1/2">
                        <div class="w-8 h-8 bg-ocean rounded-full border-3 border-white shadow-lg flex items-center justify-center animate-pulse">
                            <i class="fas fa-ship text-white text-xs"></i>
                        </div>
                    </div>
                    <div class="absolute top-2/3 right-1/3 transform -translate-x-1/2 -translate-y-1/2">
                        <div class="w-8 h-8 bg-navy rounded-full border-3 border-white shadow-lg flex items-center justify-center">
                            <i class="fas fa-home text-white text-xs"></i>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Quick Actions -->
            <div class="px-4 py-6">
                <h3 class="font-bold text-navy text-lg mb-4">Quick Connect</h3>
                <div class="grid grid-cols-2 gap-4 mb-6">
                    <button 
                        data-event="click:findSailors"
                        data-implementation="Show nearby sailors for networking"
                        class="bg-ocean text-white p-4 rounded-xl text-center hover:bg-ocean-light transition-all"
                    >
                        <i class="fas fa-users text-2xl mb-2"></i>
                        <div class="font-medium">Find Sailors</div>
                        <div class="text-sm opacity-90">23 nearby</div>
                    </button>
                    <button 
                        data-event="click:findLocals"
                        data-implementation="Show local guides and residents"
                        class="bg-navy text-white p-4 rounded-xl text-center hover:bg-blue-900 transition-all"
                    >
                        <i class="fas fa-map-marker-alt text-2xl mb-2"></i>
                        <div class="font-medium">Local Guides</div>
                        <div class="text-sm opacity-90">45 available</div>
                    </button>
                </div>
                
                <!-- Featured Connections -->
                <h3 class="font-bold text-navy text-lg mb-4">Nearby Connections</h3>
                
                <!-- @MAP: nearbyUsers.map(user => ( -->
                <div class="space-y-3">
                    <!-- Connection Card 1 -->
                    <div class="bg-white border border-gray-200 rounded-xl p-4 shadow-sm">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <!-- Portrait of a friendly sailor in uniform at a port -->
                                <img 
                                    src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&h=100" 
                                    alt="Sailor profile" 
                                    class="w-12 h-12 rounded-full object-cover mr-3"
                                    data-mock-image="true"
                                >
                                <div>
                                    <h4 class="font-medium text-navy" data-mock="true">Captain Marina</h4>
                                    <p class="text-sm text-warm-grey">2nd Engineer • 0.3 km away</p>
                                    <div class="flex items-center mt-1">
                                        <i class="fas fa-star text-yellow-400 text-xs mr-1"></i>
                                        <span class="text-xs text-warm-grey">4.9 • 15 connections</span>
                                    </div>
                                </div>
                            </div>
                            <button 
                                data-event="click:connectWithUser"
                                data-implementation="Send connection request to user"
                                class="bg-ocean text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-ocean-light transition-all"
                            >
                                Connect
                            </button>
                        </div>
                    </div>
                    
                    <!-- Connection Card 2 -->
                    <div class="bg-white border border-gray-200 rounded-xl p-4 shadow-sm">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <!-- Portrait of a local guide showing tourist sites -->
                                <img 
                                    src="https://pixabay.com/get/gf449ee2a52eb8ef352fea61d536ffc3132d344ae0f98848ef6b15161614176796649930042ad467dfbcf20b008a8db4240cfe46d5625bea4591a9c7111846cd7_1280.jpg" 
                                    alt="Local guide profile" 
                                    class="w-12 h-12 rounded-full object-cover mr-3"
                                    data-mock-image="true"
                                >
                                <div>
                                    <h4 class="font-medium text-navy" data-mock="true">Priya Local</h4>
                                    <p class="text-sm text-warm-grey">Port City Guide • 0.5 km away</p>
                                    <div class="flex items-center mt-1">
                                        <i class="fas fa-star text-yellow-400 text-xs mr-1"></i>
                                        <span class="text-xs text-warm-grey">4.8 • 32 tours guided</span>
                                    </div>
                                </div>
                            </div>
                            <button 
                                data-event="click:connectWithUser"
                                data-implementation="Send connection request to user"
                                class="bg-navy text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-900 transition-all"
                            >
                                Connect
                            </button>
                        </div>
                    </div>
                    
                    <!-- Connection Card 3 -->
                    <div class="bg-white border border-gray-200 rounded-xl p-4 shadow-sm">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center">
                                <!-- Portrait of maritime professionals networking -->
                                <img 
                                    src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&h=100" 
                                    alt="Maritime professional profile" 
                                    class="w-12 h-12 rounded-full object-cover mr-3"
                                    data-mock-image="true"
                                >
                                <div>
                                    <h4 class="font-medium text-navy" data-mock="true">Chief Engineer Raja</h4>
                                    <p class="text-sm text-warm-grey">Chief Engineer • 0.8 km away</p>
                                    <div class="flex items-center mt-1">
                                        <i class="fas fa-star text-yellow-400 text-xs mr-1"></i>
                                        <span class="text-xs text-warm-grey">5.0 • 28 connections</span>
                                    </div>
                                </div>
                            </div>
                            <button 
                                data-event="click:connectWithUser"
                                data-implementation="Send connection request to user"
                                class="bg-ocean text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-ocean-light transition-all"
                            >
                                Connect
                            </button>
                        </div>
                    </div>
                </div>
                <!-- @END_MAP )) -->
            </div>
        </div>
        <!-- @END_COMPONENT: HomeScreen -->
        
        <!-- Discover Activities Screen -->
        <div id="activities-screen" class="min-h-screen bg-white hidden">
            <!-- @COMPONENT: ActivitiesHeader -->
            <div class="bg-white shadow-sm px-4 py-4 sticky top-0 z-10">
                <div class="flex items-center">
                    <button 
                        data-event="click:goBack"
                        data-implementation="Navigate back to previous screen"
                        class="mr-3 p-2"
                    >
                        <i class="fas fa-arrow-left text-navy"></i>
                    </button>
                    <h1 class="font-bold text-lg text-navy">Discover Activities</h1>
                </div>
            </div>
            
            <div class="px-4 py-6">
                <!-- Activity Categories -->
                <div class="grid grid-cols-2 gap-4 mb-6">
                    <!-- Port Exploration -->
                    <div class="bg-gradient-to-br from-ocean to-ocean-light rounded-xl p-4 text-white">
                        <!-- Sailors exploring historic port architecture -->
                        <div 
                            class="h-24 bg-cover bg-center rounded-lg mb-3"
                            style="background-image: url('https://pixabay.com/get/g35d092b3a73b8a57f6209d1aa2655287203861bc4690b9c66a1ccf055ff82c904a4241fef9e953e6faad92f761e1bcf9f18f1bab52918abbf928f5a6d9316c84_1280.jpg');"
                        ></div>
                        <h3 class="font-bold mb-1">Port Tours</h3>
                        <p class="text-sm opacity-90">Explore maritime heritage</p>
                    </div>
                    
                    <!-- Local Culture -->
                    <div class="bg-gradient-to-br from-navy to-blue-900 rounded-xl p-4 text-white">
                        <!-- Local cultural activities in a port city -->
                        <div 
                            class="h-24 bg-cover bg-center rounded-lg mb-3"
                            style="background-image: url('https://pixabay.com/get/gb98cd94b1ff84cbb9e4cb321ef79fc95f1e258566b4a4686a8f36e13a2e4005e0db6e968366202d099438c9e37057fc945ce63a7c5f28d570fda9bd3bf019b0f_1280.jpg');"
                        ></div>
                        <h3 class="font-bold mb-1">Local Culture</h3>
                        <p class="text-sm opacity-90">Experience the city</p>
                    </div>
                </div>
                
                <!-- Recommended Activities -->
                <h3 class="font-bold text-navy text-lg mb-4">Recommended for You</h3>
                
                <!-- Activity Cards -->
                <div class="space-y-4">
                    <!-- Activity 1 -->
                    <div class="bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden">
                        <!-- Maritime community meetup at harbor -->
                        <div 
                            class="h-40 bg-cover bg-center"
                            style="background-image: url('https://images.unsplash.com/photo-1544725176-7c40e5a71c5e?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400');"
                        ></div>
                        <div class="p-4">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="font-bold text-navy" data-mock="true">Maritime Professionals Meetup</h4>
                                <span class="bg-ocean text-white text-xs px-2 py-1 rounded-full">Tonight</span>
                            </div>
                            <p class="text-warm-grey text-sm mb-3" data-mock="true">Network with fellow maritime professionals at the harbor bar. Share experiences and make new connections.</p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center text-sm text-warm-grey">
                                    <i class="fas fa-map-marker-alt mr-1"></i>
                                    <span data-mock="true">Harbor View Bar • 1.2 km</span>
                                </div>
                                <button 
                                    data-event="click:joinActivity"
                                    data-implementation="Join or express interest in activity"
                                    class="bg-ocean text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-ocean-light transition-all"
                                >
                                    Join
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Activity 2 -->
                    <div class="bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden">
                        <!-- Port city discovery experience -->
                        <div 
                            class="h-40 bg-cover bg-center"
                            style="background-image: url('https://images.unsplash.com/photo-1506905925346-21bda4d32df4?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400');"
                        ></div>
                        <div class="p-4">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="font-bold text-navy" data-mock="true">Historic Port Walking Tour</h4>
                                <span class="bg-navy text-white text-xs px-2 py-1 rounded-full">Tomorrow</span>
                            </div>
                            <p class="text-warm-grey text-sm mb-3" data-mock="true">Discover the rich maritime history of Mumbai port with a local guide. Perfect for sailors exploring the city.</p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center text-sm text-warm-grey">
                                    <i class="fas fa-clock mr-1"></i>
                                    <span data-mock="true">2 hours • ₹500</span>
                                </div>
                                <button 
                                    data-event="click:bookActivity"
                                    data-implementation="Book or reserve activity spot"
                                    class="bg-navy text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-900 transition-all"
                                >
                                    Book
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Activity 3 -->
                    <div class="bg-white border border-gray-200 rounded-xl shadow-sm overflow-hidden">
                        <!-- Local discovery activities in port area -->
                        <div 
                            class="h-40 bg-cover bg-center"
                            style="background-image: url('https://images.unsplash.com/photo-1540959733332-eab4deabeeaf?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400');"
                        ></div>
                        <div class="p-4">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="font-bold text-navy" data-mock="true">Local Food & Market Tour</h4>
                                <span class="bg-green-500 text-white text-xs px-2 py-1 rounded-full">Popular</span>
                            </div>
                            <p class="text-warm-grey text-sm mb-3" data-mock="true">Taste authentic local cuisine and explore traditional markets near the port. A cultural immersion experience.</p>
                            <div class="flex items-center justify-between">
                                <div class="flex items-center text-sm text-warm-grey">
                                    <i class="fas fa-users mr-1"></i>
                                    <span data-mock="true">8 people joined • ₹750</span>
                                </div>
                                <button 
                                    data-event="click:joinActivity"
                                    data-implementation="Join group activity"
                                    class="bg-green-500 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-green-600 transition-all"
                                >
                                    Join Group
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- @END_COMPONENT: ActivitiesScreen -->
        
        <!-- QAAQ Services Screen -->
        <div id="services-screen" class="min-h-screen bg-white hidden">
            <!-- @COMPONENT: ServicesHeader -->
            <div class="bg-gradient-to-r from-navy to-ocean text-white px-4 py-6">
                <div class="flex items-center mb-4">
                    <button 
                        data-event="click:goBack"
                        data-implementation="Navigate back to home screen"
                        class="mr-3 p-2"
                    >
                        <i class="fas fa-arrow-left text-white"></i>
                    </button>
                    <h1 class="font-bold text-lg">QAAQ Services</h1>
                </div>
                <p class="text-sm opacity-90">Maritime engineering knowledge and services</p>
            </div>
            
            <div class="px-4 py-6">
                <!-- Service Categories -->
                <div class="grid grid-cols-2 gap-4 mb-6">
                    <!-- QAAQ Q&A -->
                    <button 
                        data-event="click:openQAAQ"
                        data-implementation="Open QAAQ knowledge platform"
                        class="bg-gradient-to-br from-navy to-blue-900 text-white p-4 rounded-xl text-left hover:scale-105 transition-all"
                    >
                        <i class="fas fa-question-circle text-2xl mb-2"></i>
                        <h3 class="font-bold mb-1">QAAQ Q&A</h3>
                        <p class="text-sm opacity-90">Marine engineering help</p>
                    </button>
                    
                    <!-- QAAQ Mart -->
                    <button 
                        data-event="click:openMart"
                        data-implementation="Open QAAQ marketplace"
                        class="bg-gradient-to-br from-ocean to-ocean-light text-white p-4 rounded-xl text-left hover:scale-105 transition-all"
                    >
                        <i class="fas fa-shopping-cart text-2xl mb-2"></i>
                        <h3 class="font-bold mb-1">QAAQ Mart</h3>
                        <p class="text-sm opacity-90">Marine equipment</p>
                    </button>
                </div>
                
                <!-- Featured Services -->
                <h3 class="font-bold text-navy text-lg mb-4">Featured Services</h3>
                
                <div class="space-y-4">
                    <!-- Service 1 -->
                    <div class="bg-white border border-gray-200 rounded-xl p-4 shadow-sm">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h4 class="font-bold text-navy mb-2" data-mock="true">Expert Troubleshooting</h4>
                                <p class="text-warm-grey text-sm mb-3" data-mock="true">Get instant help from marine engineering experts for equipment issues. AI + human expertise.</p>
                                <div class="flex items-center text-sm text-warm-grey">
                                    <div class="flex items-center mr-4">
                                        <i class="fas fa-users mr-1"></i>
                                        <span>2,847 questions answered</span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-clock mr-1"></i>
                                        <span>Avg 15min response</span>
                                    </div>
                                </div>
                            </div>
                            <button 
                                data-event="click:openService"
                                data-implementation="Open QAAQ troubleshooting platform"
                                class="bg-navy text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-900 transition-all ml-4"
                            >
                                Ask Question
                            </button>
                        </div>
                    </div>
                    
                    <!-- Service 2 -->
                    <div class="bg-white border border-gray-200 rounded-xl p-4 shadow-sm">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h4 class="font-bold text-navy mb-2" data-mock="true">WhatsApp Bot Assistant</h4>
                                <p class="text-warm-grey text-sm mb-3" data-mock="true">Connect with our maritime AI bot via WhatsApp for instant answers. Available 24/7.</p>
                                <div class="flex items-center text-sm text-warm-grey">
                                    <div class="flex items-center mr-4">
                                        <i class="fab fa-whatsapp mr-1"></i>
                                        <span>+************</span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-clock mr-1"></i>
                                        <span>24/7 available</span>
                                    </div>
                                </div>
                            </div>
                            <button 
                                data-event="click:openWhatsApp"
                                data-implementation="Open WhatsApp chat with QAAQ bot"
                                class="bg-green-500 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-green-600 transition-all ml-4"
                            >
                                Chat Now
                            </button>
                        </div>
                    </div>
                    
                    <!-- Service 3 -->
                    <div class="bg-white border border-gray-200 rounded-xl p-4 shadow-sm">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h4 class="font-bold text-navy mb-2" data-mock="true">Community Groups</h4>
                                <p class="text-warm-grey text-sm mb-3" data-mock="true">Join maritime professional groups in your location. Share knowledge and experiences.</p>
                                <div class="flex items-center text-sm text-warm-grey">
                                    <div class="flex items-center mr-4">
                                        <i class="fas fa-users mr-1"></i>
                                        <span>1,203 engineers</span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-globe mr-1"></i>
                                        <span>156 machine types</span>
                                    </div>
                                </div>
                            </div>
                            <button 
                                data-event="click:joinGroups"
                                data-implementation="Show available community groups"
                                class="bg-ocean text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-ocean-light transition-all ml-4"
                            >
                                Join Groups
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Stats -->
                <div class="mt-8 bg-grey-reg rounded-xl p-4">
                    <h4 class="font-bold text-navy mb-3 text-center">QAAQ Platform Stats</h4>
                    <div class="grid grid-cols-2 gap-4 text-center">
                        <div>
                            <div class="text-2xl font-bold text-navy" data-mock="true">2,847</div>
                            <div class="text-sm text-warm-grey">Questions Answered</div>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-navy" data-mock="true">1,203</div>
                            <div class="text-sm text-warm-grey">Maritime Engineers</div>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-navy" data-mock="true">156</div>
                            <div class="text-sm text-warm-grey">Machine Types</div>
                        </div>
                        <div>
                            <div class="text-2xl font-bold text-navy" data-mock="true">94%</div>
                            <div class="text-sm text-warm-grey">Questions Resolved</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- @END_COMPONENT: ServicesScreen -->
        
        <!-- Bottom Navigation -->
        <div id="bottom-nav" class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 px-4 py-2 hidden">
            <!-- @COMPONENT: BottomNavigation -->
            <div class="flex items-center justify-around">
                <button 
                    data-event="click:navigateToHome"
                    data-implementation="Navigate to home screen"
                    class="nav-btn flex flex-col items-center py-2 text-ocean"
                >
                    <i class="fas fa-home text-xl mb-1"></i>
                    <span class="text-xs">Home</span>
                </button>
                <button 
                    data-event="click:navigateToDiscover"
                    data-implementation="Navigate to activities screen"
                    class="nav-btn flex flex-col items-center py-2 text-warm-grey hover:text-ocean transition-colors"
                >
                    <i class="fas fa-compass text-xl mb-1"></i>
                    <span class="text-xs">Discover</span>
                </button>
                <button 
                    data-event="click:navigateToServices"
                    data-implementation="Navigate to QAAQ services"
                    class="nav-btn flex flex-col items-center py-2 text-warm-grey hover:text-ocean transition-colors"
                >
                    <i class="fas fa-cogs text-xl mb-1"></i>
                    <span class="text-xs">Services</span>
                </button>
                <button 
                    data-event="click:navigateToProfile"
                    data-implementation="Navigate to user profile"
                    class="nav-btn flex flex-col items-center py-2 text-warm-grey hover:text-ocean transition-colors"
                >
                    <i class="fas fa-user text-xl mb-1"></i>
                    <span class="text-xs">Profile</span>
                </button>
            </div>
        </div>
        <!-- @END_COMPONENT: BottomNavigation -->
        
        <!-- Toast Notifications -->
        <div id="toast-container" class="fixed top-4 left-4 right-4 z-50 hidden">
            <!-- @COMPONENT: ToastNotification -->
            <div class="bg-green-500 text-white px-4 py-3 rounded-lg shadow-lg flex items-center">
                <i class="fas fa-check-circle mr-2"></i>
                <span data-bind="toast.message">Welcome back, Captain! 🚢</span>
            </div>
        </div>
        <!-- @END_COMPONENT: ToastNotification -->
    </div>

    <script>
        // TODO: Implement business logic, API calls, and state management
        
        (function() {
            // Screen management
            let currentScreen = 'registration';
            let isRegistered = false;
            
            // Mock user data - would come from API
            const mockUser = {
                name: 'Captain Jack',
                type: 'sailor',
                location: 'Mumbai'
            };
            
            // Check if user is returning (mock auto-login)
            const isReturningUser = localStorage.getItem('qaaqConnectUser');
            
            if (isReturningUser) {
                // Auto-login for returning users
                showHomeScreen();
                showToast('Welcome back, Captain! 🚢');
            } else {
                // Show registration for new users
                showRegistrationScreen();
            }
            
            // User type selection
            document.querySelectorAll('.user-type-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    // Remove selection from other buttons
                    document.querySelectorAll('.user-type-btn').forEach(b => {
                        b.classList.remove('border-ocean', 'bg-ocean', 'bg-opacity-10');
                        b.classList.add('border-gray-200');
                    });
                    
                    // Add selection to clicked button
                    this.classList.remove('border-gray-200');
                    this.classList.add('border-ocean', 'bg-ocean', 'bg-opacity-10');
                });
            });
            
            // Registration form submission
            document.querySelector('form').addEventListener('submit', function(e) {
                e.preventDefault();
                // TODO: Validate form and create user account
                localStorage.setItem('qaaqConnectUser', JSON.stringify(mockUser));
                showHomeScreen();
                showToast('Welcome to QaaqConnect! 🚢');
            });
            
            // Navigation functions
            function showRegistrationScreen() {
                document.getElementById('registration-screen').classList.remove('hidden');
                document.getElementById('home-screen').classList.add('hidden');
                document.getElementById('activities-screen').classList.add('hidden');
                document.getElementById('services-screen').classList.add('hidden');
                document.getElementById('bottom-nav').classList.add('hidden');
                currentScreen = 'registration';
            }
            
            function showHomeScreen() {
                document.getElementById('registration-screen').classList.add('hidden');
                document.getElementById('home-screen').classList.remove('hidden');
                document.getElementById('activities-screen').classList.add('hidden');
                document.getElementById('services-screen').classList.add('hidden');
                document.getElementById('bottom-nav').classList.remove('hidden');
                currentScreen = 'home';
                
                // Update active nav button
                updateActiveNavButton('home');
            }
            
            function showActivitiesScreen() {
                document.getElementById('registration-screen').classList.add('hidden');
                document.getElementById('home-screen').classList.add('hidden');
                document.getElementById('activities-screen').classList.remove('hidden');
                document.getElementById('services-screen').classList.add('hidden');
                document.getElementById('bottom-nav').classList.remove('hidden');
                currentScreen = 'activities';
                
                updateActiveNavButton('discover');
            }
            
            function showServicesScreen() {
                document.getElementById('registration-screen').classList.add('hidden');
                document.getElementById('home-screen').classList.add('hidden');
                document.getElementById('activities-screen').classList.add('hidden');
                document.getElementById('services-screen').classList.remove('hidden');
                document.getElementById('bottom-nav').classList.remove('hidden');
                currentScreen = 'services';
                
                updateActiveNavButton('services');
            }
            
            function updateActiveNavButton(active) {
                document.querySelectorAll('.nav-btn').forEach(btn => {
                    btn.classList.remove('text-ocean');
                    btn.classList.add('text-warm-grey');
                });
                
                const activeBtn = document.querySelector(`[data-event="click:navigateTo${active.charAt(0).toUpperCase() + active.slice(1)}"]`);
                if (activeBtn) {
                    activeBtn.classList.remove('text-warm-grey');
                    activeBtn.classList.add('text-ocean');
                }
            }
            
            // Toast notification
            function showToast(message) {
                const toastContainer = document.getElementById('toast-container');
                const toastSpan = toastContainer.querySelector('span');
                toastSpan.textContent = message;
                
                toastContainer.classList.remove('hidden');
                
                setTimeout(() => {
                    toastContainer.classList.add('hidden');
                }, 3000);
            }
            
            // Bottom navigation event listeners
            document.querySelector('[data-event="click:navigateToHome"]').addEventListener('click', showHomeScreen);
            document.querySelector('[data-event="click:navigateToDiscover"]').addEventListener('click', showActivitiesScreen);
            document.querySelector('[data-event="click:navigateToServices"]').addEventListener('click', showServicesScreen);
            
            // Back button functionality
            document.querySelectorAll('[data-event="click:goBack"]').forEach(btn => {
                btn.addEventListener('click', function() {
                    if (currentScreen === 'activities' || currentScreen === 'services') {
                        showHomeScreen();
                    }
                });
            });
            
            // Mock interaction handlers
            document.addEventListener('click', function(e) {
                const target = e.target.closest('[data-event]');
                if (!target) return;
                
                const event = target.getAttribute('data-event');
                
                // Handle various interactions with mock responses
                switch(event) {
                    case 'click:activateDiscovery':
                        showToast('Discovery mode activated! 🗺️');
                        break;
                    case 'click:connectWithUser':
                        showToast('Connection request sent! 🤝');
                        break;
                    case 'click:findSailors':
                        showToast('Finding sailors nearby... 🚢');
                        break;
                    case 'click:findLocals':
                        showToast('Finding local guides... 🏠');
                        break;
                    case 'click:joinActivity':
                        showToast('Joined activity! See you there! 🎉');
                        break;
                    case 'click:openQAAQ':
                        showToast('Opening QAAQ platform... 🔧');
                        break;
                    case 'click:openWhatsApp':
                        window.open('https://wa.me/************', '_blank');
                        break;
                }
            });
            
        })();
    </script>
</body>
</html>