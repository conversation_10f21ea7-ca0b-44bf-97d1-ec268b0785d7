<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Koi Maritime - Connect with Maritime Professionals</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                    },
                    colors: {
                        'navy': '#1B365D',
                        'ocean': '#2D7D9C',
                        'light-blue': '#E8F4F8',
                        'maritime-gray': '#64748B',
                        'warning-orange': '#F59E0B'
                    }
                }
            }
        };
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="font-inter bg-gray-50">

    <!-- @COMPONENT: WelcomeScreen [initial landing with branding] -->
    <div id="welcomeScreen" class="min-h-screen bg-gradient-to-br from-navy via-ocean to-navy flex flex-col">
        <!-- Header with logo -->
        <div class="text-center pt-16 pb-8">
            <div class="w-24 h-24 bg-white rounded-full mx-auto mb-6 flex items-center justify-center shadow-lg">
                <i class="fas fa-anchor text-4xl text-navy"></i>
            </div>
            <h1 class="text-4xl font-bold text-white mb-2">Koi Maritime</h1>
            <p class="text-light-blue text-lg">Connect • Discover • Collaborate</p>
        </div>

        <!-- Main content -->
        <div class="flex-1 px-6 py-8">
            <!-- A diverse group of maritime crew members on deck -->
            <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400" alt="Maritime crew members on deck" class="rounded-2xl shadow-xl mb-8 w-full h-48 object-cover" />
            
            <div class="text-center text-white mb-8">
                <h2 class="text-2xl font-semibold mb-4">Find Your Maritime Community</h2>
                <p class="text-light-blue leading-relaxed mb-6">Connect with fellow mariners, discover crew nearby, and build your professional network across all ranks and departments.</p>
                
                <div class="grid grid-cols-2 gap-4 mb-8">
                    <div class="bg-white/10 rounded-xl p-4 backdrop-blur-sm">
                        <i class="fas fa-users text-2xl text-light-blue mb-2"></i>
                        <p class="text-sm font-medium">All Maritime Professionals</p>
                    </div>
                    <div class="bg-white/10 rounded-xl p-4 backdrop-blur-sm">
                        <i class="fas fa-ship text-2xl text-light-blue mb-2"></i>
                        <p class="text-sm font-medium">Vessel-Based Network</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action buttons -->
        <div class="px-6 pb-8">
            <button onclick="showMainApp()" class="w-full bg-white text-navy py-4 rounded-xl font-semibold text-lg shadow-lg mb-4 hover:bg-gray-50 transition-colors">
                Get Started
            </button>
            <p class="text-center text-light-blue text-sm">
                Powered by <span class="font-semibold">QAAQ Maritime Database</span>
            </p>
        </div>
    </div>
    <!-- @END_COMPONENT: WelcomeScreen -->

    <!-- @COMPONENT: MainApplication [primary app interface] -->
    <div id="mainApp" class="hidden min-h-screen bg-gray-50">
        
        <!-- Top Navigation -->
        <header class="bg-navy text-white p-4 shadow-lg">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <i class="fas fa-anchor text-xl"></i>
                    <h1 class="text-xl font-semibold">Koi Maritime</h1>
                </div>
                <div class="flex items-center space-x-3">
                    <button class="relative">
                        <i class="fas fa-bell text-xl"></i>
                        <span class="absolute -top-1 -right-1 bg-warning-orange text-xs rounded-full w-5 h-5 flex items-center justify-center">3</span>
                    </button>
                    <button onclick="showProfile()" class="w-8 h-8 bg-ocean rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-sm"></i>
                    </button>
                </div>
            </div>
        </header>

        <!-- Tab Navigation -->
        <div class="bg-white border-b border-gray-200">
            <div class="flex">
                <button onclick="showTab('sailors')" id="sailorsTab" class="flex-1 py-4 text-center font-medium border-b-2 border-ocean text-ocean">
                    <i class="fas fa-compass mr-2"></i>Sailors Nearby
                </button>
                <button onclick="showTab('crew')" id="crewTab" class="flex-1 py-4 text-center font-medium border-b-2 border-transparent text-gray-500">
                    <i class="fas fa-ship mr-2"></i>Crew Nearby
                </button>
            </div>
        </div>

        <!-- @COMPONENT: SailorsNearbyTab [location-based discovery] -->
        <div id="sailorsContent" class="p-4">
            <!-- Search and Filter -->
            <div class="flex space-x-3 mb-4">
                <div class="flex-1 relative">
                    <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                    <input type="text" placeholder="Search mariners..." class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-ocean focus:border-transparent">
                </div>
                <button onclick="showFilters()" class="bg-ocean text-white px-4 py-3 rounded-xl">
                    <i class="fas fa-filter"></i>
                </button>
            </div>

            <!-- Location Status -->
            <div class="bg-light-blue border border-ocean/20 rounded-xl p-4 mb-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-map-marker-alt text-ocean"></i>
                        <div>
                            <p class="font-medium text-navy">Istanbul Port, Turkey</p>
                            <p class="text-sm text-maritime-gray">23 mariners within 5km</p>
                        </div>
                    </div>
                    <button class="text-ocean font-medium">Update</button>
                </div>
            </div>

            <!-- @MAP: nearbyMariners.map(mariner => ( -->
            <!-- Mariner Cards -->
            <div class="space-y-4">
                <!-- Chief Engineer Card -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
                    <div class="flex items-start space-x-3">
                        <!-- Professional headshot of a maritime engineer -->
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150" alt="Chief Engineer profile" class="w-12 h-12 rounded-full object-cover" />
                        <div class="flex-1">
                            <div class="flex items-center justify-between">
                                <h3 class="font-semibold text-navy" data-bind="mariner.name">Piyush Gupta</h3>
                                <span class="text-sm text-maritime-gray">1.2km</span>
                            </div>
                            <div class="flex items-center space-x-2 mt-1">
                                <span class="bg-navy text-white text-xs px-2 py-1 rounded-full">Chief Engineer</span>
                                <span class="text-xs text-maritime-gray">Engine Dept.</span>
                            </div>
                            <p class="text-sm text-maritime-gray mt-1" data-bind="mariner.vessel">MT Solar Claire</p>
                            <div class="flex items-center justify-between mt-3">
                                <div class="flex items-center space-x-2 text-xs text-maritime-gray">
                                    <i class="fas fa-certificate"></i>
                                    <span>MEO Class I</span>
                                </div>
                                <div class="flex space-x-2">
                                    <button data-event="click:openChat" class="bg-ocean text-white px-3 py-1 rounded-lg text-sm">
                                        <i class="fas fa-comment mr-1"></i>Chat
                                    </button>
                                    <button class="border border-gray-300 text-gray-600 px-3 py-1 rounded-lg text-sm">
                                        <i class="fas fa-user-plus mr-1"></i>Connect
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Second Engineer Card -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
                    <div class="flex items-start space-x-3">
                        <!-- Professional headshot of a female maritime officer -->
                        <img src="https://pixabay.com/get/g3720c6a8b9a13785f3fe9f901f4cbe0944d7b2ca744d28a1b8e4753c71994f1a1b58adbb079988ab16f4e8ebf0df0cea30deddb3b96f0fdc77d0e62cda85dd12_1280.jpg" alt="Second Engineer profile" class="w-12 h-12 rounded-full object-cover" />
                        <div class="flex-1">
                            <div class="flex items-center justify-between">
                                <h3 class="font-semibold text-navy">Sarah Chen</h3>
                                <span class="text-sm text-maritime-gray">2.8km</span>
                            </div>
                            <div class="flex items-center space-x-2 mt-1">
                                <span class="bg-ocean text-white text-xs px-2 py-1 rounded-full">2nd Engineer</span>
                                <span class="text-xs text-maritime-gray">Engine Dept.</span>
                            </div>
                            <p class="text-sm text-maritime-gray mt-1">MV Baltic Star</p>
                            <div class="flex items-center justify-between mt-3">
                                <div class="flex items-center space-x-2 text-xs text-maritime-gray">
                                    <i class="fas fa-certificate"></i>
                                    <span>MEO Class II</span>
                                </div>
                                <div class="flex space-x-2">
                                    <button class="bg-ocean text-white px-3 py-1 rounded-lg text-sm">
                                        <i class="fas fa-comment mr-1"></i>Chat
                                    </button>
                                    <button class="border border-gray-300 text-gray-600 px-3 py-1 rounded-lg text-sm">
                                        <i class="fas fa-user-plus mr-1"></i>Connect
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Bosun Card -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
                    <div class="flex items-start space-x-3">
                        <!-- Professional headshot of an experienced deck officer -->
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150" alt="Bosun profile" class="w-12 h-12 rounded-full object-cover" />
                        <div class="flex-1">
                            <div class="flex items-center justify-between">
                                <h3 class="font-semibold text-navy">James Rodriguez</h3>
                                <span class="text-sm text-maritime-gray">3.5km</span>
                            </div>
                            <div class="flex items-center space-x-2 mt-1">
                                <span class="bg-green-600 text-white text-xs px-2 py-1 rounded-full">Bosun</span>
                                <span class="text-xs text-maritime-gray">Deck Dept.</span>
                            </div>
                            <p class="text-sm text-maritime-gray mt-1">MV Ocean Explorer</p>
                            <div class="flex items-center justify-between mt-3">
                                <div class="flex items-center space-x-2 text-xs text-maritime-gray">
                                    <i class="fas fa-certificate"></i>
                                    <span>STCW A-II/1</span>
                                </div>
                                <div class="flex space-x-2">
                                    <button class="bg-ocean text-white px-3 py-1 rounded-lg text-sm">
                                        <i class="fas fa-comment mr-1"></i>Chat
                                    </button>
                                    <button class="border border-gray-300 text-gray-600 px-3 py-1 rounded-lg text-sm">
                                        <i class="fas fa-user-plus mr-1"></i>Connect
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Cook Card -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-4">
                    <div class="flex items-start space-x-3">
                        <!-- Professional headshot of a ship's cook -->
                        <img src="https://images.unsplash.com/photo-1582750433449-648ed127bb54?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150" alt="Cook profile" class="w-12 h-12 rounded-full object-cover" />
                        <div class="flex-1">
                            <div class="flex items-center justify-between">
                                <h3 class="font-semibold text-navy">Maria Santos</h3>
                                <span class="text-sm text-maritime-gray">4.1km</span>
                            </div>
                            <div class="flex items-center space-x-2 mt-1">
                                <span class="bg-orange-500 text-white text-xs px-2 py-1 rounded-full">Chief Cook</span>
                                <span class="text-xs text-maritime-gray">Catering Dept.</span>
                            </div>
                            <p class="text-sm text-maritime-gray mt-1">MV Mediterranean Pride</p>
                            <div class="flex items-center justify-between mt-3">
                                <div class="flex items-center space-x-2 text-xs text-maritime-gray">
                                    <i class="fas fa-certificate"></i>
                                    <span>Food Safety</span>
                                </div>
                                <div class="flex space-x-2">
                                    <button class="bg-ocean text-white px-3 py-1 rounded-lg text-sm">
                                        <i class="fas fa-comment mr-1"></i>Chat
                                    </button>
                                    <button class="border border-gray-300 text-gray-600 px-3 py-1 rounded-lg text-sm">
                                        <i class="fas fa-user-plus mr-1"></i>Connect
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- @END_MAP )) -->
        </div>
        <!-- @END_COMPONENT: SailorsNearbyTab -->

        <!-- @COMPONENT: CrewNearbyTab [vessel-based discovery] -->
        <div id="crewContent" class="hidden p-4">
            <!-- Vessel Search -->
            <div class="bg-white rounded-xl p-4 mb-4 border border-gray-200">
                <label class="block text-sm font-medium text-navy mb-2">Enter Vessel IMO Number</label>
                <div class="flex space-x-3">
                    <input type="text" placeholder="9123456" class="flex-1 px-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-ocean focus:border-transparent">
                    <button class="bg-ocean text-white px-6 py-3 rounded-xl font-medium">Search</button>
                </div>
            </div>

            <!-- Current Vessel Info -->
            <div class="bg-navy text-white rounded-xl p-4 mb-6">
                <div class="flex items-start space-x-3">
                    <i class="fas fa-ship text-2xl text-light-blue mt-1"></i>
                    <div class="flex-1">
                        <h3 class="text-lg font-semibold">MT Solar Claire</h3>
                        <p class="text-light-blue">Chemical Tanker • IMO: 9567890</p>
                        <div class="flex items-center space-x-4 mt-2 text-sm">
                            <span><i class="fas fa-users mr-1"></i>24 Crew</span>
                            <span><i class="fas fa-map-marker-alt mr-1"></i>Istanbul Port</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Department Filters -->
            <div class="flex space-x-2 mb-4 overflow-x-auto">
                <button onclick="filterDepartment('all')" class="bg-ocean text-white px-4 py-2 rounded-full text-sm whitespace-nowrap">All Departments</button>
                <button onclick="filterDepartment('deck')" class="bg-gray-200 text-gray-700 px-4 py-2 rounded-full text-sm whitespace-nowrap">Deck</button>
                <button onclick="filterDepartment('engine')" class="bg-gray-200 text-gray-700 px-4 py-2 rounded-full text-sm whitespace-nowrap">Engine</button>
                <button onclick="filterDepartment('catering')" class="bg-gray-200 text-gray-700 px-4 py-2 rounded-full text-sm whitespace-nowrap">Catering</button>
            </div>

            <!-- Crew List by Department -->
            <div class="space-y-6">
                <!-- Deck Department -->
                <div>
                    <h4 class="font-semibold text-navy mb-3 flex items-center">
                        <i class="fas fa-anchor text-ocean mr-2"></i>Deck Department (8)
                    </h4>
                    <div class="space-y-3">
                        <div class="bg-white rounded-xl p-4 border border-gray-200">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <!-- Captain profile photo -->
                                    <img src="https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150" alt="Captain profile" class="w-10 h-10 rounded-full object-cover" />
                                    <div>
                                        <h5 class="font-medium text-navy">Captain Anderson</h5>
                                        <p class="text-sm text-maritime-gray">Master Mariner</p>
                                    </div>
                                </div>
                                <button class="text-ocean">
                                    <i class="fas fa-comment"></i>
                                </button>
                            </div>
                        </div>
                        
                        <div class="bg-white rounded-xl p-4 border border-gray-200">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <!-- Chief Officer profile photo -->
                                    <img src="https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150" alt="Chief Officer profile" class="w-10 h-10 rounded-full object-cover" />
                                    <div>
                                        <h5 class="font-medium text-navy">First Officer Kim</h5>
                                        <p class="text-sm text-maritime-gray">Chief Mate</p>
                                    </div>
                                </div>
                                <button class="text-ocean">
                                    <i class="fas fa-comment"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Engine Department -->
                <div>
                    <h4 class="font-semibold text-navy mb-3 flex items-center">
                        <i class="fas fa-cog text-ocean mr-2"></i>Engine Department (10)
                    </h4>
                    <div class="space-y-3">
                        <div class="bg-white rounded-xl p-4 border border-gray-200">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150" alt="Chief Engineer profile" class="w-10 h-10 rounded-full object-cover" />
                                    <div>
                                        <h5 class="font-medium text-navy">Piyush Gupta</h5>
                                        <p class="text-sm text-maritime-gray">Chief Engineer</p>
                                    </div>
                                </div>
                                <button class="text-ocean">
                                    <i class="fas fa-comment"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Catering Department -->
                <div>
                    <h4 class="font-semibold text-navy mb-3 flex items-center">
                        <i class="fas fa-utensils text-ocean mr-2"></i>Catering Department (6)
                    </h4>
                    <div class="space-y-3">
                        <div class="bg-white rounded-xl p-4 border border-gray-200">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <img src="https://images.unsplash.com/photo-1582750433449-648ed127bb54?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150" alt="Chief Cook profile" class="w-10 h-10 rounded-full object-cover" />
                                    <div>
                                        <h5 class="font-medium text-navy">Chief Cook Martinez</h5>
                                        <p class="text-sm text-maritime-gray">Catering Manager</p>
                                    </div>
                                </div>
                                <button class="text-ocean">
                                    <i class="fas fa-comment"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- @END_COMPONENT: CrewNearbyTab -->

        <!-- Floating Action Button -->
        <button onclick="showQuickActions()" class="fixed bottom-6 right-6 bg-ocean text-white w-14 h-14 rounded-full shadow-lg flex items-center justify-center">
            <i class="fas fa-plus text-xl"></i>
        </button>

    </div>
    <!-- @END_COMPONENT: MainApplication -->

    <!-- @COMPONENT: ChatInterface [WhatsApp-style messaging] -->
    <div id="chatInterface" class="hidden min-h-screen bg-gray-100">
        <!-- Chat Header -->
        <header class="bg-navy text-white p-4 flex items-center space-x-3">
            <button onclick="closeChatInterface()" class="text-xl">
                <i class="fas fa-arrow-left"></i>
            </button>
            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150" alt="Chat partner profile" class="w-10 h-10 rounded-full object-cover" />
            <div class="flex-1">
                <h3 class="font-semibold">Piyush Gupta</h3>
                <p class="text-sm text-light-blue">Chief Engineer • Online</p>
            </div>
            <button class="text-xl">
                <i class="fas fa-phone"></i>
            </button>
        </header>

        <!-- Chat Messages -->
        <div class="flex-1 p-4 space-y-4 pb-20">
            <!-- Received Message -->
            <div class="flex items-start space-x-2">
                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150" alt="Chat partner" class="w-8 h-8 rounded-full object-cover" />
                <div>
                    <div class="bg-white rounded-2xl rounded-tl-md p-3 shadow-sm max-w-xs">
                        <p class="text-gray-800">Hey! I saw you're also docked in Istanbul. Are you free for coffee later?</p>
                    </div>
                    <p class="text-xs text-gray-500 mt-1 ml-3">10:30 AM</p>
                </div>
            </div>

            <!-- Sent Message -->
            <div class="flex items-end justify-end space-x-2">
                <div>
                    <div class="bg-ocean text-white rounded-2xl rounded-br-md p-3 shadow-sm max-w-xs">
                        <p>Absolutely! I know a great place near the port. What time works for you?</p>
                    </div>
                    <p class="text-xs text-gray-500 mt-1 mr-3 text-right">10:32 AM ✓✓</p>
                </div>
            </div>

            <!-- System Message -->
            <div class="flex justify-center">
                <div class="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-xs">
                    <i class="fas fa-info-circle mr-1"></i>
                    Professional networking reminder: Keep conversations respectful and maritime-focused
                </div>
            </div>

            <!-- Received Message with Maritime Context -->
            <div class="flex items-start space-x-2">
                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150" alt="Chat partner" class="w-8 h-8 rounded-full object-cover" />
                <div>
                    <div class="bg-white rounded-2xl rounded-tl-md p-3 shadow-sm max-w-xs">
                        <p class="text-gray-800">How about 3 PM? Also, I wanted to ask about your experience with MAN B&W engines. We're having some issues with our 6S50ME.</p>
                    </div>
                    <p class="text-xs text-gray-500 mt-1 ml-3">10:35 AM</p>
                </div>
            </div>
        </div>

        <!-- Chat Input -->
        <div class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4">
            <div class="flex items-center space-x-3">
                <button class="text-gray-500">
                    <i class="fas fa-paperclip text-xl"></i>
                </button>
                <div class="flex-1 bg-gray-100 rounded-full px-4 py-2">
                    <input type="text" placeholder="Type a message..." class="w-full bg-transparent focus:outline-none">
                </div>
                <button class="bg-ocean text-white w-10 h-10 rounded-full flex items-center justify-center">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    </div>
    <!-- @END_COMPONENT: ChatInterface -->

    <!-- @COMPONENT: ProfileScreen [user profile and documents] -->
    <div id="profileScreen" class="hidden min-h-screen bg-gray-50">
        <!-- Profile Header -->
        <header class="bg-gradient-to-r from-navy to-ocean text-white p-6">
            <div class="flex items-center justify-between mb-4">
                <button onclick="closeProfile()" class="text-xl">
                    <i class="fas fa-arrow-left"></i>
                </button>
                <h1 class="text-xl font-semibold">My Profile</h1>
                <button class="text-xl">
                    <i class="fas fa-edit"></i>
                </button>
            </div>
            
            <div class="text-center">
                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150" alt="Profile photo" class="w-24 h-24 rounded-full mx-auto mb-4 border-4 border-white/20" />
                <h2 class="text-2xl font-bold">Piyush Gupta</h2>
                <p class="text-light-blue">Chief Engineer</p>
                <p class="text-sm text-light-blue mt-1">21+ years experience • Age 45</p>
            </div>
        </header>

        <!-- Profile Content -->
        <div class="p-4 space-y-6">
            <!-- Current Position -->
            <div class="bg-white rounded-xl p-4 shadow-sm">
                <h3 class="font-semibold text-navy mb-3 flex items-center">
                    <i class="fas fa-ship text-ocean mr-2"></i>Current Assignment
                </h3>
                <div class="space-y-2">
                    <div class="flex justify-between">
                        <span class="text-maritime-gray">Vessel:</span>
                        <span class="font-medium">MT Solar Claire</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-maritime-gray">Type:</span>
                        <span class="font-medium">Chemical Tanker</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-maritime-gray">Location:</span>
                        <span class="font-medium">Istanbul, Turkey</span>
                    </div>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="grid grid-cols-2 gap-4">
                <button onclick="showDocuments()" class="bg-white rounded-xl p-4 shadow-sm border border-gray-200 text-center">
                    <i class="fas fa-folder text-2xl text-ocean mb-2"></i>
                    <p class="font-medium text-navy">Documents</p>
                    <p class="text-xs text-maritime-gray">Certificates & Papers</p>
                </button>
                <button onclick="showResume()" class="bg-white rounded-xl p-4 shadow-sm border border-gray-200 text-center">
                    <i class="fas fa-file-alt text-2xl text-ocean mb-2"></i>
                    <p class="font-medium text-navy">Resume</p>
                    <p class="text-xs text-maritime-gray">Career Profile</p>
                </button>
            </div>

            <!-- Professional Summary -->
            <div class="bg-white rounded-xl p-4 shadow-sm">
                <h3 class="font-semibold text-navy mb-3">Professional Summary</h3>
                <p class="text-maritime-gray leading-relaxed text-sm">
                    Marine Engineer with 21+ years of Industry expertise. 15 years' sailing & 6 years' shore experience. Specialized in technical management of dual fuel compliant vessels and EGCS systems.
                </p>
            </div>

            <!-- Recent Experience -->
            <div class="bg-white rounded-xl p-4 shadow-sm">
                <h3 class="font-semibold text-navy mb-3">Recent Experience</h3>
                <div class="space-y-3">
                    <div class="border-l-4 border-ocean pl-3">
                        <h4 class="font-medium text-navy">Technical Manager</h4>
                        <p class="text-sm text-maritime-gray">Nordic Hamburg (Dec 2023 - Oct 2024)</p>
                        <p class="text-sm text-maritime-gray mt-1">Fleet of 25 vessels including Container ships & Tankers</p>
                    </div>
                    <div class="border-l-4 border-gray-300 pl-3">
                        <h4 class="font-medium text-navy">Technical Superintendent</h4>
                        <p class="text-sm text-maritime-gray">Scorpio Marine Management (Feb 2023 - Dec 2023)</p>
                        <p class="text-sm text-maritime-gray mt-1">LR Crude/Clean product Oil Tankers</p>
                    </div>
                </div>
            </div>

            <!-- Certificates -->
            <div class="bg-white rounded-xl p-4 shadow-sm">
                <h3 class="font-semibold text-navy mb-3">Key Certificates</h3>
                <div class="grid grid-cols-2 gap-3">
                    <div class="bg-gray-50 rounded-lg p-3 text-center">
                        <i class="fas fa-certificate text-ocean mb-2"></i>
                        <p class="text-sm font-medium">MEO Class I</p>
                        <p class="text-xs text-maritime-gray">Valid: Sep 2027</p>
                    </div>
                    <div class="bg-gray-50 rounded-lg p-3 text-center">
                        <i class="fas fa-certificate text-ocean mb-2"></i>
                        <p class="text-sm font-medium">STCW A-III/2</p>
                        <p class="text-xs text-maritime-gray">Valid: 2026</p>
                    </div>
                </div>
            </div>

            <!-- QAAQ Integration -->
            <div class="bg-gradient-to-r from-navy to-ocean rounded-xl p-4 text-white">
                <div class="flex items-center space-x-3 mb-3">
                    <i class="fas fa-anchor text-2xl"></i>
                    <div>
                        <h3 class="font-semibold">QAAQ Profile Sync</h3>
                        <p class="text-light-blue text-sm">Connected to maritime knowledge hub</p>
                    </div>
                </div>
                <div class="grid grid-cols-3 gap-4 text-center">
                    <div>
                        <p class="text-2xl font-bold">156</p>
                        <p class="text-xs text-light-blue">Questions Asked</p>
                    </div>
                    <div>
                        <p class="text-2xl font-bold">89</p>
                        <p class="text-xs text-light-blue">Answers Given</p>
                    </div>
                    <div>
                        <p class="text-2xl font-bold">4.8</p>
                        <p class="text-xs text-light-blue">Rating</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- @END_COMPONENT: ProfileScreen -->

    <!-- @COMPONENT: DocumentsScreen [certificates and papers] -->
    <div id="documentsScreen" class="hidden min-h-screen bg-gray-50">
        <!-- Documents Header -->
        <header class="bg-navy text-white p-4 flex items-center space-x-3">
            <button onclick="closeDocuments()" class="text-xl">
                <i class="fas fa-arrow-left"></i>
            </button>
            <h1 class="text-xl font-semibold">Marine Documents</h1>
            <button class="ml-auto text-xl">
                <i class="fas fa-plus"></i>
            </button>
        </header>

        <!-- Document Categories -->
        <div class="p-4 space-y-4">
            <!-- Certificates of Competency -->
            <div class="bg-white rounded-xl shadow-sm">
                <div class="p-4 border-b border-gray-200">
                    <h3 class="font-semibold text-navy flex items-center">
                        <i class="fas fa-certificate text-ocean mr-2"></i>
                        Certificates of Competency
                    </h3>
                </div>
                <div class="p-4 space-y-3">
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-file-alt text-ocean"></i>
                            <div>
                                <p class="font-medium text-navy">MEO Class I (Motor)</p>
                                <p class="text-sm text-maritime-gray">Cert No: 95W7349</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-medium text-green-600">Valid</p>
                            <p class="text-xs text-maritime-gray">Until Sep 2027</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Dangerous Cargo Endorsements -->
            <div class="bg-white rounded-xl shadow-sm">
                <div class="p-4 border-b border-gray-200">
                    <h3 class="font-semibold text-navy flex items-center">
                        <i class="fas fa-exclamation-triangle text-warning-orange mr-2"></i>
                        Dangerous Cargo Endorsements
                    </h3>
                </div>
                <div class="p-4 space-y-3">
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-file-alt text-warning-orange"></i>
                            <div>
                                <p class="font-medium text-navy">Petroleum Tanker</p>
                                <p class="text-sm text-maritime-gray">DCE02MUM22013387</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-medium text-green-600">Valid</p>
                            <p class="text-xs text-maritime-gray">Until Sep 2027</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-file-alt text-warning-orange"></i>
                            <div>
                                <p class="font-medium text-navy">Chemical Tanker</p>
                                <p class="text-sm text-maritime-gray">DCE02MUM22013385</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-medium text-green-600">Valid</p>
                            <p class="text-xs text-maritime-gray">Until Sep 2027</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- STCW Certificates -->
            <div class="bg-white rounded-xl shadow-sm">
                <div class="p-4 border-b border-gray-200">
                    <h3 class="font-semibold text-navy flex items-center">
                        <i class="fas fa-life-ring text-ocean mr-2"></i>
                        STCW Safety Certificates
                    </h3>
                </div>
                <div class="p-4 space-y-3">
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-file-alt text-ocean"></i>
                            <div>
                                <p class="font-medium text-navy">Advanced Fire Fighting</p>
                                <p class="text-sm text-maritime-gray">AFFC/0211/015/2003</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-medium text-green-600">Valid</p>
                            <p class="text-xs text-maritime-gray">Lifetime</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-file-alt text-ocean"></i>
                            <div>
                                <p class="font-medium text-navy">Medical First Aid</p>
                                <p class="text-sm text-maritime-gray">BPMA/M/64/2003</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-medium text-red-600">Expired</p>
                            <p class="text-xs text-maritime-gray">Dec 2016</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Travel Documents -->
            <div class="bg-white rounded-xl shadow-sm">
                <div class="p-4 border-b border-gray-200">
                    <h3 class="font-semibold text-navy flex items-center">
                        <i class="fas fa-passport text-ocean mr-2"></i>
                        Travel Documents
                    </h3>
                </div>
                <div class="p-4 space-y-3">
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-id-card text-ocean"></i>
                            <div>
                                <p class="font-medium text-navy">Indian Passport</p>
                                <p class="text-sm text-maritime-gray">Z 4318896</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-medium text-green-600">Valid</p>
                            <p class="text-xs text-maritime-gray">Until Jun 2027</p>
                        </div>
                    </div>
                    
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <i class="fas fa-id-card text-ocean"></i>
                            <div>
                                <p class="font-medium text-navy">CDC (Seafarer ID)</p>
                                <p class="text-sm text-maritime-gray">M32531107</p>
                            </div>
                        </div>
                        <div class="text-right">
                            <p class="text-sm font-medium text-green-600">Valid</p>
                            <p class="text-xs text-maritime-gray">Until Aug 2032</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Upload New Document Button -->
        <div class="fixed bottom-6 right-6">
            <button class="bg-ocean text-white w-14 h-14 rounded-full shadow-lg flex items-center justify-center">
                <i class="fas fa-camera text-xl"></i>
            </button>
        </div>
    </div>
    <!-- @END_COMPONENT: DocumentsScreen -->

    <!-- Bottom Navigation (shown in main app) -->
    <nav id="bottomNav" class="hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200">
        <div class="flex">
            <button onclick="showTab('sailors')" class="flex-1 py-3 text-center">
                <i class="fas fa-compass text-xl text-ocean mb-1"></i>
                <p class="text-xs text-ocean font-medium">Discover</p>
            </button>
            <button onclick="showMessages()" class="flex-1 py-3 text-center">
                <i class="fas fa-comment text-xl text-gray-400 mb-1"></i>
                <p class="text-xs text-gray-400">Messages</p>
            </button>
            <button onclick="showProfile()" class="flex-1 py-3 text-center">
                <i class="fas fa-user text-xl text-gray-400 mb-1"></i>
                <p class="text-xs text-gray-400">Profile</p>
            </button>
        </div>
    </nav>

    <script>
        // @STATE: currentView:string = 'welcome'
        // @STATE: activeTab:string = 'sailors'
        
        (function() {
            // Show main application
            window.showMainApp = function() {
                document.getElementById('welcomeScreen').classList.add('hidden');
                document.getElementById('mainApp').classList.remove('hidden');
                document.getElementById('bottomNav').classList.remove('hidden');
            };

            // Tab switching functionality
            window.showTab = function(tab) {
                // Hide all tab contents
                document.getElementById('sailorsContent').classList.add('hidden');
                document.getElementById('crewContent').classList.add('hidden');
                
                // Reset tab styles
                document.getElementById('sailorsTab').className = 'flex-1 py-4 text-center font-medium border-b-2 border-transparent text-gray-500';
                document.getElementById('crewTab').className = 'flex-1 py-4 text-center font-medium border-b-2 border-transparent text-gray-500';
                
                // Show selected tab
                if (tab === 'sailors') {
                    document.getElementById('sailorsContent').classList.remove('hidden');
                    document.getElementById('sailorsTab').className = 'flex-1 py-4 text-center font-medium border-b-2 border-ocean text-ocean';
                } else if (tab === 'crew') {
                    document.getElementById('crewContent').classList.remove('hidden');
                    document.getElementById('crewTab').className = 'flex-1 py-4 text-center font-medium border-b-2 border-ocean text-ocean';
                }
            };

            // Chat interface
            window.openChat = function(userId) {
                document.getElementById('mainApp').classList.add('hidden');
                document.getElementById('chatInterface').classList.remove('hidden');
                document.getElementById('bottomNav').classList.add('hidden');
            };

            window.closeChatInterface = function() {
                document.getElementById('chatInterface').classList.add('hidden');
                document.getElementById('mainApp').classList.remove('hidden');
                document.getElementById('bottomNav').classList.remove('hidden');
            };

            // Profile management
            window.showProfile = function() {
                document.getElementById('mainApp').classList.add('hidden');
                document.getElementById('profileScreen').classList.remove('hidden');
                document.getElementById('bottomNav').classList.add('hidden');
            };

            window.closeProfile = function() {
                document.getElementById('profileScreen').classList.add('hidden');
                document.getElementById('mainApp').classList.remove('hidden');
                document.getElementById('bottomNav').classList.remove('hidden');
            };

            // Documents management
            window.showDocuments = function() {
                document.getElementById('profileScreen').classList.add('hidden');
                document.getElementById('documentsScreen').classList.remove('hidden');
            };

            window.closeDocuments = function() {
                document.getElementById('documentsScreen').classList.add('hidden');
                document.getElementById('profileScreen').classList.remove('hidden');
            };

            // Filter functions
            window.showFilters = function() {
                // TODO: Implement filter modal
                console.log('Show filters modal');
            };

            window.filterDepartment = function(dept) {
                // TODO: Implement department filtering
                console.log('Filter by department:', dept);
            };

            // Quick actions
            window.showQuickActions = function() {
                // TODO: Implement quick actions menu
                console.log('Show quick actions');
            };

            // Placeholder functions
            window.showResume = function() {
                // TODO: Implement resume builder
                alert('Resume builder coming soon!');
            };

            window.showMessages = function() {
                // TODO: Implement messages list
                alert('Messages feature coming soon!');
            };

            // Event listeners
            document.addEventListener('click', function(e) {
                if (e.target.dataset.event === 'click:openChat') {
                    openChat();
                }
            });
        })();
    </script>

</body>
</html>