<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Koi Maritime - Connect with Sailors Worldwide</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'navy': '#1e3a8a',
                        'ocean-teal': '#0891b2',
                        'light-teal': '#67e8f9',
                        'maritime-gray': '#64748b',
                        'ocean-mist': '#f0f9ff'
                    },
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                        'outfit': ['Outfit', 'sans-serif']
                    }
                }
            }
        };
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Outfit:wght@400;500;600&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-ocean-mist font-inter">
    <!-- @COMPONENT: Header [navigation and user profile] -->
    <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-4">
                    <div class="flex items-center space-x-2">
                        <div class="w-10 h-10 bg-gradient-to-br from-navy to-ocean-teal rounded-lg flex items-center justify-center">
                            <i class="fas fa-fish text-white text-lg"></i>
                        </div>
                        <span class="text-xl font-bold text-navy font-outfit">Koi Maritime</span>
                    </div>
                    <nav class="hidden md:flex space-x-6 ml-8">
                        <a href="#" class="text-navy font-medium border-b-2 border-ocean-teal pb-1">Discover</a>
                        <a href="#" class="text-maritime-gray hover:text-navy transition-colors">My Vessels</a>
                        <a href="#" class="text-maritime-gray hover:text-navy transition-colors">Timeline</a>
                        <a href="#" class="text-maritime-gray hover:text-navy transition-colors">Messages</a>
                    </nav>
                </div>
                
                <div class="flex items-center space-x-4">
                    <button class="relative p-2 text-maritime-gray hover:text-navy transition-colors">
                        <i class="fas fa-bell text-lg"></i>
                        <span class="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">3</span>
                    </button>
                    <div class="flex items-center space-x-3 cursor-pointer">
                        <div class="w-8 h-8 rounded-full overflow-hidden">
                            <!-- Portrait of a professional sailor in uniform -->
                            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=100&h=100" 
                                 alt="Captain profile" class="w-full h-full object-cover" data-mock="true">
                        </div>
                        <span class="hidden md:block text-sm font-medium text-navy" data-bind="user.name">Capt. Rodriguez</span>
                    </div>
                </div>
            </div>
        </div>
    </header>
    <!-- @END_COMPONENT: Header -->

    <!-- @COMPONENT: MainContent [discovery interface and vessel connections] -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div class="grid grid-cols-1 lg:grid-cols-4 gap-6">
            
            <!-- @COMPONENT: DiscoveryFilters [location and vessel search controls] -->
            <div class="lg:col-span-1">
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 sticky top-24">
                    <h2 class="text-lg font-semibold text-navy mb-4 flex items-center">
                        <i class="fas fa-search mr-2 text-ocean-teal"></i>
                        Who's There?
                    </h2>
                    
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-maritime-gray mb-2">Current Location</label>
                            <div class="relative">
                                <input type="text" placeholder="Hamburg Port" 
                                       class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-ocean-teal focus:border-transparent"
                                       data-bind="filters.location" data-mock="true">
                                <i class="fas fa-map-marker-alt absolute left-3 top-2.5 text-ocean-teal"></i>
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-maritime-gray mb-2">Distance Range</label>
                            <input type="range" min="1" max="500" value="50" 
                                   class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                                   data-bind="filters.distance">
                            <div class="flex justify-between text-xs text-maritime-gray mt-1">
                                <span>1km</span>
                                <span data-bind="filters.distance">50km</span>
                                <span>500km</span>
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-maritime-gray mb-2">Vessel Type</label>
                            <select class="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-ocean-teal focus:border-transparent"
                                    data-bind="filters.vesselType">
                                <option>All Vessels</option>
                                <option>Container Ships</option>
                                <option>Bulk Carriers</option>
                                <option>Tankers</option>
                                <option>Cruise Ships</option>
                            </select>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-maritime-gray mb-2">Timeline</label>
                            <select class="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-ocean-teal focus:border-transparent"
                                    data-bind="filters.timeline">
                                <option>Next 7 days</option>
                                <option>Next 30 days</option>
                                <option>Next 90 days</option>
                            </select>
                        </div>
                        
                        <button class="w-full bg-ocean-teal text-white py-2 rounded-lg hover:bg-navy transition-colors font-medium"
                                data-event="click:applyFilters">
                            <i class="fas fa-search mr-2"></i>
                            Update Search
                        </button>
                    </div>
                </div>
            </div>
            <!-- @END_COMPONENT: DiscoveryFilters -->
            
            <!-- @COMPONENT: DiscoveryResults [vessel and sailor connections] -->
            <div class="lg:col-span-3">
                <!-- Quick Stats -->
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                    <div class="bg-white rounded-lg p-4 text-center border border-gray-200">
                        <div class="text-2xl font-bold text-navy" data-bind="stats.sailorsNearby">24</div>
                        <div class="text-sm text-maritime-gray">Sailors Nearby</div>
                    </div>
                    <div class="bg-white rounded-lg p-4 text-center border border-gray-200">
                        <div class="text-2xl font-bold text-ocean-teal" data-bind="stats.vesselsInPort">12</div>
                        <div class="text-sm text-maritime-gray">Vessels in Port</div>
                    </div>
                    <div class="bg-white rounded-lg p-4 text-center border border-gray-200">
                        <div class="text-2xl font-bold text-navy" data-bind="stats.upcomingArrivals">8</div>
                        <div class="text-sm text-maritime-gray">Arriving Today</div>
                    </div>
                    <div class="bg-white rounded-lg p-4 text-center border border-gray-200">
                        <div class="text-2xl font-bold text-ocean-teal" data-bind="stats.activeConnections">15</div>
                        <div class="text-sm text-maritime-gray">Active Chats</div>
                    </div>
                </div>

                <!-- Port Timeline -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
                    <h3 class="text-lg font-semibold text-navy mb-4 flex items-center">
                        <i class="fas fa-clock mr-2 text-ocean-teal"></i>
                        Hamburg Port Timeline - Next 7 Days
                    </h3>
                    
                    <div class="space-y-4">
                        <!-- @MAP: timeline.map(day => ( -->
                        <div class="border-l-4 border-ocean-teal pl-4" data-mock="true">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="font-medium text-navy">Today - March 15</h4>
                                <span class="text-sm text-maritime-gray">6 vessels</span>
                            </div>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-3">
                                <div class="bg-ocean-mist rounded-lg p-3">
                                    <div class="flex justify-between items-start">
                                        <div>
                                            <div class="font-medium text-navy">MV Atlantic Pioneer</div>
                                            <div class="text-sm text-maritime-gray">IMO: 9234567</div>
                                            <div class="text-sm text-ocean-teal">Berth A-12 • 3 sailors</div>
                                        </div>
                                        <button class="text-ocean-teal hover:text-navy transition-colors">
                                            <i class="fas fa-comment-dots"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="bg-ocean-mist rounded-lg p-3">
                                    <div class="flex justify-between items-start">
                                        <div>
                                            <div class="font-medium text-navy">MV Northern Star</div>
                                            <div class="text-sm text-maritime-gray">IMO: 9345678</div>
                                            <div class="text-sm text-ocean-teal">Berth C-8 • 5 sailors</div>
                                        </div>
                                        <button class="text-ocean-teal hover:text-navy transition-colors">
                                            <i class="fas fa-comment-dots"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- @END_MAP )) -->
                        
                        <div class="border-l-4 border-gray-300 pl-4" data-mock="true">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="font-medium text-maritime-gray">Tomorrow - March 16</h4>
                                <span class="text-sm text-maritime-gray">4 vessels</span>
                            </div>
                            <div class="text-sm text-maritime-gray">
                                Expected arrivals: MV Pacific Dream (10:00), MV European Grace (14:30)
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sailor Connections -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 p-6">
                    <h3 class="text-lg font-semibold text-navy mb-4 flex items-center">
                        <i class="fas fa-users mr-2 text-ocean-teal"></i>
                        Connect with Sailors
                    </h3>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <!-- @MAP: sailors.map(sailor => ( -->
                        <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow" data-mock="true">
                            <div class="flex items-start space-x-3">
                                <div class="w-12 h-12 rounded-full overflow-hidden flex-shrink-0">
                                    <!-- Professional maritime officer portrait -->
                                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=100&h=100" 
                                         alt="Chief Engineer portrait" class="w-full h-full object-cover">
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center justify-between">
                                        <h4 class="font-medium text-navy" data-bind="sailor.name">Chief Eng. Martinez</h4>
                                        <div class="flex items-center space-x-1">
                                            <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                            <span class="text-xs text-maritime-gray">Online</span>
                                        </div>
                                    </div>
                                    <div class="text-sm text-maritime-gray mb-2" data-bind="sailor.vessel">MV Atlantic Pioneer</div>
                                    <div class="text-xs text-ocean-teal mb-2" data-bind="sailor.port">Hamburg Port - Berth A-12</div>
                                    <div class="text-xs text-maritime-gray mb-3" data-bind="sailor.schedule">In port until March 18, 2024</div>
                                    <div class="flex space-x-2">
                                        <button class="flex-1 bg-ocean-teal text-white text-sm py-2 rounded-lg hover:bg-navy transition-colors"
                                                data-event="click:connectSailor">
                                            <i class="fas fa-handshake mr-1"></i>
                                            Connect
                                        </button>
                                        <button class="px-3 py-2 border border-ocean-teal text-ocean-teal rounded-lg hover:bg-ocean-teal hover:text-white transition-colors"
                                                data-event="click:viewProfile">
                                            <i class="fas fa-user"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow" data-mock="true">
                            <div class="flex items-start space-x-3">
                                <div class="w-12 h-12 rounded-full overflow-hidden flex-shrink-0">
                                    <!-- Professional sailor portrait -->
                                    <img src="https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=100&h=100" 
                                         alt="Second Officer portrait" class="w-full h-full object-cover">
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center justify-between">
                                        <h4 class="font-medium text-navy" data-bind="sailor.name">2nd Off. Jensen</h4>
                                        <div class="flex items-center space-x-1">
                                            <div class="w-2 h-2 bg-yellow-500 rounded-full"></div>
                                            <span class="text-xs text-maritime-gray">Away</span>
                                        </div>
                                    </div>
                                    <div class="text-sm text-maritime-gray mb-2" data-bind="sailor.vessel">MV Northern Star</div>
                                    <div class="text-xs text-ocean-teal mb-2" data-bind="sailor.port">Hamburg Port - Berth C-8</div>
                                    <div class="text-xs text-maritime-gray mb-3" data-bind="sailor.schedule">In port until March 17, 2024</div>
                                    <div class="flex space-x-2">
                                        <button class="flex-1 bg-ocean-teal text-white text-sm py-2 rounded-lg hover:bg-navy transition-colors"
                                                data-event="click:connectSailor">
                                            <i class="fas fa-handshake mr-1"></i>
                                            Connect
                                        </button>
                                        <button class="px-3 py-2 border border-ocean-teal text-ocean-teal rounded-lg hover:bg-ocean-teal hover:text-white transition-colors"
                                                data-event="click:viewProfile">
                                            <i class="fas fa-user"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow" data-mock="true">
                            <div class="flex items-start space-x-3">
                                <div class="w-12 h-12 rounded-full overflow-hidden flex-shrink-0">
                                    <!-- Female maritime officer portrait -->
                                    <img src="https://pixabay.com/get/gd6d0542b49a946fdfba61fd85e04d60c0abd9b06cbe277d65c2064b50f441e0c750c0fe596f4bd8ae8e589febb9383fcb462fdd6b13614670b7f8f94d30f2b20_1280.jpg" 
                                         alt="Third Engineer portrait" class="w-full h-full object-cover">
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center justify-between">
                                        <h4 class="font-medium text-navy" data-bind="sailor.name">3rd Eng. Chen</h4>
                                        <div class="flex items-center space-x-1">
                                            <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                                            <span class="text-xs text-maritime-gray">Online</span>
                                        </div>
                                    </div>
                                    <div class="text-sm text-maritime-gray mb-2" data-bind="sailor.vessel">MV Pacific Dream</div>
                                    <div class="text-xs text-ocean-teal mb-2" data-bind="sailor.port">Arriving tomorrow 10:00</div>
                                    <div class="text-xs text-maritime-gray mb-3" data-bind="sailor.schedule">Will be in port March 16-20</div>
                                    <div class="flex space-x-2">
                                        <button class="flex-1 bg-gray-300 text-gray-600 text-sm py-2 rounded-lg cursor-not-allowed"
                                                disabled>
                                            <i class="fas fa-clock mr-1"></i>
                                            Arriving Soon
                                        </button>
                                        <button class="px-3 py-2 border border-ocean-teal text-ocean-teal rounded-lg hover:bg-ocean-teal hover:text-white transition-colors"
                                                data-event="click:viewProfile">
                                            <i class="fas fa-user"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow" data-mock="true">
                            <div class="flex items-start space-x-3">
                                <div class="w-12 h-12 rounded-full overflow-hidden flex-shrink-0">
                                    <!-- Experienced captain portrait -->
                                    <img src="https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=100&h=100" 
                                         alt="Captain portrait" class="w-full h-full object-cover">
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center justify-between">
                                        <h4 class="font-medium text-navy" data-bind="sailor.name">Capt. Thompson</h4>
                                        <div class="flex items-center space-x-1">
                                            <div class="w-2 h-2 bg-red-500 rounded-full"></div>
                                            <span class="text-xs text-maritime-gray">Busy</span>
                                        </div>
                                    </div>
                                    <div class="text-sm text-maritime-gray mb-2" data-bind="sailor.vessel">MV European Grace</div>
                                    <div class="text-xs text-ocean-teal mb-2" data-bind="sailor.port">Arriving tomorrow 14:30</div>
                                    <div class="text-xs text-maritime-gray mb-3" data-bind="sailor.schedule">Will be in port March 16-19</div>
                                    <div class="flex space-x-2">
                                        <button class="flex-1 bg-gray-300 text-gray-600 text-sm py-2 rounded-lg cursor-not-allowed"
                                                disabled>
                                            <i class="fas fa-clock mr-1"></i>
                                            Arriving Soon
                                        </button>
                                        <button class="px-3 py-2 border border-ocean-teal text-ocean-teal rounded-lg hover:bg-ocean-teal hover:text-white transition-colors"
                                                data-event="click:viewProfile">
                                            <i class="fas fa-user"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- @END_MAP )) -->
                    </div>
                </div>
            </div>
            <!-- @END_COMPONENT: DiscoveryResults -->
        </div>
    </main>
    <!-- @END_COMPONENT: MainContent -->

    <!-- @COMPONENT: FloatingActionButton [quick vessel registration] -->
    <div class="fixed bottom-6 right-6 z-40">
        <button class="w-14 h-14 bg-ocean-teal text-white rounded-full shadow-lg hover:bg-navy transition-colors flex items-center justify-center"
                data-event="click:openVesselRegistration"
                title="Register Your Vessel">
            <i class="fas fa-ship text-xl"></i>
        </button>
    </div>
    <!-- @END_COMPONENT: FloatingActionButton -->

    <!-- @COMPONENT: VesselRegistrationModal [IMO registration form] -->
    <div id="vesselModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden flex items-center justify-center p-4">
        <div class="bg-white rounded-xl max-w-md w-full p-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-navy">Register Your Vessel</h3>
                <button class="text-maritime-gray hover:text-navy transition-colors" data-event="click:closeModal">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            
            <form class="space-y-4" data-event="submit:registerVessel">
                <div>
                    <label class="block text-sm font-medium text-maritime-gray mb-2">IMO Number</label>
                    <input type="text" placeholder="IMO 9234567" 
                           class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-ocean-teal focus:border-transparent"
                           data-bind="vessel.imo" required>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-maritime-gray mb-2">Current Port</label>
                    <input type="text" placeholder="Hamburg Port" 
                           class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-ocean-teal focus:border-transparent"
                           data-bind="vessel.currentPort" required>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-maritime-gray mb-2">Departure Date</label>
                    <input type="date" 
                           class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-ocean-teal focus:border-transparent"
                           data-bind="vessel.departureDate" required>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-maritime-gray mb-2">Your Position</label>
                    <select class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-ocean-teal focus:border-transparent"
                            data-bind="sailor.position" required>
                        <option value="">Select Position</option>
                        <option value="captain">Captain</option>
                        <option value="chief_engineer">Chief Engineer</option>
                        <option value="chief_officer">Chief Officer</option>
                        <option value="second_engineer">Second Engineer</option>
                        <option value="second_officer">Second Officer</option>
                        <option value="third_engineer">Third Engineer</option>
                        <option value="third_officer">Third Officer</option>
                    </select>
                </div>
                
                <div class="flex space-x-3 pt-4">
                    <button type="button" 
                            class="flex-1 py-3 border border-gray-300 text-maritime-gray rounded-lg hover:bg-gray-50 transition-colors"
                            data-event="click:closeModal">
                        Cancel
                    </button>
                    <button type="submit" 
                            class="flex-1 py-3 bg-ocean-teal text-white rounded-lg hover:bg-navy transition-colors">
                        Register Vessel
                    </button>
                </div>
            </form>
        </div>
    </div>
    <!-- @END_COMPONENT: VesselRegistrationModal -->

    <!-- @COMPONENT: ChatInterface [WhatsApp-style messaging] -->
    <div id="chatPanel" class="fixed right-0 top-16 bottom-0 w-80 bg-white shadow-xl transform translate-x-full transition-transform duration-300 z-40 hidden lg:block">
        <div class="p-4 border-b border-gray-200 bg-navy text-white">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 rounded-full overflow-hidden">
                        <!-- Professional maritime officer portrait -->
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=100&h=100" 
                             alt="Chief Engineer portrait" class="w-full h-full object-cover">
                    </div>
                    <div>
                        <div class="font-medium" data-bind="chat.recipient">Chief Eng. Martinez</div>
                        <div class="text-xs opacity-75" data-bind="chat.vessel">MV Atlantic Pioneer</div>
                    </div>
                </div>
                <button class="text-white hover:text-gray-300 transition-colors" data-event="click:closeChat">
                    <i class="fas fa-times"></i>
                </button>
            </div>
        </div>
        
        <div class="flex-1 p-4 space-y-3 h-96 overflow-y-auto">
            <!-- @MAP: messages.map(message => ( -->
            <div class="flex justify-start" data-mock="true">
                <div class="bg-gray-100 rounded-lg p-3 max-w-xs">
                    <div class="text-sm" data-bind="message.content">Hey! I see you're also in Hamburg Port. Want to grab coffee during break?</div>
                    <div class="text-xs text-maritime-gray mt-1" data-bind="message.timestamp">2:15 PM</div>
                </div>
            </div>
            
            <div class="flex justify-end" data-mock="true">
                <div class="bg-ocean-teal text-white rounded-lg p-3 max-w-xs">
                    <div class="text-sm" data-bind="message.content">Absolutely! I'm free at 3 PM. Know any good spots near the port?</div>
                    <div class="text-xs opacity-75 mt-1" data-bind="message.timestamp">2:18 PM</div>
                </div>
            </div>
            
            <div class="flex justify-start" data-mock="true">
                <div class="bg-gray-100 rounded-lg p-3 max-w-xs">
                    <div class="text-sm" data-bind="message.content">There's a great café called "Nautical Beans" just 5 minutes from Berth A. Meet there?</div>
                    <div class="text-xs text-maritime-gray mt-1" data-bind="message.timestamp">2:20 PM</div>
                </div>
            </div>
            <!-- @END_MAP )) -->
        </div>
        
        <div class="p-4 border-t border-gray-200">
            <div class="flex space-x-2">
                <input type="text" placeholder="Type a message..." 
                       class="flex-1 p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-ocean-teal focus:border-transparent"
                       data-bind="chat.newMessage">
                <button class="px-4 py-2 bg-ocean-teal text-white rounded-lg hover:bg-navy transition-colors"
                        data-event="click:sendMessage">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    </div>
    <!-- @END_COMPONENT: ChatInterface -->

    <!-- @COMPONENT: MobileNavigation [bottom navigation for mobile] -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 md:hidden z-40">
        <div class="grid grid-cols-4 h-16">
            <button class="flex flex-col items-center justify-center text-ocean-teal" data-event="click:navigate">
                <i class="fas fa-search text-lg"></i>
                <span class="text-xs mt-1">Discover</span>
            </button>
            <button class="flex flex-col items-center justify-center text-maritime-gray hover:text-navy transition-colors" data-event="click:navigate">
                <i class="fas fa-ship text-lg"></i>
                <span class="text-xs mt-1">Vessels</span>
            </button>
            <button class="flex flex-col items-center justify-center text-maritime-gray hover:text-navy transition-colors" data-event="click:navigate">
                <i class="fas fa-clock text-lg"></i>
                <span class="text-xs mt-1">Timeline</span>
            </button>
            <button class="flex flex-col items-center justify-center text-maritime-gray hover:text-navy transition-colors relative" data-event="click:navigate">
                <i class="fas fa-comment-dots text-lg"></i>
                <span class="text-xs mt-1">Messages</span>
                <span class="absolute top-2 right-3 w-4 h-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">3</span>
            </button>
        </div>
    </nav>
    <!-- @END_COMPONENT: MobileNavigation -->

    <!-- @STATE: isModalOpen:boolean = false -->
    <!-- @STATE: isChatOpen:boolean = false -->
    <!-- @STATE: activeFilters:object = {location: '', distance: 50, vesselType: '', timeline: ''} -->
    <!-- @STATE: connectionRequests:array = [] -->

    <script>
        (function() {
            // TODO: Implement vessel registration logic with IMO validation
            // TODO: Implement real-time chat functionality with WebSocket connection
            // TODO: Implement port schedule matching algorithm
            // TODO: Integrate with QAAQ user authentication system
            // TODO: Implement push notifications for new connections
            // TODO: Add privacy controls for vessel information sharing
            
            // Modal Management
            const vesselModal = document.getElementById('vesselModal');
            const chatPanel = document.getElementById('chatPanel');
            
            // Show vessel registration modal
            document.querySelector('[data-event="click:openVesselRegistration"]').addEventListener('click', function() {
                vesselModal.classList.remove('hidden');
            });
            
            // Close modal functionality
            document.querySelectorAll('[data-event="click:closeModal"]').forEach(button => {
                button.addEventListener('click', function() {
                    vesselModal.classList.add('hidden');
                });
            });
            
            // Close modal when clicking outside
            vesselModal.addEventListener('click', function(e) {
                if (e.target === vesselModal) {
                    vesselModal.classList.add('hidden');
                }
            });
            
            // Connect button functionality
            document.querySelectorAll('[data-event="click:connectSailor"]').forEach(button => {
                button.addEventListener('click', function() {
                    // TODO: Implement connection request logic
                    this.innerHTML = '<i class="fas fa-check mr-1"></i>Sent';
                    this.classList.remove('bg-ocean-teal', 'hover:bg-navy');
                    this.classList.add('bg-green-500');
                    this.disabled = true;
                });
            });
            
            // Form submission
            document.querySelector('[data-event="submit:registerVessel"]').addEventListener('submit', function(e) {
                e.preventDefault();
                // TODO: Implement vessel registration with IMO validation
                vesselModal.classList.add('hidden');
                // Show success notification
                alert('Vessel registered successfully!');
            });
            
            // Distance slider update
            const distanceSlider = document.querySelector('[data-bind="filters.distance"]');
            if (distanceSlider) {
                distanceSlider.addEventListener('input', function() {
                    document.querySelector('[data-bind="filters.distance"]:not(input)').textContent = this.value + 'km';
                });
            }
        })();
    </script>
</body>
</html>