<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>1234 Koi Hai? - Maritime Professional Discovery Platform</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'navy': '#1E3A8A',
                        'ocean': '#0891B2',
                        'maritime-gold': '#F59E0B',
                        'deep-sea': '#0F172A',
                        'light-ocean': '#E0F7FA'
                    },
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif'],
                        'mono': ['Roboto Mono', 'monospace']
                    }
                }
            }
        };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Roboto+Mono:wght@400;500&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-gray-50 font-inter">
    <!-- Mobile Navigation Header -->
    <header class="bg-navy shadow-lg sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between h-16">
                <!-- Logo -->
                <div class="flex items-center space-x-3">
                    <div class="flex-shrink-0">
                        <i class="fas fa-anchor text-maritime-gold text-2xl"></i>
                    </div>
                    <div>
                        <h1 class="text-white font-bold text-lg">1234 Koi Hai?</h1>
                        <p class="text-ocean text-xs font-mono">Maritime Discovery</p>
                    </div>
                </div>
                
                <!-- Navigation Icons -->
                <div class="flex items-center space-x-4">
                    <button class="text-white hover:text-maritime-gold transition-colors">
                        <i class="fas fa-bell text-lg"></i>
                    </button>
                    <button class="text-white hover:text-maritime-gold transition-colors">
                        <i class="fas fa-user-circle text-lg"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- QAAQ AI Question Bar -->
    <div class="bg-gradient-to-r from-ocean to-navy py-4 px-4">
        <div class="max-w-7xl mx-auto">
            <div class="bg-white rounded-xl shadow-lg p-4">
                <div class="flex items-center space-x-3 mb-3">
                    <i class="fas fa-robot text-ocean text-xl"></i>
                    <h2 class="font-semibold text-navy">QAAQ AI Assistant</h2>
                </div>
                <div class="relative">
                    <input 
                        type="text" 
                        placeholder="Ask about port services, crew connections, or local recommendations..."
                        class="w-full px-4 py-3 pr-12 border border-gray-200 rounded-lg focus:ring-2 focus:ring-ocean focus:border-transparent outline-none"
                    >
                    <button class="absolute right-3 top-1/2 transform -translate-y-1/2 text-ocean hover:text-navy transition-colors">
                        <i class="fas fa-search text-lg"></i>
                    </button>
                </div>
                <div class="flex items-center mt-2 text-sm text-gray-600">
                    <span class="font-mono">Examples:</span>
                    <span class="ml-2">"Best SIM cards in Hamburg Hafencity?" • "Cheap food near Rotterdam Waalhaven?"</span>
                </div>
            </div>
        </div>
    </div>

    <!-- CPSS Breadcrumb Navigation -->
    <div class="bg-white border-b border-gray-200 py-3 px-4">
        <div class="max-w-7xl mx-auto">
            <nav class="flex items-center space-x-2 text-sm">
                <a href="#" class="text-ocean hover:text-navy font-medium">🌍 Germany</a>
                <i class="fas fa-chevron-right text-gray-400 text-xs"></i>
                <a href="#" class="text-ocean hover:text-navy font-medium">⚓ Hamburg</a>
                <i class="fas fa-chevron-right text-gray-400 text-xs"></i>
                <a href="#" class="text-ocean hover:text-navy font-medium">🏘️ Hafencity</a>
                <i class="fas fa-chevron-right text-gray-400 text-xs"></i>
                <span class="text-gray-600">🛍️ Services</span>
            </nav>
        </div>
    </div>

    <!-- Main Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <!-- Location Status Card -->
        <div class="bg-white rounded-xl shadow-lg p-6 mb-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-4">
                    <!-- Maritime officers meeting casually in Hamburg port -->
                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150" alt="Maritime officers in Hamburg port" class="w-16 h-16 rounded-full object-cover">
                    <div>
                        <h3 class="font-semibold text-navy text-lg">Currently in Hamburg Port</h3>
                        <p class="text-gray-600">📍 Hafencity District • 🕐 14:30 CET</p>
                        <p class="text-sm text-ocean font-mono">12 Officers • 8 Crew Members nearby</p>
                    </div>
                </div>
                <button class="px-4 py-2 bg-ocean text-white rounded-lg hover:bg-navy transition-colors">
                    <i class="fas fa-location-arrow mr-2"></i>Update Location
                </button>
            </div>
        </div>

        <!-- Quick Discovery Tabs -->
        <div class="flex space-x-4 mb-6 overflow-x-auto pb-2">
            <button class="flex-shrink-0 px-6 py-3 bg-navy text-white rounded-lg font-medium">
                <i class="fas fa-users mr-2"></i>Officers Nearby
            </button>
            <button class="flex-shrink-0 px-6 py-3 bg-white text-navy border border-gray-200 rounded-lg font-medium hover:bg-gray-50">
                <i class="fas fa-ship mr-2"></i>Crew Nearby
            </button>
            <button class="flex-shrink-0 px-6 py-3 bg-white text-navy border border-gray-200 rounded-lg font-medium hover:bg-gray-50">
                <i class="fas fa-concierge-bell mr-2"></i>Port Services
            </button>
            <button class="flex-shrink-0 px-6 py-3 bg-white text-navy border border-gray-200 rounded-lg font-medium hover:bg-gray-50">
                <i class="fas fa-utensils mr-2"></i>Restaurants
            </button>
        </div>

        <!-- Distance Filter -->
        <div class="bg-white rounded-xl shadow-lg p-4 mb-6">
            <div class="flex items-center justify-between">
                <span class="font-medium text-navy">Search Radius</span>
                <span class="text-ocean font-mono">5.2 km</span>
            </div>
            <input type="range" min="1" max="50" value="5" class="w-full mt-3 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider">
            <div class="flex justify-between text-xs text-gray-500 mt-1">
                <span>1km</span>
                <span>50km</span>
            </div>
        </div>

        <!-- Officers Nearby Section -->
        <div class="mb-8">
            <h2 class="text-xl font-bold text-navy mb-4 flex items-center">
                <i class="fas fa-users text-ocean mr-3"></i>Maritime Officers Nearby
            </h2>
            
            <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                <!-- Officer Card 1 -->
                <div class="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow">
                    <div class="flex items-center space-x-4 mb-4">
                        <!-- Ship crew at port cities -->
                        <img src="https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&auto=format&fit=crop&w=80&h=80" alt="Chief Engineer at port" class="w-16 h-16 rounded-full object-cover">
                        <div class="flex-1">
                            <h3 class="font-semibold text-navy">Piyush Gupta</h3>
                            <p class="text-ocean font-mono text-sm">Chief Engineer</p>
                            <p class="text-gray-600 text-sm">MT Solar Claire</p>
                        </div>
                        <div class="text-right">
                            <p class="text-maritime-gold font-mono text-sm font-semibold">1.2 km</p>
                            <div class="flex text-yellow-400 text-xs">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                    </div>
                    <div class="flex space-x-2 mb-4">
                        <span class="px-2 py-1 bg-ocean/10 text-ocean text-xs rounded-full">21+ years exp</span>
                        <span class="px-2 py-1 bg-maritime-gold/10 text-maritime-gold text-xs rounded-full">MAN B&W Expert</span>
                    </div>
                    <button class="w-full py-2 bg-ocean text-white rounded-lg hover:bg-navy transition-colors">
                        <i class="fas fa-comment-dots mr-2"></i>Say Hello
                    </button>
                </div>

                <!-- Officer Card 2 -->
                <div class="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow">
                    <div class="flex items-center space-x-4 mb-4">
                        <!-- Maritime officers at port meeting -->
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=80&h=80" alt="Second Engineer" class="w-16 h-16 rounded-full object-cover">
                        <div class="flex-1">
                            <h3 class="font-semibold text-navy">Erik Larsson</h3>
                            <p class="text-ocean font-mono text-sm">2nd Engineer</p>
                            <p class="text-gray-600 text-sm">MV Nordic Spirit</p>
                        </div>
                        <div class="text-right">
                            <p class="text-maritime-gold font-mono text-sm font-semibold">2.8 km</p>
                            <div class="flex text-yellow-400 text-xs">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="far fa-star"></i>
                            </div>
                        </div>
                    </div>
                    <div class="flex space-x-2 mb-4">
                        <span class="px-2 py-1 bg-ocean/10 text-ocean text-xs rounded-full">8 years exp</span>
                        <span class="px-2 py-1 bg-maritime-gold/10 text-maritime-gold text-xs rounded-full">Wartsila Expert</span>
                    </div>
                    <button class="w-full py-2 bg-ocean text-white rounded-lg hover:bg-navy transition-colors">
                        <i class="fas fa-comment-dots mr-2"></i>Say Hello
                    </button>
                </div>

                <!-- Officer Card 3 -->
                <div class="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow">
                    <div class="flex items-center space-x-4 mb-4">
                        <!-- Maritime professional at port -->
                        <img src="https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?ixlib=rb-4.0.3&auto=format&fit=crop&w=80&h=80" alt="Electrical Officer" class="w-16 h-16 rounded-full object-cover">
                        <div class="flex-1">
                            <h3 class="font-semibold text-navy">Ahmed Hassan</h3>
                            <p class="text-ocean font-mono text-sm">Electrical Officer</p>
                            <p class="text-gray-600 text-sm">MV Hamburg Express</p>
                        </div>
                        <div class="text-right">
                            <p class="text-maritime-gold font-mono text-sm font-semibold">4.1 km</p>
                            <div class="flex text-yellow-400 text-xs">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                        </div>
                    </div>
                    <div class="flex space-x-2 mb-4">
                        <span class="px-2 py-1 bg-ocean/10 text-ocean text-xs rounded-full">12 years exp</span>
                        <span class="px-2 py-1 bg-maritime-gold/10 text-maritime-gold text-xs rounded-full">Automation Expert</span>
                    </div>
                    <button class="w-full py-2 bg-ocean text-white rounded-lg hover:bg-navy transition-colors">
                        <i class="fas fa-comment-dots mr-2"></i>Say Hello
                    </button>
                </div>
            </div>
        </div>

        <!-- Port Services Section -->
        <div class="mb-8">
            <h2 class="text-xl font-bold text-navy mb-4 flex items-center">
                <i class="fas fa-concierge-bell text-ocean mr-3"></i>Port Services in Hafencity
            </h2>
            
            <div class="grid gap-4 md:grid-cols-2">
                <!-- Service Category 1 -->
                <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                    <!-- Port vendor services in Hamburg -->
                    <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=200" alt="Port vendor services in Hamburg" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <h3 class="font-semibold text-navy text-lg mb-2">📱 SIM Cards & Mobile Services</h3>
                        <p class="text-gray-600 text-sm mb-4">Local SIM cards, international calling plans, and mobile top-ups</p>
                        <div class="flex items-center justify-between">
                            <div class="flex space-x-2">
                                <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">8 providers</span>
                                <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">24/7 available</span>
                            </div>
                            <button class="text-ocean hover:text-navy transition-colors">
                                <i class="fas fa-arrow-right"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Service Category 2 -->
                <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                    <!-- Sailors shore leave activities -->
                    <img src="https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=200" alt="Sailors enjoying shore leave activities" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <h3 class="font-semibold text-navy text-lg mb-2">🍽️ Restaurants & Dining</h3>
                        <p class="text-gray-600 text-sm mb-4">Crew-friendly restaurants, international cuisine, and budget options</p>
                        <div class="flex items-center justify-between">
                            <div class="flex space-x-2">
                                <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">23 restaurants</span>
                                <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">₹₹-₹₹₹₹</span>
                            </div>
                            <button class="text-ocean hover:text-navy transition-colors">
                                <i class="fas fa-arrow-right"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Service Category 3 -->
                <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                    <!-- Port transportation services -->
                    <img src="https://images.unsplash.com/photo-1449824913935-59a10b8d2000?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=200" alt="Port transportation and taxi services" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <h3 class="font-semibold text-navy text-lg mb-2">🚕 Transportation</h3>
                        <p class="text-gray-600 text-sm mb-4">Taxis, port shuttles, public transport, and crew transport services</p>
                        <div class="flex items-center justify-between">
                            <div class="flex space-x-2">
                                <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">24/7 service</span>
                                <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">Multi-language</span>
                            </div>
                            <button class="text-ocean hover:text-navy transition-colors">
                                <i class="fas fa-arrow-right"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Service Category 4 -->
                <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                    <!-- Maritime shore activities -->
                    <img src="https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&h=200" alt="Shopping and leisure activities for maritime crew" class="w-full h-48 object-cover">
                    <div class="p-6">
                        <h3 class="font-semibold text-navy text-lg mb-2">🛍️ Shopping & Leisure</h3>
                        <p class="text-gray-600 text-sm mb-4">Duty-free shops, malls, entertainment centers, and crew recreation</p>
                        <div class="flex items-center justify-between">
                            <div class="flex space-x-2">
                                <span class="px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">15 venues</span>
                                <span class="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">Crew discounts</span>
                            </div>
                            <button class="text-ocean hover:text-navy transition-colors">
                                <i class="fas fa-arrow-right"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- QAAQ Integration Card -->
        <div class="bg-gradient-to-r from-navy to-deep-sea rounded-xl shadow-lg p-6 text-white mb-8">
            <div class="flex items-center space-x-4 mb-4">
                <i class="fas fa-brain text-maritime-gold text-3xl"></i>
                <div>
                    <h3 class="text-xl font-bold">Powered by QAAQ AI</h3>
                    <p class="text-light-ocean">Maritime Engineering Knowledge Hub</p>
                </div>
            </div>
            <p class="mb-4">Connect with the world's largest maritime engineering community. Get instant answers to technical questions and access expert knowledge.</p>
            <div class="flex space-x-4">
                <button class="px-6 py-2 bg-maritime-gold text-navy rounded-lg font-medium hover:bg-yellow-500 transition-colors">
                    Visit QAAQ Platform
                </button>
                <button class="px-6 py-2 border border-white text-white rounded-lg font-medium hover:bg-white hover:text-navy transition-colors">
                    WhatsApp Bot
                </button>
            </div>
        </div>
    </main>

    <!-- Bottom Navigation (Mobile) -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 md:hidden">
        <div class="flex items-center justify-around py-2">
            <button class="flex flex-col items-center p-2 text-navy">
                <i class="fas fa-home text-lg"></i>
                <span class="text-xs mt-1">Home</span>
            </button>
            <button class="flex flex-col items-center p-2 text-gray-600">
                <i class="fas fa-search text-lg"></i>
                <span class="text-xs mt-1">Discover</span>
            </button>
            <button class="flex flex-col items-center p-2 text-gray-600">
                <i class="fas fa-comments text-lg"></i>
                <span class="text-xs mt-1">Messages</span>
            </button>
            <button class="flex flex-col items-center p-2 text-gray-600">
                <i class="fas fa-user text-lg"></i>
                <span class="text-xs mt-1">Profile</span>
            </button>
        </div>
    </nav>

    <!-- Custom Styles for Slider -->
    <style>
        .slider::-webkit-slider-thumb {
            appearance: none;
            height: 20px;
            width: 20px;
            border-radius: 50%;
            background: #0891B2;
            cursor: pointer;
        }
        .slider::-moz-range-thumb {
            height: 20px;
            width: 20px;
            border-radius: 50%;
            background: #0891B2;
            cursor: pointer;
            border: none;
        }
    </style>

    <script>
        (function() {
            // TODO: Implement business logic for officer discovery, location services, and QAAQ integration
            
            // Placeholder for location-based discovery
            function updateNearbyOfficers() {
                // Implementation would fetch real officer data based on current location
                console.log('Updating nearby officers based on location');
            }
            
            // Placeholder for QAAQ AI integration
            function submitQuestionToQAAQ() {
                // Implementation would integrate with QAAQ AI system
                console.log('Submitting question to QAAQ AI system');
            }
            
            // Distance slider interaction
            const slider = document.querySelector('.slider');
            slider.addEventListener('input', function() {
                // Implementation would update search radius and refresh results
                console.log('Search radius updated to:', this.value + 'km');
            });
            
            // Say Hello button interactions
            document.querySelectorAll('button').forEach(button => {
                if (button.textContent.includes('Say Hello')) {
                    button.addEventListener('click', function() {
                        // Implementation would initiate contact with selected officer
                        console.log('Initiating contact with officer');
                    });
                }
            });
        })();
    </script>
</body>
</html>