<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>1234 Koi Hai? - Maritime Community Groups</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'navy': '#1B365D',
                        'ocean-teal': '#2D7A7A',
                        'light-teal': '#4A9B9B',
                        'maritime-gray': '#F8FAFB',
                        'deep-blue': '#0F2A44'
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif']
                    }
                }
            }
        };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-maritime-gray font-sans">
    <!-- @COMPONENT: NavigationHeader -->
    <header class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-4">
                    <div class="text-2xl font-bold text-navy">
                        1234 <span class="text-ocean-teal">कोई है?</span>
                    </div>
                    <div class="hidden md:flex text-sm text-gray-600">
                        Sister app to <a href="https://qaaqit.replit.app/" class="text-ocean-teal hover:underline" data-mock="true">QAAQ</a>
                    </div>
                </div>
                
                <!-- Mode Toggle -->
                <div class="flex items-center space-x-4">
                    <div class="bg-gray-100 rounded-lg p-1 flex">
                        <button class="px-4 py-2 text-sm font-medium text-white bg-navy rounded-md transition-colors" data-event="click:togglePortMode">
                            <i class="fas fa-anchor mr-2"></i>Port Mode
                        </button>
                        <button class="px-4 py-2 text-sm font-medium text-gray-600 hover:text-navy transition-colors" data-event="click:toggleAirportMode">
                            <i class="fas fa-plane mr-2"></i>Airport Mode
                        </button>
                    </div>
                    
                    <!-- Profile -->
                    <div class="relative">
                        <button class="flex items-center space-x-3 text-sm" data-event="click:toggleProfile">
                            <!-- Professional maritime profile image -->
                            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150" alt="Chief Engineer Profile" class="w-8 h-8 rounded-full object-cover">
                            <span class="hidden md:block text-gray-700" data-bind="user.name">Piyush G.</span>
                            <i class="fas fa-chevron-down text-gray-400"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </header>
    <!-- @END_COMPONENT: NavigationHeader -->

    <!-- @COMPONENT: HeroSection -->
    <section class="bg-gradient-to-br from-navy via-deep-blue to-ocean-teal text-white py-16">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center">
                <h1 class="text-4xl md:text-6xl font-bold mb-6">
                    Find Your <span class="text-teal-300">Maritime Crew</span>
                </h1>
                <p class="text-xl md:text-2xl mb-8 text-blue-100">
                    Connect with sailors, engineers, and maritime professionals at ports and airports worldwide
                </p>
                
                <!-- QAAQ AI Search Integration -->
                <div class="max-w-3xl mx-auto mb-8">
                    <div class="relative">
                        <input type="text" 
                               placeholder="Ask QAAQ AI or find groups nearby..." 
                               class="w-full px-6 py-4 text-lg rounded-xl border-0 shadow-lg text-gray-800 placeholder-gray-500 focus:ring-4 focus:ring-teal-300 focus:outline-none"
                               data-implementation="Integrate with QAAQ AI system">
                        <button class="absolute right-2 top-2 bottom-2 px-6 bg-ocean-teal text-white rounded-lg hover:bg-light-teal transition-colors">
                            <i class="fas fa-search"></i>
                        </button>
                    </div>
                    <div class="text-sm text-blue-200 mt-2">
                        Powered by QAAQ AI • Ask technical questions or discover local groups
                    </div>
                </div>

                <!-- Current Location Display -->
                <div class="flex items-center justify-center space-x-2 text-blue-200">
                    <i class="fas fa-map-marker-alt"></i>
                    <span data-bind="user.currentLocation">Hamburg Port, Germany</span>
                    <button class="text-teal-300 hover:underline ml-2" data-event="click:changeLocation">Change</button>
                </div>
            </div>
        </div>
    </section>
    <!-- @END_COMPONENT: HeroSection -->

    <!-- @COMPONENT: LocationBreadcrumb -->
    <section class="bg-white shadow-sm py-4">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center space-x-2 text-sm">
                <span class="text-gray-500">CPSS Location:</span>
                <nav class="flex items-center space-x-2">
                    <a href="#" class="text-ocean-teal hover:underline" data-mock="true">Germany</a>
                    <i class="fas fa-chevron-right text-gray-400 text-xs"></i>
                    <a href="#" class="text-ocean-teal hover:underline" data-mock="true">Hamburg</a>
                    <i class="fas fa-chevron-right text-gray-400 text-xs"></i>
                    <a href="#" class="text-ocean-teal hover:underline" data-mock="true">Port Area</a>
                    <i class="fas fa-chevron-right text-gray-400 text-xs"></i>
                    <span class="text-gray-800 font-medium">Container Terminal</span>
                </nav>
                <div class="ml-auto">
                    <button class="text-ocean-teal hover:underline text-sm" data-event="click:browseLocations">
                        <i class="fas fa-globe mr-1"></i>Browse All Locations
                    </button>
                </div>
            </div>
        </div>
    </section>
    <!-- @END_COMPONENT: LocationBreadcrumb -->

    <!-- @COMPONENT: QuickStats -->
    <section class="py-8">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                <div class="text-center">
                    <div class="text-3xl font-bold text-navy" data-bind="stats.nearbyOfficers">24</div>
                    <div class="text-sm text-gray-600">Officers Nearby</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-ocean-teal" data-bind="stats.activeGroups">12</div>
                    <div class="text-sm text-gray-600">Active Groups</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-navy" data-bind="stats.sailorsInTransit">8</div>
                    <div class="text-sm text-gray-600">In Transit</div>
                </div>
                <div class="text-center">
                    <div class="text-3xl font-bold text-ocean-teal" data-bind="stats.newConnections">156</div>
                    <div class="text-sm text-gray-600">New Connections</div>
                </div>
            </div>
        </div>
    </section>
    <!-- @END_COMPONENT: QuickStats -->

    <!-- @COMPONENT: GroupDiscovery -->
    <section class="py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center justify-between mb-8">
                <div>
                    <h2 class="text-3xl font-bold text-gray-900">Active Groups at Hamburg Port</h2>
                    <p class="text-gray-600 mt-2">Join conversations with maritime professionals in your area</p>
                </div>
                <button class="bg-ocean-teal text-white px-6 py-3 rounded-lg hover:bg-light-teal transition-colors flex items-center space-x-2" data-event="click:createGroup">
                    <i class="fas fa-plus"></i>
                    <span>Create Group</span>
                </button>
            </div>

            <!-- Group Cards Grid -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- @MAP: groups.map(group => ( -->
                
                <!-- Group Card 1: Taxi Share -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                    <div class="p-6">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-taxi text-yellow-600 text-xl"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-900" data-mock="true">Hamburg Taxi Share</h3>
                                    <div class="text-sm text-gray-500">
                                        <i class="fas fa-map-marker-alt mr-1"></i>Container Terminal • Public
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center text-green-600 text-sm">
                                <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                                <span data-bind="group.activeMembers">6 active</span>
                            </div>
                        </div>
                        
                        <p class="text-gray-600 text-sm mb-4" data-mock="true">Share taxi rides from Hamburg port to city center. Save money and meet fellow maritime professionals.</p>
                        
                        <div class="flex items-center justify-between">
                            <div class="flex -space-x-2">
                                <!-- Maritime professional avatars -->
                                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150" alt="Chief Engineer" class="w-8 h-8 rounded-full border-2 border-white object-cover">
                                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150" alt="2nd Engineer" class="w-8 h-8 rounded-full border-2 border-white object-cover">
                                <img src="https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150" alt="3rd Officer" class="w-8 h-8 rounded-full border-2 border-white object-cover">
                                <div class="w-8 h-8 bg-gray-200 rounded-full border-2 border-white flex items-center justify-center text-xs text-gray-600">+3</div>
                            </div>
                            <button class="bg-navy text-white px-4 py-2 rounded-lg hover:bg-deep-blue transition-colors text-sm" data-event="click:joinGroup">
                                Join Group
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Group Card 2: Sports -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                    <div class="p-6">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-football-ball text-green-600 text-xl"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-900" data-mock="true">Hamburg Crew Sports</h3>
                                    <div class="text-sm text-gray-500">
                                        <i class="fas fa-map-marker-alt mr-1"></i>Recreation Area • Public
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center text-green-600 text-sm">
                                <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                                <span data-bind="group.activeMembers">12 active</span>
                            </div>
                        </div>
                        
                        <p class="text-gray-600 text-sm mb-4" data-mock="true">Football, basketball, and fitness activities for maritime crew during port stays. All skill levels welcome!</p>
                        
                        <div class="flex items-center justify-between">
                            <div class="flex -space-x-2">
                                <!-- Maritime crew sports group avatars -->
                                <img src="https://pixabay.com/get/gb27a01565ae42fbe56751a4239115322e00b2ccaaa80bec2a93c12011b8ec9f47dfa02f5d72fdb0f7cd8a3992a55524ea0e33aed37814b9a2dd77ef45b84222d_1280.jpg" alt="Deck Officer" class="w-8 h-8 rounded-full border-2 border-white object-cover">
                                <img src="https://images.unsplash.com/photo-1582750433449-648ed127bb54?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150" alt="Chief Cook" class="w-8 h-8 rounded-full border-2 border-white object-cover">
                                <img src="https://images.unsplash.com/photo-1520975954732-35dd22299614?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150" alt="Able Seaman" class="w-8 h-8 rounded-full border-2 border-white object-cover">
                                <div class="w-8 h-8 bg-gray-200 rounded-full border-2 border-white flex items-center justify-center text-xs text-gray-600">+9</div>
                            </div>
                            <button class="bg-navy text-white px-4 py-2 rounded-lg hover:bg-deep-blue transition-colors text-sm" data-event="click:joinGroup">
                                Join Group
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Group Card 3: Technical Discussion -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                    <div class="p-6">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-cogs text-blue-600 text-xl"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-900" data-mock="true">MAN B&W Engineers</h3>
                                    <div class="text-sm text-gray-500">
                                        <i class="fas fa-map-marker-alt mr-1"></i>Technical Area • Private
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center text-green-600 text-sm">
                                <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                                <span data-bind="group.activeMembers">8 active</span>
                            </div>
                        </div>
                        
                        <p class="text-gray-600 text-sm mb-4" data-mock="true">Technical discussions for MAN B&W engine specialists. Share troubleshooting tips and maintenance experiences.</p>
                        
                        <div class="flex items-center justify-between">
                            <div class="flex -space-x-2">
                                <!-- Maritime engineering professionals -->
                                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150" alt="Chief Engineer" class="w-8 h-8 rounded-full border-2 border-white object-cover">
                                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150" alt="2nd Engineer" class="w-8 h-8 rounded-full border-2 border-white object-cover">
                                <img src="https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150" alt="3rd Engineer" class="w-8 h-8 rounded-full border-2 border-white object-cover">
                                <div class="w-8 h-8 bg-gray-200 rounded-full border-2 border-white flex items-center justify-center text-xs text-gray-600">+5</div>
                            </div>
                            <button class="bg-ocean-teal text-white px-4 py-2 rounded-lg hover:bg-light-teal transition-colors text-sm" data-event="click:requestAccess">
                                <i class="fas fa-lock mr-1"></i>Request Access
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Group Card 4: Food & Dining -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                    <div class="p-6">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-utensils text-orange-600 text-xl"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-900" data-mock="true">Hamburg Local Eats</h3>
                                    <div class="text-sm text-gray-500">
                                        <i class="fas fa-map-marker-alt mr-1"></i>Food & Dining • Public
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center text-green-600 text-sm">
                                <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                                <span data-bind="group.activeMembers">15 active</span>
                            </div>
                        </div>
                        
                        <p class="text-gray-600 text-sm mb-4" data-mock="true">Best local restaurants, affordable meals, and crew-friendly dining spots in Hamburg. Share your food discoveries!</p>
                        
                        <div class="flex items-center justify-between">
                            <div class="flex -space-x-2">
                                <!-- Diverse maritime crew -->
                                <img src="https://images.unsplash.com/photo-1582750433449-648ed127bb54?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150" alt="Chief Cook" class="w-8 h-8 rounded-full border-2 border-white object-cover">
                                <img src="https://images.unsplash.com/photo-1520975954732-35dd22299614?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150" alt="Able Seaman" class="w-8 h-8 rounded-full border-2 border-white object-cover">
                                <img src="https://pixabay.com/get/g1c4c06bbe18937435deb61cdb3ab3e2a8a63861d8d71eb8234a9f4dc1ed7146ca369fc6f6faab046906355360f108b8fbbc07a1717c2d3b157e92b405a12fe17_1280.jpg" alt="Deck Officer" class="w-8 h-8 rounded-full border-2 border-white object-cover">
                                <div class="w-8 h-8 bg-gray-200 rounded-full border-2 border-white flex items-center justify-center text-xs text-gray-600">+12</div>
                            </div>
                            <button class="bg-navy text-white px-4 py-2 rounded-lg hover:bg-deep-blue transition-colors text-sm" data-event="click:joinGroup">
                                Join Group
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Group Card 5: Shopping -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                    <div class="p-6">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-shopping-bag text-purple-600 text-xl"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-900" data-mock="true">Crew Shopping Tips</h3>
                                    <div class="text-sm text-gray-500">
                                        <i class="fas fa-map-marker-alt mr-1"></i>Shopping • Public
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center text-green-600 text-sm">
                                <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                                <span data-bind="group.activeMembers">9 active</span>
                            </div>
                        </div>
                        
                        <p class="text-gray-600 text-sm mb-4" data-mock="true">Share info about duty-free shopping, electronics, clothing, and crew essentials. Best deals and shopping locations.</p>
                        
                        <div class="flex items-center justify-between">
                            <div class="flex -space-x-2">
                                <!-- Shopping-focused crew members -->
                                <img src="https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150" alt="3rd Officer" class="w-8 h-8 rounded-full border-2 border-white object-cover">
                                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150" alt="Radio Officer" class="w-8 h-8 rounded-full border-2 border-white object-cover">
                                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150" alt="Bosun" class="w-8 h-8 rounded-full border-2 border-white object-cover">
                                <div class="w-8 h-8 bg-gray-200 rounded-full border-2 border-white flex items-center justify-center text-xs text-gray-600">+6</div>
                            </div>
                            <button class="bg-navy text-white px-4 py-2 rounded-lg hover:bg-deep-blue transition-colors text-sm" data-event="click:joinGroup">
                                Join Group
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Group Card 6: Emergency Support -->
                <div class="bg-white rounded-xl shadow-sm border border-gray-200 hover:shadow-md transition-shadow">
                    <div class="p-6">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-red-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-life-ring text-red-600 text-xl"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-gray-900" data-mock="true">Hamburg Crew Support</h3>
                                    <div class="text-sm text-gray-500">
                                        <i class="fas fa-map-marker-alt mr-1"></i>Emergency Support • Public
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center text-green-600 text-sm">
                                <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                                <span data-bind="group.activeMembers">24/7</span>
                            </div>
                        </div>
                        
                        <p class="text-gray-600 text-sm mb-4" data-mock="true">Emergency support, medical assistance, legal help, and crew welfare services. Always here to help fellow mariners.</p>
                        
                        <div class="flex items-center justify-between">
                            <div class="flex -space-x-2">
                                <!-- Support and senior crew members -->
                                <img src="https://images.unsplash.com/photo-1582750433449-648ed127bb54?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150" alt="Captain" class="w-8 h-8 rounded-full border-2 border-white object-cover">
                                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150" alt="Chief Engineer" class="w-8 h-8 rounded-full border-2 border-white object-cover">
                                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150" alt="Chief Officer" class="w-8 h-8 rounded-full border-2 border-white object-cover">
                                <div class="w-8 h-8 bg-gray-200 rounded-full border-2 border-white flex items-center justify-center text-xs text-gray-600">+20</div>
                            </div>
                            <button class="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors text-sm" data-event="click:joinGroup">
                                <i class="fas fa-heart mr-1"></i>Join Support
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- @END_MAP )) -->
            </div>
        </div>
    </section>
    <!-- @END_COMPONENT: GroupDiscovery -->

    <!-- @COMPONENT: ChatInterface (Modal-style overlay) -->
    <div id="chatModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden" data-event="click:closeChatModal">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-xl shadow-2xl w-full max-w-4xl h-[600px] flex flex-col" onclick="event.stopPropagation()">
                <!-- Chat Header -->
                <div class="flex items-center justify-between p-4 border-b border-gray-200">
                    <div class="flex items-center space-x-3">
                        <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                            <i class="fas fa-taxi text-yellow-600 text-xl"></i>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-900" data-mock="true">Hamburg Taxi Share</h3>
                            <div class="text-sm text-gray-500">
                                <span class="text-green-600">6 active members</span> • Container Terminal
                            </div>
                        </div>
                    </div>
                    <button class="text-gray-400 hover:text-gray-600" data-event="click:closeChatModal">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>

                <!-- Chat Messages Area -->
                <div class="flex-1 overflow-y-auto p-4 space-y-4">
                    <!-- @MAP: messages.map(message => ( -->
                    
                    <!-- Message 1 -->
                    <div class="flex items-start space-x-3">
                        <!-- Maritime professional discussing taxi sharing -->
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150" alt="Chief Engineer" class="w-8 h-8 rounded-full object-cover">
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-1">
                                <span class="font-medium text-gray-900" data-mock="true">Lars M. (Chief Eng.)</span>
                                <span class="text-xs text-gray-500" data-bind="message.timestamp">2:34 PM</span>
                            </div>
                            <div class="bg-gray-100 rounded-lg p-3 text-sm text-gray-800" data-mock="true">
                                Anyone heading to Hamburg city center around 6 PM? Happy to share taxi costs. Coming from Container Terminal 3.
                            </div>
                        </div>
                    </div>

                    <!-- Message 2 -->
                    <div class="flex items-start space-x-3">
                        <!-- Another maritime professional responding -->
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150" alt="2nd Engineer" class="w-8 h-8 rounded-full object-cover">
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-1">
                                <span class="font-medium text-gray-900" data-mock="true">Piyush G. (2nd Eng.)</span>
                                <span class="text-xs text-gray-500" data-bind="message.timestamp">2:36 PM</span>
                            </div>
                            <div class="bg-gray-100 rounded-lg p-3 text-sm text-gray-800" data-mock="true">
                                I'm interested! Also need to get to city center. My vessel is at Terminal 4, can meet at the taxi stand near the port entrance.
                            </div>
                        </div>
                    </div>

                    <!-- Message 3 -->
                    <div class="flex items-start space-x-3">
                        <!-- Third maritime professional joining -->
                        <img src="https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150" alt="3rd Officer" class="w-8 h-8 rounded-full object-cover">
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-1">
                                <span class="font-medium text-gray-900" data-mock="true">Miguel R. (3rd Off.)</span>
                                <span class="text-xs text-gray-500" data-bind="message.timestamp">2:38 PM</span>
                            </div>
                            <div class="bg-gray-100 rounded-lg p-3 text-sm text-gray-800" data-mock="true">
                                Count me in! 3 people = much cheaper taxi. I'll be at the main gate taxi stand by 5:50 PM.
                            </div>
                        </div>
                    </div>

                    <!-- Message 4 - User's own message -->
                    <div class="flex items-end justify-end space-x-3">
                        <div class="flex-1 max-w-xs">
                            <div class="bg-ocean-teal text-white rounded-lg p-3 text-sm" data-mock="true">
                                Perfect! I'll be there by 5:50 PM too. Should we exchange WhatsApp numbers for coordination?
                            </div>
                            <div class="text-xs text-gray-500 text-right mt-1" data-bind="message.timestamp">2:40 PM</div>
                        </div>
                    </div>
                    
                    <!-- @END_MAP )) -->
                </div>

                <!-- Chat Input -->
                <div class="p-4 border-t border-gray-200">
                    <div class="flex items-center space-x-3">
                        <div class="flex-1 relative">
                            <input type="text" 
                                   placeholder="Type your message..." 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-ocean-teal focus:border-transparent"
                                   data-implementation="Implement real-time messaging">
                            <button class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-ocean-teal">
                                <i class="fas fa-paperclip"></i>
                            </button>
                        </div>
                        <button class="bg-ocean-teal text-white px-6 py-3 rounded-lg hover:bg-light-teal transition-colors" data-event="click:sendMessage">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                    
                    <!-- WhatsApp Integration Notice -->
                    <div class="flex items-center justify-center mt-2 text-xs text-gray-500">
                        <i class="fab fa-whatsapp text-green-500 mr-1"></i>
                        Connected to WhatsApp for notifications
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- @END_COMPONENT: ChatInterface -->

    <!-- @COMPONENT: CreateGroupModal -->
    <div id="createGroupModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden" data-event="click:closeCreateGroupModal">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-xl shadow-2xl w-full max-w-2xl" onclick="event.stopPropagation()">
                <div class="p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h2 class="text-2xl font-bold text-gray-900">Create New Group</h2>
                        <button class="text-gray-400 hover:text-gray-600" data-event="click:closeCreateGroupModal">
                            <i class="fas fa-times text-xl"></i>
                        </button>
                    </div>

                    <form class="space-y-6" data-event="submit:createNewGroup">
                        <!-- Group Name -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Group Name</label>
                            <input type="text" 
                                   placeholder="e.g., Hamburg Taxi Share" 
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-ocean-teal focus:border-transparent"
                                   required>
                        </div>

                        <!-- CPSS Location -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">CPSS Location</label>
                            <div class="bg-gray-50 p-3 rounded-lg">
                                <div class="text-sm text-gray-600 mb-2">Current Location Breadcrumb:</div>
                                <div class="flex items-center space-x-2 text-sm">
                                    <span class="bg-ocean-teal text-white px-2 py-1 rounded" data-mock="true">Germany</span>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                    <span class="bg-ocean-teal text-white px-2 py-1 rounded" data-mock="true">Hamburg</span>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                    <span class="bg-ocean-teal text-white px-2 py-1 rounded" data-mock="true">Port Area</span>
                                    <i class="fas fa-chevron-right text-gray-400"></i>
                                    <select class="border border-gray-300 rounded px-2 py-1">
                                        <option>Container Terminal</option>
                                        <option>Bulk Terminal</option>
                                        <option>Recreation Area</option>
                                        <option>Food & Dining</option>
                                        <option>Shopping</option>
                                        <option>Emergency Support</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Privacy Setting -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Privacy Setting</label>
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input type="radio" name="privacy" value="public" class="text-ocean-teal" checked>
                                    <span class="ml-2 text-sm text-gray-700">Public - Anyone can join</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="privacy" value="private" class="text-ocean-teal">
                                    <span class="ml-2 text-sm text-gray-700">Private - Invitation only</span>
                                </label>
                            </div>
                        </div>

                        <!-- Description -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Description</label>
                            <textarea rows="3" 
                                      placeholder="Describe the purpose of this group and any rules..."
                                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-ocean-teal focus:border-transparent"
                                      required></textarea>
                        </div>

                        <!-- Group Rules -->
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Group Rules (Optional)</label>
                            <textarea rows="2" 
                                      placeholder="e.g., Be respectful, No spam, Share costs fairly..."
                                      class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-ocean-teal focus:border-transparent"></textarea>
                        </div>

                        <!-- Actions -->
                        <div class="flex items-center justify-end space-x-4 pt-4">
                            <button type="button" 
                                    class="px-6 py-3 text-gray-600 hover:text-gray-800 transition-colors"
                                    data-event="click:closeCreateGroupModal">
                                Cancel
                            </button>
                            <button type="submit" 
                                    class="bg-ocean-teal text-white px-6 py-3 rounded-lg hover:bg-light-teal transition-colors"
                                    data-implementation="Should validate form and create group">
                                Create Group
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!-- @END_COMPONENT: CreateGroupModal -->

    <!-- @COMPONENT: QAAQIntegration -->
    <section class="bg-white py-12 border-t border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-8">
                <h2 class="text-3xl font-bold text-gray-900">Powered by QAAQ AI</h2>
                <p class="text-gray-600 mt-2">Get instant answers to technical questions while connecting with your community</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-8 items-center">
                <div>
                    <h3 class="text-xl font-semibold text-gray-900 mb-4">Ask Technical Questions</h3>
                    <div class="space-y-4">
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <div class="text-sm text-gray-600 mb-2">Recent QAAQ Question:</div>
                            <div class="text-gray-800" data-mock="true">"My MAN B&W 6S50ME engine showing high exhaust temperature on cylinder 3..."</div>
                            <div class="mt-2 text-xs text-ocean-teal">
                                <i class="fas fa-check-circle mr-1"></i>Answered by Chief Engineer with 20+ years experience
                            </div>
                        </div>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <div class="text-sm text-gray-600 mb-2">From Hamburg Taxi Share Group:</div>
                            <div class="text-gray-800" data-mock="true">"Anyone know the best route to avoid Hamburg traffic at 6 PM?"</div>
                            <div class="mt-2 text-xs text-ocean-teal">
                                <i class="fas fa-users mr-1"></i>3 group members shared local knowledge
                            </div>
                        </div>
                    </div>
                </div>

                <div class="text-center">
                    <!-- Hamburg port scene with maritime professionals -->
                    <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600" alt="Hamburg port with containers and maritime professionals" class="rounded-xl shadow-lg w-full h-auto">
                    <div class="mt-4">
                        <a href="https://qaaqit.replit.app/" class="inline-flex items-center text-ocean-teal hover:underline" data-mock="true">
                            <span>Visit QAAQ for more technical discussions</span>
                            <i class="fas fa-external-link-alt ml-2"></i>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- @END_COMPONENT: QAAQIntegration -->

    <!-- @COMPONENT: Footer -->
    <footer class="bg-navy text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="text-2xl font-bold mb-4">
                        1234 <span class="text-teal-300">कोई है?</span>
                    </div>
                    <p class="text-blue-200 text-sm">
                        Connecting maritime professionals worldwide through location-based community groups.
                    </p>
                </div>
                
                <div>
                    <h4 class="font-semibold mb-4">Features</h4>
                    <ul class="space-y-2 text-sm text-blue-200">
                        <li><a href="#" class="hover:text-white transition-colors" data-mock="true">Port Mode</a></li>
                        <li><a href="#" class="hover:text-white transition-colors" data-mock="true">Airport Mode</a></li>
                        <li><a href="#" class="hover:text-white transition-colors" data-mock="true">QAAQ AI Integration</a></li>
                        <li><a href="#" class="hover:text-white transition-colors" data-mock="true">WhatsApp Notifications</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="font-semibold mb-4">Support</h4>
                    <ul class="space-y-2 text-sm text-blue-200">
                        <li><a href="#" class="hover:text-white transition-colors" data-mock="true">Help Center</a></li>
                        <li><a href="#" class="hover:text-white transition-colors" data-mock="true">Safety Guidelines</a></li>
                        <li><a href="#" class="hover:text-white transition-colors" data-mock="true">Community Rules</a></li>
                        <li><a href="#" class="hover:text-white transition-colors" data-mock="true">Report Issues</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="font-semibold mb-4">Connect</h4>
                    <div class="space-y-3">
                        <div class="flex items-center space-x-2 text-sm text-blue-200">
                            <i class="fab fa-whatsapp"></i>
                            <span>WhatsApp: +************</span>
                        </div>
                        <div class="text-sm text-blue-200">
                            Sister app to <a href="https://qaaqit.replit.app/" class="text-teal-300 hover:underline" data-mock="true">QAAQ</a>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="border-t border-blue-800 mt-8 pt-8 text-center text-sm text-blue-200">
                <p>&copy; 2024 1234 Koi Hai? - Maritime Community Platform. All rights reserved.</p>
            </div>
        </div>
    </footer>
    <!-- @END_COMPONENT: Footer -->

    <!-- @STATE: isPortMode:boolean = true -->
    <!-- @STATE: isAirportMode:boolean = false -->
    <!-- @STATE: showChatModal:boolean = false -->
    <!-- @STATE: showCreateGroupModal:boolean = false -->
    <!-- @STATE: currentLocation:string = "Hamburg Port, Germany" -->
    <!-- @STATE: activeGroups:array = [] -->
    <!-- @STATE: nearbyOfficers:number = 24 -->

    <script>
        // TODO: Implement business logic, API calls, or state management
        (function() {
            // Modal control functionality
            const chatModal = document.getElementById('chatModal');
            const createGroupModal = document.getElementById('createGroupModal');
            
            // Group card click handlers - open chat modal
            const groupCards = document.querySelectorAll('[data-event*="joinGroup"]');
            groupCards.forEach(card => {
                card.addEventListener('click', () => {
                    chatModal.classList.remove('hidden');
                });
            });
            
            // Create group button handler
            const createGroupBtn = document.querySelector('[data-event*="createGroup"]');
            if (createGroupBtn) {
                createGroupBtn.addEventListener('click', () => {
                    createGroupModal.classList.remove('hidden');
                });
            }
            
            // Modal close handlers
            chatModal.addEventListener('click', (e) => {
                if (e.target === chatModal) {
                    chatModal.classList.add('hidden');
                }
            });
            
            createGroupModal.addEventListener('click', (e) => {
                if (e.target === createGroupModal) {
                    createGroupModal.classList.add('hidden');
                }
            });
            
            // Close button handlers
            document.querySelectorAll('[data-event*="closeModal"], [data-event*="closeChatModal"], [data-event*="closeCreateGroupModal"]').forEach(btn => {
                btn.addEventListener('click', () => {
                    chatModal.classList.add('hidden');
                    createGroupModal.classList.add('hidden');
                });
            });
            
            // Mode toggle functionality
            const portModeBtn = document.querySelector('[data-event*="togglePortMode"]');
            const airportModeBtn = document.querySelector('[data-event*="toggleAirportMode"]');
            
            if (portModeBtn && airportModeBtn) {
                portModeBtn.addEventListener('click', () => {
                    portModeBtn.classList.add('bg-navy', 'text-white');
                    portModeBtn.classList.remove('text-gray-600');
                    airportModeBtn.classList.remove('bg-navy', 'text-white');
                    airportModeBtn.classList.add('text-gray-600');
                });
                
                airportModeBtn.addEventListener('click', () => {
                    airportModeBtn.classList.add('bg-navy', 'text-white');
                    airportModeBtn.classList.remove('text-gray-600');
                    portModeBtn.classList.remove('bg-navy', 'text-white');
                    portModeBtn.classList.add('text-gray-600');
                });
            }
        })();
    </script>
</body>
</html>