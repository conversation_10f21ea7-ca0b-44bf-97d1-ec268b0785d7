<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QaaqConnect - Maritime Community & Mart</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'navy': '#1B365D',
                        'ocean': '#2DD4BF',
                        'ocean-light': '#7DD3FC',
                        'maritime-gray': '#64748B'
                    },
                    fontFamily: {
                        'sans': ['Inter', 'sans-serif']
                    }
                }
            }
        };
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-50 font-sans">
    <!-- @COMPONENT: MobileHeader -->
    <header class="bg-navy text-white sticky top-0 z-50 shadow-lg">
        <div class="px-4 py-3">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <i class="fas fa-anchor text-ocean text-xl"></i>
                    <h1 class="text-xl font-bold">QaaqConnect</h1>
                </div>
                <div class="flex items-center space-x-4">
                    <!-- Shopping cart with badge -->
                    <div class="relative">
                        <i class="fas fa-shopping-cart text-lg cursor-pointer hover:text-ocean transition-colors"></i>
                        <span class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center" data-bind="cartItemCount">3</span>
                    </div>
                    <!-- Notifications -->
                    <div class="relative">
                        <i class="fas fa-bell text-lg cursor-pointer hover:text-ocean transition-colors"></i>
                        <span class="absolute -top-1 -right-1 bg-ocean h-3 w-3 rounded-full"></span>
                    </div>
                    <!-- Profile -->
                    <div class="w-8 h-8 bg-ocean rounded-full flex items-center justify-center">
                        <span class="text-sm font-semibold text-navy" data-bind="user.initials">PG</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- @COMPONENT: NavigationTabs -->
        <nav class="border-t border-navy-light">
            <div class="flex">
                <button class="flex-1 py-3 text-center text-sm font-medium border-b-2 border-ocean text-ocean" data-active="true">
                    <i class="fas fa-compass block mb-1"></i>
                    Discover
                </button>
                <button class="flex-1 py-3 text-center text-sm font-medium border-b-2 border-transparent text-gray-300 hover:text-ocean transition-colors">
                    <i class="fas fa-store block mb-1"></i>
                    QAAQ Mart
                </button>
                <button class="flex-1 py-3 text-center text-sm font-medium border-b-2 border-transparent text-gray-300 hover:text-ocean transition-colors">
                    <i class="fas fa-users block mb-1"></i>
                    Community
                </button>
                <button class="flex-1 py-3 text-center text-sm font-medium border-b-2 border-transparent text-gray-300 hover:text-ocean transition-colors">
                    <i class="fas fa-truck block mb-1"></i>
                    Orders
                </button>
            </div>
        </nav>
    </header>
    <!-- @END_COMPONENT: MobileHeader -->

    <!-- @COMPONENT: QuickSearch -->
    <section class="bg-white border-b border-gray-200">
        <div class="px-4 py-4">
            <div class="relative">
                <input type="text" placeholder="1234 koi hai? Search sailors, ports, items..." 
                       class="w-full bg-gray-50 border border-gray-200 rounded-xl px-4 py-3 pl-10 text-sm focus:outline-none focus:ring-2 focus:ring-ocean focus:border-transparent">
                <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
            </div>
        </div>
    </section>
    <!-- @END_COMPONENT: QuickSearch -->

    <!-- @COMPONENT: DeliveryStatus -->
    <section class="bg-gradient-to-r from-ocean to-ocean-light text-white">
        <div class="px-4 py-4">
            <div class="flex items-center justify-between">
                <div>
                    <h3 class="font-semibold">Next Delivery</h3>
                    <p class="text-sm opacity-90">Singapore Port - Tomorrow 14:00</p>
                </div>
                <div class="text-right">
                    <div class="bg-white bg-opacity-20 rounded-lg px-3 py-1">
                        <i class="fas fa-clock text-sm"></i>
                        <span class="text-sm font-medium ml-1">22h 15m</span>
                    </div>
                </div>
            </div>
            <div class="mt-3 bg-white bg-opacity-20 rounded-lg p-3">
                <div class="flex items-center justify-between text-sm">
                    <span>Order #QM-2024-001</span>
                    <span class="bg-green-500 px-2 py-1 rounded-full text-xs">Ready</span>
                </div>
                <p class="text-xs mt-1 opacity-90">3 items • SIM Card, Snacks, Electronics</p>
            </div>
        </div>
    </section>
    <!-- @END_COMPONENT: DeliveryStatus -->

    <!-- @COMPONENT: MainContent -->
    <main class="pb-20">
        <!-- @COMPONENT: QuickActions -->
        <section class="bg-white">
            <div class="px-4 py-4">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Quick Actions</h2>
                <div class="grid grid-cols-2 gap-3">
                    <button class="bg-navy text-white rounded-xl p-4 text-left hover:bg-opacity-90 transition-all">
                        <i class="fas fa-shopping-bag text-ocean mb-2 text-xl"></i>
                        <div class="text-sm font-medium">Order for Next Port</div>
                        <div class="text-xs opacity-75">Pre-order essentials</div>
                    </button>
                    <button class="bg-gradient-to-br from-ocean to-ocean-light text-white rounded-xl p-4 text-left hover:shadow-lg transition-all">
                        <i class="fas fa-users mb-2 text-xl"></i>
                        <div class="text-sm font-medium">Find Crew Nearby</div>
                        <div class="text-xs opacity-75">Connect with sailors</div>
                    </button>
                    <button class="border-2 border-ocean text-ocean rounded-xl p-4 text-left hover:bg-ocean hover:text-white transition-all">
                        <i class="fas fa-map-marked-alt mb-2 text-xl"></i>
                        <div class="text-sm font-medium">Port Services</div>
                        <div class="text-xs opacity-75">Local delivery help</div>
                    </button>
                    <button class="border border-gray-200 text-gray-700 rounded-xl p-4 text-left hover:border-ocean hover:text-ocean transition-all">
                        <i class="fas fa-comments mb-2 text-xl"></i>
                        <div class="text-sm font-medium">CPSS Groups</div>
                        <div class="text-xs opacity-75">Join discussions</div>
                    </button>
                </div>
            </div>
        </section>
        <!-- @END_COMPONENT: QuickActions -->

        <!-- @COMPONENT: FeaturedItems -->
        <section class="bg-white mt-2">
            <div class="px-4 py-4">
                <div class="flex items-center justify-between mb-4">
                    <h2 class="text-lg font-semibold text-gray-900">Featured in QAAQ Mart</h2>
                    <button class="text-ocean text-sm font-medium">View All</button>
                </div>
                <div class="flex space-x-3 overflow-x-auto pb-2">
                    <!-- @MAP: featuredItems.map(item => ( -->
                    <div class="min-w-[140px] bg-gray-50 rounded-xl p-3">
                        <!-- A collection of maritime communication devices including SIM cards and satellite phones -->
                        <img src="https://images.unsplash.com/photo-1556742393-d75f468bfcb0?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&h=120" alt="SIM Cards & Data Plans" class="w-full h-20 object-cover rounded-lg mb-2">
                        <h3 class="text-sm font-medium text-gray-900 mb-1">SIM Cards</h3>
                        <p class="text-xs text-gray-600 mb-2">From $15</p>
                        <button class="bg-ocean text-white text-xs px-3 py-1.5 rounded-lg w-full">Quick Add</button>
                    </div>
                    <div class="min-w-[140px] bg-gray-50 rounded-xl p-3">
                        <!-- A variety of personal care items and toiletries arranged for travel -->
                        <img src="https://images.unsplash.com/photo-1571781926291-c477ebfd024b?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&h=120" alt="Personal Care Items" class="w-full h-20 object-cover rounded-lg mb-2">
                        <h3 class="text-sm font-medium text-gray-900 mb-1">Personal Care</h3>
                        <p class="text-xs text-gray-600 mb-2">From $8</p>
                        <button class="bg-ocean text-white text-xs px-3 py-1.5 rounded-lg w-full">Quick Add</button>
                    </div>
                    <div class="min-w-[140px] bg-gray-50 rounded-xl p-3">
                        <!-- An assortment of international snacks and beverages in packaging -->
                        <img src="https://pixabay.com/get/g50a98e484cc7d15a61974df5829ae01b84876dc59a166f57cf2b5ae4f75f72a7e1abf30e314b8789c4ae9c4f038c1b211f9f96ccf210854c92c3934543cf6b67_1280.jpg" alt="Snacks & Beverages" class="w-full h-20 object-cover rounded-lg mb-2">
                        <h3 class="text-sm font-medium text-gray-900 mb-1">Snacks & Drinks</h3>
                        <p class="text-xs text-gray-600 mb-2">From $5</p>
                        <button class="bg-ocean text-white text-xs px-3 py-1.5 rounded-lg w-full">Quick Add</button>
                    </div>
                    <!-- @END_MAP )) -->
                </div>
            </div>
        </section>
        <!-- @END_COMPONENT: FeaturedItems -->

        <!-- @COMPONENT: CommunityFeed -->
        <section class="bg-white mt-2">
            <div class="px-4 py-4">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Community Activity</h2>
                
                <!-- @MAP: communityPosts.map(post => ( -->
                <div class="border border-gray-100 rounded-xl p-4 mb-4">
                    <div class="flex items-start space-x-3">
                        <div class="w-10 h-10 bg-navy rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-semibold" data-bind="post.user.initials">RK</span>
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-2">
                                <span class="font-medium text-gray-900" data-bind="post.user.name">Rajesh Kumar</span>
                                <span class="text-xs text-gray-500">Chief Engineer • 2nd Engineer</span>
                                <span class="text-xs text-gray-400">2h ago</span>
                            </div>
                            <p class="text-gray-700 text-sm mb-3" data-bind="post.content">Looking for crew in Hamburg port tomorrow. Need help with shore power connection setup. Anyone available around 15:00?</p>
                            <!-- A group of maritime crew members working together on ship equipment at a busy port -->
                            <img src="https://pixabay.com/get/gf925d0e1c78cd34c9d92bc736c25708e0b8f58b04a5fd6a98e8b770ef7a507ce3f7a24b982eb84234864bcf4fe1b7ba107941273d8ef8a9c2c3a24416bfc1a64_1280.jpg" alt="Maritime crew interactions" class="w-full h-32 object-cover rounded-lg mb-3">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-4">
                                    <button class="flex items-center space-x-1 text-gray-500 hover:text-ocean transition-colors">
                                        <span class="text-xl">🦆</span>
                                        <span class="text-xs" data-bind="post.duckLikes">12</span>
                                    </button>
                                    <button class="flex items-center space-x-1 text-gray-500 hover:text-ocean transition-colors">
                                        <i class="fas fa-comment text-sm"></i>
                                        <span class="text-xs" data-bind="post.comments">5</span>
                                    </button>
                                    <button class="flex items-center space-x-1 text-gray-500 hover:text-ocean transition-colors">
                                        <i class="fas fa-share text-sm"></i>
                                        <span class="text-xs">Share</span>
                                    </button>
                                </div>
                                <span class="text-xs text-ocean font-medium">Hamburg Port</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="border border-gray-100 rounded-xl p-4 mb-4">
                    <div class="flex items-start space-x-3">
                        <div class="w-10 h-10 bg-ocean rounded-full flex items-center justify-center">
                            <span class="text-white text-sm font-semibold" data-bind="post.user.initials">AM</span>
                        </div>
                        <div class="flex-1">
                            <div class="flex items-center space-x-2 mb-2">
                                <span class="font-medium text-gray-900" data-bind="post.user.name">Ahmed Malik</span>
                                <span class="text-xs text-gray-500">2nd Engineer</span>
                                <span class="text-xs text-gray-400">4h ago</span>
                            </div>
                            <p class="text-gray-700 text-sm mb-3" data-bind="post.content">Successfully delivered orders to 3 ships today in Singapore! QAAQ Mart service is really making shore leave easier for everyone 🚢</p>
                            <!-- Professional delivery person with packages walking toward a large container ship at port -->
                            <img src="https://pixabay.com/get/g80a2c81c97ec1564e9fbb09d40a98e83f7303234247ba15f87c7ad49d3ebc3c65db68adb8e9500b67a49d5cd494ab57195cc180407f1b4dbc97b31934c9e9e0d_1280.jpg" alt="Port delivery services" class="w-full h-32 object-cover rounded-lg mb-3">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-4">
                                    <button class="flex items-center space-x-1 text-ocean">
                                        <span class="text-xl">🦆</span>
                                        <span class="text-xs" data-bind="post.duckLikes">28</span>
                                    </button>
                                    <button class="flex items-center space-x-1 text-gray-500 hover:text-ocean transition-colors">
                                        <i class="fas fa-comment text-sm"></i>
                                        <span class="text-xs" data-bind="post.comments">8</span>
                                    </button>
                                    <button class="flex items-center space-x-1 text-gray-500 hover:text-ocean transition-colors">
                                        <i class="fas fa-share text-sm"></i>
                                        <span class="text-xs">Share</span>
                                    </button>
                                </div>
                                <span class="text-xs text-ocean font-medium">Singapore Port</span>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- @END_MAP )) -->
            </div>
        </section>
        <!-- @END_COMPONENT: CommunityFeed -->

        <!-- @COMPONENT: PortSelection -->
        <section class="bg-white mt-2">
            <div class="px-4 py-4">
                <h2 class="text-lg font-semibold text-gray-900 mb-4">Select CPS Launch Port</h2>
                <div class="space-y-3">
                    <!-- @MAP: ports.map(port => ( -->
                    <div class="border border-gray-200 rounded-xl p-4 hover:border-ocean transition-colors cursor-pointer" data-port="singapore">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-anchor text-ocean text-lg"></i>
                                </div>
                                <div>
                                    <h3 class="font-medium text-gray-900">Singapore Port</h3>
                                    <p class="text-sm text-gray-600">Available • Next slot: Tomorrow 14:00</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full">Active</span>
                                <p class="text-xs text-gray-500 mt-1">12 orders today</p>
                            </div>
                        </div>
                    </div>

                    <div class="border border-gray-200 rounded-xl p-4 hover:border-ocean transition-colors cursor-pointer" data-port="rotterdam">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-anchor text-ocean text-lg"></i>
                                </div>
                                <div>
                                    <h3 class="font-medium text-gray-900">Rotterdam</h3>
                                    <p class="text-sm text-gray-600">Available • Next slot: Jan 28, 16:30</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">Available</span>
                                <p class="text-xs text-gray-500 mt-1">8 orders today</p>
                            </div>
                        </div>
                    </div>

                    <div class="border border-gray-200 rounded-xl p-4 hover:border-ocean transition-colors cursor-pointer" data-port="hamburg">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-anchor text-ocean text-lg"></i>
                                </div>
                                <div>
                                    <h3 class="font-medium text-gray-900">Hamburg</h3>
                                    <p class="text-sm text-gray-600">Available • Next slot: Jan 29, 11:00</p>
                                </div>
                            </div>
                            <div class="text-right">
                                <span class="bg-blue-100 text-blue-800 text-xs px-2 py-1 rounded-full">Available</span>
                                <p class="text-xs text-gray-500 mt-1">6 orders today</p>
                            </div>
                        </div>
                    </div>

                    <div class="border border-gray-200 rounded-xl p-4 opacity-60" data-port="coming-soon">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                                    <i class="fas fa-anchor text-gray-400 text-lg"></i>
                                </div>
                                <div>
                                    <h3 class="font-medium text-gray-500">More Ports</h3>
                                    <p class="text-sm text-gray-400">Coming Soon</p>
                                </div>
                            </div>
                            <span class="bg-gray-100 text-gray-500 text-xs px-2 py-1 rounded-full">Soon</span>
                        </div>
                    </div>
                    <!-- @END_MAP )) -->
                </div>
            </div>
        </section>
        <!-- @END_COMPONENT: PortSelection -->

        <!-- @COMPONENT: QAQIntegration -->
        <section class="bg-gradient-to-r from-navy to-blue-900 text-white mt-2">
            <div class="px-4 py-6">
                <div class="text-center">
                    <i class="fas fa-link text-ocean text-3xl mb-3"></i>
                    <h2 class="text-xl font-bold mb-2">Powered by QAAQ</h2>
                    <p class="text-sm opacity-90 mb-4">Connect with the full QAAQ ecosystem for marine engineering support</p>
                    <button class="bg-ocean text-navy font-semibold px-6 py-3 rounded-xl hover:bg-ocean-light transition-colors">
                        Visit QAAQ Main Platform
                    </button>
                    <div class="mt-4 text-xs opacity-75">
                        <p>AI-powered Q&A • Technical Documentation • Expert Community</p>
                    </div>
                </div>
            </div>
        </section>
        <!-- @END_COMPONENT: QAQIntegration -->
    </main>
    <!-- @END_COMPONENT: MainContent -->

    <!-- @COMPONENT: FloatingActionButton -->
    <div class="fixed bottom-24 right-4 z-40">
        <button class="bg-ocean text-white w-14 h-14 rounded-full shadow-lg hover:shadow-xl transition-all duration-300 hover:scale-110">
            <i class="fas fa-plus text-xl"></i>
        </button>
    </div>
    <!-- @END_COMPONENT: FloatingActionButton -->

    <!-- @COMPONENT: BottomNavigation -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-50">
        <div class="flex">
            <button class="flex-1 py-3 text-center">
                <i class="fas fa-compass text-ocean text-lg block mb-1"></i>
                <span class="text-xs text-ocean font-medium">Discover</span>
            </button>
            <button class="flex-1 py-3 text-center">
                <i class="fas fa-store text-gray-400 text-lg block mb-1"></i>
                <span class="text-xs text-gray-500">Mart</span>
            </button>
            <button class="flex-1 py-3 text-center">
                <i class="fas fa-users text-gray-400 text-lg block mb-1"></i>
                <span class="text-xs text-gray-500">Community</span>
            </button>
            <button class="flex-1 py-3 text-center">
                <i class="fas fa-truck text-gray-400 text-lg block mb-1"></i>
                <span class="text-xs text-gray-500">Orders</span>
            </button>
            <button class="flex-1 py-3 text-center">
                <i class="fas fa-user text-gray-400 text-lg block mb-1"></i>
                <span class="text-xs text-gray-500">Profile</span>
            </button>
        </div>
    </nav>
    <!-- @END_COMPONENT: BottomNavigation -->

    <!-- @COMPONENT: QAAQMartModal -->
    <div id="martModal" class="fixed inset-0 bg-black bg-opacity-50 z-50 hidden">
        <div class="fixed bottom-0 left-0 right-0 bg-white rounded-t-3xl max-h-[90vh] overflow-hidden">
            <div class="p-4 border-b border-gray-200">
                <div class="flex items-center justify-between">
                    <h2 class="text-xl font-bold text-gray-900">QAAQ Mart</h2>
                    <button class="text-gray-400 hover:text-gray-600" onclick="closeModal('martModal')">
                        <i class="fas fa-times text-xl"></i>
                    </button>
                </div>
            </div>
            <div class="p-4 overflow-y-auto max-h-[calc(90vh-80px)]">
                <div class="grid grid-cols-2 gap-4">
                    <!-- @MAP: martCategories.map(category => ( -->
                    <div class="border border-gray-200 rounded-xl p-4 text-center hover:border-ocean transition-colors">
                        <!-- Various SIM cards and mobile communication devices for sailors -->
                        <img src="https://images.unsplash.com/photo-1511707171634-5f897ff02aa9?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&h=120" alt="SIM Cards & Data Plans" class="w-full h-20 object-cover rounded-lg mb-3">
                        <h3 class="font-medium text-gray-900 mb-1">SIM Cards</h3>
                        <p class="text-xs text-gray-600 mb-2">Global connectivity</p>
                        <span class="text-xs bg-ocean text-white px-2 py-1 rounded-full">12 items</span>
                    </div>
                    <div class="border border-gray-200 rounded-xl p-4 text-center hover:border-ocean transition-colors">
                        <!-- Personal hygiene and care products arranged neatly for maritime use -->
                        <img src="https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&h=120" alt="Personal Care Items" class="w-full h-20 object-cover rounded-lg mb-3">
                        <h3 class="font-medium text-gray-900 mb-1">Personal Care</h3>
                        <p class="text-xs text-gray-600 mb-2">Hygiene essentials</p>
                        <span class="text-xs bg-ocean text-white px-2 py-1 rounded-full">18 items</span>
                    </div>
                    <div class="border border-gray-200 rounded-xl p-4 text-center hover:border-ocean transition-colors">
                        <!-- Various international snacks and energy drinks suitable for ship crews -->
                        <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&h=120" alt="Snacks & Beverages" class="w-full h-20 object-cover rounded-lg mb-3">
                        <h3 class="font-medium text-gray-900 mb-1">Snacks & Drinks</h3>
                        <p class="text-xs text-gray-600 mb-2">Energy & comfort</p>
                        <span class="text-xs bg-ocean text-white px-2 py-1 rounded-full">25 items</span>
                    </div>
                    <div class="border border-gray-200 rounded-xl p-4 text-center hover:border-ocean transition-colors">
                        <!-- Electronic accessories and gadgets useful for maritime professionals -->
                        <img src="https://images.unsplash.com/photo-1498049794561-7780e7231661?ixlib=rb-4.0.3&auto=format&fit=crop&w=200&h=120" alt="Electronics Accessories" class="w-full h-20 object-cover rounded-lg mb-3">
                        <h3 class="font-medium text-gray-900 mb-1">Electronics</h3>
                        <p class="text-xs text-gray-600 mb-2">Cables, chargers</p>
                        <span class="text-xs bg-ocean text-white px-2 py-1 rounded-full">15 items</span>
                    </div>
                    <!-- @END_MAP )) -->
                </div>
                
                <div class="mt-6 p-4 bg-ocean bg-opacity-10 rounded-xl">
                    <h3 class="font-semibold text-gray-900 mb-2">How QAAQ Mart Works</h3>
                    <div class="space-y-2 text-sm text-gray-600">
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-check-circle text-ocean"></i>
                            <span>Browse and add items to cart</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-check-circle text-ocean"></i>
                            <span>Select ship arrival time at port</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-check-circle text-ocean"></i>
                            <span>Choose convenient pickup point</span>
                        </div>
                        <div class="flex items-center space-x-2">
                            <i class="fas fa-check-circle text-ocean"></i>
                            <span>QAAQ team delivers to ship</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- @END_COMPONENT: QAAQMartModal -->

    <script>
        // TODO: Implement modal functionality, cart management, and navigation
        (function() {
            // Modal functions
            window.closeModal = function(modalId) {
                document.getElementById(modalId).classList.add('hidden');
            };

            // Navigation tab switching
            const navButtons = document.querySelectorAll('nav button');
            navButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // Remove active state from all buttons
                    navButtons.forEach(btn => {
                        btn.classList.remove('border-ocean', 'text-ocean');
                        btn.classList.add('border-transparent', 'text-gray-300');
                    });
                    
                    // Add active state to clicked button
                    this.classList.remove('border-transparent', 'text-gray-300');
                    this.classList.add('border-ocean', 'text-ocean');
                    
                    // Show QAAQ Mart modal if Mart tab is clicked
                    if (this.textContent.includes('QAAQ Mart')) {
                        document.getElementById('martModal').classList.remove('hidden');
                    }
                });
            });

            // Duck like animation
            const duckButtons = document.querySelectorAll('button:has(span:contains("🦆"))');
            duckButtons.forEach(button => {
                button.addEventListener('click', function() {
                    this.classList.add('animate-bounce');
                    setTimeout(() => {
                        this.classList.remove('animate-bounce');
                    }, 1000);
                });
            });

            // Shopping cart badge update
            const cartBadge = document.querySelector('[data-bind="cartItemCount"]');
            if (cartBadge) {
                // TODO: Update cart count from actual cart state
                cartBadge.textContent = '3';
            }

            // Port selection handling
            const portCards = document.querySelectorAll('[data-port]');
            portCards.forEach(card => {
                card.addEventListener('click', function() {
                    if (this.dataset.port !== 'coming-soon') {
                        // TODO: Handle port selection and navigation
                        console.log('Selected port:', this.dataset.port);
                    }
                });
            });

            // Quick action buttons
            const quickActionButtons = document.querySelectorAll('section button');
            quickActionButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // TODO: Handle quick action navigation
                    console.log('Quick action clicked:', this.textContent);
                });
            });
        })();
    </script>
</body>
</html>