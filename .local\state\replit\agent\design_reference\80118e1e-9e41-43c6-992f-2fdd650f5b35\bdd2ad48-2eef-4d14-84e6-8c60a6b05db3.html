<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MaritimeConnect - Professional Maritime Networking</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'maritime-navy': '#1e3a8a',
                        'maritime-blue': '#0ea5e9',
                        'maritime-gold': '#f59e0b',
                        'ocean-blue': '#0284c7'
                    },
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif']
                    }
                }
            }
        };
    </script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="font-inter bg-slate-50 min-h-screen">
    <!-- Mobile App Container -->
    <div class="max-w-md mx-auto bg-white shadow-2xl min-h-screen relative overflow-hidden">
        
        <!-- @COMPONENT: WelcomeScreen -->
        <div id="welcome-screen" class="welcome-screen h-screen bg-gradient-to-br from-maritime-navy to-ocean-blue flex flex-col justify-center items-center p-8 text-white">
            <!-- Maritime Background Pattern -->
            <div class="absolute inset-0 opacity-10">
                <!-- Abstract maritime pattern with anchor and wave elements -->
                <div class="absolute top-10 left-10 text-6xl"><i class="fas fa-anchor"></i></div>
                <div class="absolute top-32 right-8 text-4xl"><i class="fas fa-ship"></i></div>
                <div class="absolute bottom-40 left-6 text-5xl"><i class="fas fa-compass"></i></div>
                <div class="absolute bottom-20 right-12 text-3xl"><i class="fas fa-life-ring"></i></div>
            </div>
            
            <div class="z-10 text-center">
                <div class="mb-8">
                    <i class="fas fa-anchor text-6xl text-maritime-gold mb-4"></i>
                    <h1 class="text-3xl font-bold mb-2">MaritimeConnect</h1>
                    <p class="text-lg opacity-90">Connect with Maritime Professionals Worldwide</p>
                </div>
                
                <div class="mb-8 space-y-4 text-sm opacity-80">
                    <div class="flex items-center justify-center space-x-2">
                        <i class="fas fa-map-marker-alt text-maritime-gold"></i>
                        <span>Find nearby maritime professionals</span>
                    </div>
                    <div class="flex items-center justify-center space-x-2">
                        <i class="fas fa-comments text-maritime-gold"></i>
                        <span>Chat with industry experts</span>
                    </div>
                    <div class="flex items-center justify-center space-x-2">
                        <i class="fas fa-shield-alt text-maritime-gold"></i>
                        <span>Verified maritime credentials</span>
                    </div>
                </div>
                
                <div class="space-y-4">
                    <button onclick="showLogin()" class="w-full bg-white text-maritime-navy py-3 px-6 rounded-xl font-semibold hover:bg-gray-100 transition duration-300">
                        Login to Continue
                    </button>
                    <button onclick="showRegister()" class="w-full border-2 border-white text-white py-3 px-6 rounded-xl font-semibold hover:bg-white hover:text-maritime-navy transition duration-300">
                        Join MaritimeConnect
                    </button>
                </div>
                
                <div class="mt-8 text-xs opacity-70">
                    <p>Sister app to <span class="text-maritime-gold font-semibold">QAAQ</span> - Maritime Engineering Knowledge Hub</p>
                </div>
            </div>
        </div>
        <!-- @END_COMPONENT: WelcomeScreen -->

        <!-- @COMPONENT: LoginScreen -->
        <div id="login-screen" class="screen hidden h-screen bg-white">
            <div class="p-6">
                <!-- Header -->
                <div class="flex items-center justify-between mb-8 pt-4">
                    <button onclick="showWelcome()" class="text-maritime-navy">
                        <i class="fas fa-arrow-left text-xl"></i>
                    </button>
                    <h2 class="text-xl font-semibold text-maritime-navy">Login</h2>
                    <div></div>
                </div>
                
                <!-- QAAQ Integration Notice -->
                <div class="bg-maritime-blue/10 border-l-4 border-maritime-blue p-4 mb-6 rounded-r-lg">
                    <div class="flex items-center space-x-2">
                        <i class="fas fa-info-circle text-maritime-blue"></i>
                        <span class="text-sm text-maritime-navy font-medium">Connected to QAAQ Database</span>
                    </div>
                    <p class="text-xs text-gray-600 mt-1">Use your QAAQ credentials or create a new account</p>
                </div>
                
                <!-- Login Form -->
                <form class="space-y-6" data-implementation="Handle authentication with QAAQ integration">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                        <div class="relative">
                            <span class="absolute left-3 top-3 text-gray-400">
                                <i class="fas fa-phone"></i>
                            </span>
                            <input type="tel" placeholder="Your WhatsApp number" 
                                   class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-maritime-blue focus:border-transparent">
                        </div>
                        <p class="text-xs text-gray-500 mt-1">Use your WhatsApp or WeChat number</p>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-2">Password</label>
                        <div class="relative">
                            <span class="absolute left-3 top-3 text-gray-400">
                                <i class="fas fa-lock"></i>
                            </span>
                            <input type="password" placeholder="Password" 
                                   class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-xl focus:ring-2 focus:ring-maritime-blue focus:border-transparent">
                        </div>
                        <p class="text-xs text-gray-500 mt-1">New users: Use your current city name as password</p>
                    </div>
                    
                    <button type="submit" onclick="showMainApp()" 
                            class="w-full bg-maritime-navy text-white py-3 px-6 rounded-xl font-semibold hover:bg-maritime-blue transition duration-300">
                        Login / Sign Up
                    </button>
                </form>
                
                <div class="mt-8 text-center">
                    <p class="text-sm text-gray-600 mb-4">Or continue with</p>
                    <div class="space-y-3">
                        <button class="w-full flex items-center justify-center space-x-3 border border-gray-300 py-3 px-6 rounded-xl hover:bg-gray-50 transition duration-300">
                            <i class="fab fa-google text-red-500"></i>
                            <span class="text-gray-700">Continue with Google</span>
                        </button>
                        <button class="w-full flex items-center justify-center space-x-3 bg-gray-900 text-white py-3 px-6 rounded-xl hover:bg-gray-800 transition duration-300">
                            <span class="font-bold">R</span>
                            <span>Continue with Replit</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <!-- @END_COMPONENT: LoginScreen -->

        <!-- @COMPONENT: MainApp -->
        <div id="main-app" class="screen hidden h-screen bg-slate-50">
            <!-- @COMPONENT: TopNavigation -->
            <div class="bg-white shadow-sm border-b p-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <!-- User Profile Preview -->
                        <div class="w-10 h-10 bg-maritime-navy rounded-full flex items-center justify-center">
                            <span class="text-white font-semibold text-sm" data-bind="user.initials">JD</span>
                        </div>
                        <div>
                            <h3 class="font-semibold text-gray-900" data-bind="user.name">John Doe</h3>
                            <p class="text-xs text-gray-500" data-bind="user.rank">Chief Engineer</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4">
                        <button onclick="toggleLocationSharing()" class="text-maritime-blue">
                            <i class="fas fa-map-marker-alt text-lg"></i>
                        </button>
                        <button onclick="showSettings()" class="text-gray-600">
                            <i class="fas fa-cog text-lg"></i>
                        </button>
                    </div>
                </div>
            </div>
            <!-- @END_COMPONENT: TopNavigation -->

            <!-- @COMPONENT: LocationControls -->
            <div class="bg-white border-b p-4">
                <div class="flex items-center justify-between mb-3">
                    <h4 class="font-medium text-gray-900">Find Maritime Professionals</h4>
                    <span class="text-sm text-maritime-blue font-medium" data-bind="nearbyCount">12 nearby</span>
                </div>
                
                <!-- Distance Slider -->
                <div class="space-y-2">
                    <div class="flex items-center justify-between text-sm text-gray-600">
                        <span>Search Radius</span>
                        <span id="radius-value" class="font-medium">10 km</span>
                    </div>
                    <input type="range" min="1" max="50" value="10" 
                           class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider"
                           oninput="updateRadius(this.value)">
                    <div class="flex justify-between text-xs text-gray-400">
                        <span>1km</span>
                        <span>50km</span>
                    </div>
                </div>
            </div>
            <!-- @END_COMPONENT: LocationControls -->

            <!-- @COMPONENT: TabNavigation -->
            <div class="bg-white border-b">
                <div class="flex">
                    <button onclick="showTab('list')" id="tab-list" 
                            class="tab-button flex-1 py-3 px-4 text-center font-medium text-maritime-blue border-b-2 border-maritime-blue">
                        <i class="fas fa-list mr-2"></i>List
                    </button>
                    <button onclick="showTab('map')" id="tab-map" 
                            class="tab-button flex-1 py-3 px-4 text-center font-medium text-gray-500 border-b-2 border-transparent">
                        <i class="fas fa-map mr-2"></i>Map
                    </button>
                    <button onclick="showTab('chat')" id="tab-chat" 
                            class="tab-button flex-1 py-3 px-4 text-center font-medium text-gray-500 border-b-2 border-transparent relative">
                        <i class="fas fa-comments mr-2"></i>Chats
                        <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">3</span>
                    </button>
                </div>
            </div>
            <!-- @END_COMPONENT: TabNavigation -->

            <!-- @COMPONENT: ContentArea -->
            <div class="flex-1 overflow-hidden">
                
                <!-- List View -->
                <div id="content-list" class="tab-content h-full overflow-y-auto">
                    <div class="p-4 space-y-4">
                        <!-- @MAP: nearbyMaritimeProfessionals.map(professional => ( -->
                        
                        <!-- Maritime Professional Card 1 -->
                        <div class="bg-white rounded-xl shadow-sm border p-4 hover:shadow-md transition duration-300" data-mock="true">
                            <div class="flex items-start space-x-3">
                                <div class="w-12 h-12 bg-maritime-navy rounded-full flex items-center justify-center">
                                    <span class="text-white font-semibold">CE</span>
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center justify-between">
                                        <h5 class="font-semibold text-gray-900">Chief Engineer Smith</h5>
                                        <span class="text-xs text-gray-500">2.3 km</span>
                                    </div>
                                    <p class="text-sm text-gray-600">Container Vessel • 15+ years exp</p>
                                    <div class="flex items-center space-x-2 mt-2">
                                        <span class="bg-maritime-blue/10 text-maritime-blue text-xs px-2 py-1 rounded-full">MAN B&W</span>
                                        <span class="bg-maritime-blue/10 text-maritime-blue text-xs px-2 py-1 rounded-full">Wartsila</span>
                                    </div>
                                    <div class="flex items-center justify-between mt-3">
                                        <div class="flex items-center space-x-1 text-xs text-gray-500">
                                            <i class="fas fa-star text-maritime-gold"></i>
                                            <span>4.9 • 47 reviews on QAAQ</span>
                                        </div>
                                        <button class="bg-maritime-blue text-white px-3 py-1 rounded-lg text-sm hover:bg-maritime-navy transition duration-300">
                                            Connect
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Maritime Professional Card 2 -->
                        <div class="bg-white rounded-xl shadow-sm border p-4 hover:shadow-md transition duration-300" data-mock="true">
                            <div class="flex items-start space-x-3">
                                <div class="w-12 h-12 bg-ocean-blue rounded-full flex items-center justify-center">
                                    <span class="text-white font-semibold">2E</span>
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center justify-between">
                                        <h5 class="font-semibold text-gray-900">Maria Rodriguez</h5>
                                        <span class="text-xs text-gray-500">4.7 km</span>
                                    </div>
                                    <p class="text-sm text-gray-600">Tanker • 2nd Engineer • 8 years exp</p>
                                    <div class="flex items-center space-x-2 mt-2">
                                        <span class="bg-maritime-blue/10 text-maritime-blue text-xs px-2 py-1 rounded-full">Alfa Laval</span>
                                        <span class="bg-maritime-blue/10 text-maritime-blue text-xs px-2 py-1 rounded-full">Mitsubishi</span>
                                    </div>
                                    <div class="flex items-center justify-between mt-3">
                                        <div class="flex items-center space-x-1 text-xs text-gray-500">
                                            <i class="fas fa-star text-maritime-gold"></i>
                                            <span>4.7 • 23 reviews on QAAQ</span>
                                        </div>
                                        <button class="bg-maritime-blue text-white px-3 py-1 rounded-lg text-sm hover:bg-maritime-navy transition duration-300">
                                            Connect
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Maritime Professional Card 3 -->
                        <div class="bg-white rounded-xl shadow-sm border p-4 hover:shadow-md transition duration-300" data-mock="true">
                            <div class="flex items-start space-x-3">
                                <div class="w-12 h-12 bg-maritime-gold rounded-full flex items-center justify-center">
                                    <span class="text-white font-semibold">EO</span>
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center justify-between">
                                        <h5 class="font-semibold text-gray-900">Captain Hansen</h5>
                                        <span class="text-xs text-gray-500">1.2 km</span>
                                    </div>
                                    <p class="text-sm text-gray-600">Offshore Vessel • Electrical Officer • 12 years exp</p>
                                    <div class="flex items-center space-x-2 mt-2">
                                        <span class="bg-maritime-blue/10 text-maritime-blue text-xs px-2 py-1 rounded-full">ABB</span>
                                        <span class="bg-maritime-blue/10 text-maritime-blue text-xs px-2 py-1 rounded-full">Schneider</span>
                                    </div>
                                    <div class="flex items-center justify-between mt-3">
                                        <div class="flex items-center space-x-1 text-xs text-gray-500">
                                            <i class="fas fa-star text-maritime-gold"></i>
                                            <span>4.8 • 31 reviews on QAAQ</span>
                                        </div>
                                        <button class="bg-maritime-blue text-white px-3 py-1 rounded-lg text-sm hover:bg-maritime-navy transition duration-300">
                                            Connect
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- @END_MAP )) -->
                    </div>
                </div>

                <!-- Map View -->
                <div id="content-map" class="tab-content hidden h-full relative">
                    <!-- Placeholder for interactive map -->
                    <!-- This modern port scene shows multiple maritime professionals at a busy commercial port -->
                    <div class="h-full bg-cover bg-center relative" style="background-image: url('https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600');" data-implementation="Replace with interactive map using Mapbox or Google Maps API">
                        <div class="absolute inset-0 bg-maritime-navy/20"></div>
                        
                        <!-- Map Controls -->
                        <div class="absolute top-4 right-4 space-y-2">
                            <button class="bg-white shadow-lg rounded-lg p-2 hover:bg-gray-50">
                                <i class="fas fa-plus text-gray-600"></i>
                            </button>
                            <button class="bg-white shadow-lg rounded-lg p-2 hover:bg-gray-50">
                                <i class="fas fa-minus text-gray-600"></i>
                            </button>
                            <button class="bg-white shadow-lg rounded-lg p-2 hover:bg-gray-50">
                                <i class="fas fa-location-arrow text-maritime-blue"></i>
                            </button>
                        </div>
                        
                        <!-- Sample Location Markers -->
                        <div class="absolute top-1/3 left-1/4 transform -translate-x-1/2 -translate-y-1/2">
                            <div class="bg-maritime-blue rounded-full p-2 shadow-lg border-2 border-white cursor-pointer hover:scale-110 transition duration-300" data-implementation="Show professional info on click">
                                <i class="fas fa-user text-white text-sm"></i>
                            </div>
                        </div>
                        
                        <div class="absolute top-1/2 right-1/3 transform -translate-x-1/2 -translate-y-1/2">
                            <div class="bg-maritime-gold rounded-full p-2 shadow-lg border-2 border-white cursor-pointer hover:scale-110 transition duration-300">
                                <i class="fas fa-user text-white text-sm"></i>
                            </div>
                        </div>
                        
                        <div class="absolute bottom-1/3 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                            <div class="bg-ocean-blue rounded-full p-2 shadow-lg border-2 border-white cursor-pointer hover:scale-110 transition duration-300">
                                <i class="fas fa-user text-white text-sm"></i>
                            </div>
                        </div>
                        
                        <!-- Current Location -->
                        <div class="absolute bottom-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                            <div class="bg-red-500 rounded-full p-3 shadow-lg border-2 border-white animate-pulse">
                                <i class="fas fa-crosshairs text-white"></i>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Chat View -->
                <div id="content-chat" class="tab-content hidden h-full flex flex-col">
                    <!-- Chat List Header -->
                    <div class="bg-white border-b p-4">
                        <div class="flex items-center justify-between">
                            <h4 class="font-semibold text-gray-900">Active Chats</h4>
                            <button class="text-maritime-blue hover:text-maritime-navy">
                                <i class="fas fa-plus text-lg"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- Chat List -->
                    <div class="flex-1 overflow-y-auto bg-white">
                        <!-- Chat Item 1 -->
                        <div class="border-b p-4 hover:bg-gray-50 cursor-pointer" onclick="openChat('chief-smith')" data-mock="true">
                            <div class="flex items-center space-x-3">
                                <div class="relative">
                                    <div class="w-12 h-12 bg-maritime-navy rounded-full flex items-center justify-center">
                                        <span class="text-white font-semibold text-sm">CE</span>
                                    </div>
                                    <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center justify-between">
                                        <h5 class="font-semibold text-gray-900">Chief Engineer Smith</h5>
                                        <span class="text-xs text-gray-500">2m</span>
                                    </div>
                                    <p class="text-sm text-gray-600 truncate">Thanks for the MAN B&W troubleshooting tip! ⚓</p>
                                </div>
                                <div class="bg-maritime-blue rounded-full w-5 h-5 flex items-center justify-center">
                                    <span class="text-white text-xs">2</span>
                                </div>
                            </div>
                        </div>

                        <!-- Chat Item 2 -->
                        <div class="border-b p-4 hover:bg-gray-50 cursor-pointer" onclick="openChat('maria-rodriguez')" data-mock="true">
                            <div class="flex items-center space-x-3">
                                <div class="relative">
                                    <div class="w-12 h-12 bg-ocean-blue rounded-full flex items-center justify-center">
                                        <span class="text-white font-semibold text-sm">MR</span>
                                    </div>
                                    <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center justify-between">
                                        <h5 class="font-semibold text-gray-900">Maria Rodriguez</h5>
                                        <span class="text-xs text-gray-500">15m</span>
                                    </div>
                                    <p class="text-sm text-gray-600 truncate">The purifier issue is resolved! 🔧</p>
                                </div>
                                <div class="bg-maritime-blue rounded-full w-5 h-5 flex items-center justify-center">
                                    <span class="text-white text-xs">1</span>
                                </div>
                            </div>
                        </div>

                        <!-- Group Chat -->
                        <div class="border-b p-4 hover:bg-gray-50 cursor-pointer" onclick="openChat('port-engineers')" data-mock="true">
                            <div class="flex items-center space-x-3">
                                <div class="relative">
                                    <div class="w-12 h-12 bg-maritime-gold rounded-full flex items-center justify-center">
                                        <i class="fas fa-users text-white"></i>
                                    </div>
                                    <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 rounded-full border-2 border-white"></div>
                                </div>
                                <div class="flex-1">
                                    <div class="flex items-center justify-between">
                                        <h5 class="font-semibold text-gray-900">Port Engineers Group</h5>
                                        <span class="text-xs text-gray-500">1h</span>
                                    </div>
                                    <p class="text-sm text-gray-600 truncate">John: Anyone familiar with Caterpillar 3512B issues?</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- @END_COMPONENT: ContentArea -->
        </div>
        <!-- @END_COMPONENT: MainApp -->

        <!-- @COMPONENT: ChatWindow -->
        <div id="chat-window" class="screen hidden h-screen bg-white flex flex-col">
            <!-- Chat Header -->
            <div class="bg-maritime-navy text-white p-4 flex items-center space-x-3">
                <button onclick="closeChat()" class="text-white">
                    <i class="fas fa-arrow-left text-lg"></i>
                </button>
                <div class="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                    <span class="font-semibold text-sm" id="chat-initials">CE</span>
                </div>
                <div class="flex-1">
                    <h3 class="font-semibold" id="chat-name">Chief Engineer Smith</h3>
                    <p class="text-sm opacity-80" id="chat-status">Online • 2.3 km away</p>
                </div>
                <button class="text-white">
                    <i class="fas fa-phone text-lg"></i>
                </button>
            </div>
            
            <!-- Chat Messages -->
            <div class="flex-1 overflow-y-auto p-4 space-y-4 bg-gray-50" data-mock="true">
                <!-- Received Message -->
                <div class="flex items-start space-x-2">
                    <div class="w-8 h-8 bg-maritime-navy rounded-full flex items-center justify-center">
                        <span class="text-white text-xs font-semibold">CE</span>
                    </div>
                    <div class="flex-1">
                        <div class="bg-white rounded-lg rounded-tl-none p-3 shadow-sm">
                            <p class="text-gray-800">Hi! Saw your question about the MAN B&W exhaust temperature issue on QAAQ. Have you checked the turbocharger nozzle ring? ⚓</p>
                        </div>
                        <span class="text-xs text-gray-500 ml-2">10:30 AM</span>
                    </div>
                </div>
                
                <!-- Sent Message -->
                <div class="flex items-start space-x-2 justify-end">
                    <div class="flex-1">
                        <div class="bg-maritime-blue text-white rounded-lg rounded-tr-none p-3 ml-12">
                            <p>Thanks! Yes, I cleaned the nozzle ring but still getting high temps. Could it be the exhaust valve? 🔧</p>
                        </div>
                        <div class="text-right">
                            <span class="text-xs text-gray-500 mr-2">10:32 AM</span>
                        </div>
                    </div>
                </div>
                
                <!-- Received Message -->
                <div class="flex items-start space-x-2">
                    <div class="w-8 h-8 bg-maritime-navy rounded-full flex items-center justify-center">
                        <span class="text-white text-xs font-semibold">CE</span>
                    </div>
                    <div class="flex-1">
                        <div class="bg-white rounded-lg rounded-tl-none p-3 shadow-sm">
                            <p class="text-gray-800">Exactly! Check the exhaust valve clearance. We had the exact same issue last month. Valve was sticking due to carbon buildup. 🚢</p>
                        </div>
                        <span class="text-xs text-gray-500 ml-2">10:35 AM</span>
                    </div>
                </div>
                
                <!-- System Message -->
                <div class="text-center">
                    <div class="inline-block bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm">
                        <i class="fas fa-shield-alt mr-1"></i>
                        Maritime credentials verified via QAAQ
                    </div>
                </div>
            </div>
            
            <!-- Chat Input -->
            <div class="bg-white border-t p-4" data-implementation="Implement WebSocket for real-time messaging">
                <div class="flex items-center space-x-3">
                    <button class="text-gray-400 hover:text-maritime-blue">
                        <i class="fas fa-paperclip text-lg"></i>
                    </button>
                    <div class="flex-1 relative">
                        <input type="text" placeholder="Type a message..." 
                               class="w-full px-4 py-2 border border-gray-300 rounded-full focus:ring-2 focus:ring-maritime-blue focus:border-transparent">
                        <button class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-maritime-blue">
                            <i class="fas fa-face-smile text-lg"></i>
                        </button>
                    </div>
                    <button class="bg-maritime-blue text-white p-2 rounded-full hover:bg-maritime-navy transition duration-300">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>
        <!-- @END_COMPONENT: ChatWindow -->

        <!-- @COMPONENT: SettingsScreen -->
        <div id="settings-screen" class="screen hidden h-screen bg-white">
            <div class="p-6">
                <!-- Header -->
                <div class="flex items-center justify-between mb-8 pt-4">
                    <button onclick="closeSettings()" class="text-maritime-navy">
                        <i class="fas fa-arrow-left text-xl"></i>
                    </button>
                    <h2 class="text-xl font-semibold text-maritime-navy">Settings</h2>
                    <div></div>
                </div>
                
                <!-- Privacy Controls -->
                <div class="space-y-6">
                    <div class="bg-red-50 border border-red-200 rounded-xl p-4">
                        <h3 class="font-semibold text-red-800 mb-2">
                            <i class="fas fa-shield-alt mr-2"></i>Privacy & Safety
                        </h3>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="font-medium text-gray-900">Location Sharing</p>
                                    <p class="text-sm text-gray-600">Allow others to see your location</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-maritime-blue/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-maritime-blue"></div>
                                </label>
                            </div>
                            
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="font-medium text-gray-900">Show Online Status</p>
                                    <p class="text-sm text-gray-600">Let others know when you're active</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-maritime-blue/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-maritime-blue"></div>
                                </label>
                            </div>
                            
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="font-medium text-gray-900">Profile Visibility</p>
                                    <p class="text-sm text-gray-600">Who can see your full profile</p>
                                </div>
                                <select class="border border-gray-300 rounded-lg px-3 py-1 text-sm">
                                    <option>Everyone</option>
                                    <option>Maritime Professionals Only</option>
                                    <option>Connections Only</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- QAAQ Integration -->
                    <div class="bg-maritime-blue/5 border border-maritime-blue/20 rounded-xl p-4">
                        <h3 class="font-semibold text-maritime-navy mb-2">
                            <i class="fas fa-link mr-2"></i>QAAQ Integration
                        </h3>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <div>
                                    <p class="font-medium text-gray-900">Sync QAAQ Profile</p>
                                    <p class="text-sm text-gray-600">Show QAAQ reputation and expertise</p>
                                </div>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-maritime-blue/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-maritime-blue"></div>
                                </label>
                            </div>
                            
                            <div class="bg-white rounded-lg p-3 border">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <p class="font-medium text-gray-900">QAAQ Account Status</p>
                                        <p class="text-sm text-green-600">✓ Connected & Verified</p>
                                    </div>
                                    <button class="text-maritime-blue hover:text-maritime-navy text-sm font-medium">
                                        View Profile
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Notification Settings -->
                    <div class="bg-white border rounded-xl p-4">
                        <h3 class="font-semibold text-gray-900 mb-4">
                            <i class="fas fa-bell mr-2"></i>Notifications
                        </h3>
                        <div class="space-y-4">
                            <div class="flex items-center justify-between">
                                <span class="text-gray-700">New Connection Requests</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-maritime-blue/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-maritime-blue"></div>
                                </label>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-700">Chat Messages</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" checked class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-maritime-blue/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-maritime-blue"></div>
                                </label>
                            </div>
                            <div class="flex items-center justify-between">
                                <span class="text-gray-700">Nearby Maritime Professionals</span>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" class="sr-only peer">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-maritime-blue/20 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-maritime-blue"></div>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- @END_COMPONENT: SettingsScreen -->
    </div>

    <script>
        // Screen management
        function showScreen(screenId) {
            document.querySelectorAll('.screen').forEach(screen => {
                screen.classList.add('hidden');
            });
            document.getElementById(screenId).classList.remove('hidden');
        }

        function showWelcome() {
            showScreen('welcome-screen');
        }

        function showLogin() {
            showScreen('login-screen');
        }

        function showRegister() {
            showScreen('register-screen');
        }

        function showMainApp() {
            showScreen('main-app');
        }

        function showSettings() {
            showScreen('settings-screen');
        }

        function closeSettings() {
            showScreen('main-app');
        }

        // Tab management
        function showTab(tabName) {
            // Update tab buttons
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('text-maritime-blue', 'border-maritime-blue');
                btn.classList.add('text-gray-500', 'border-transparent');
            });
            
            document.getElementById(`tab-${tabName}`).classList.remove('text-gray-500', 'border-transparent');
            document.getElementById(`tab-${tabName}`).classList.add('text-maritime-blue', 'border-maritime-blue');

            // Show/hide content
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.add('hidden');
            });
            document.getElementById(`content-${tabName}`).classList.remove('hidden');
        }

        // Chat functionality
        function openChat(chatId) {
            showScreen('chat-window');
            // TODO: Load specific chat data based on chatId
        }

        function closeChat() {
            showScreen('main-app');
            showTab('chat');
        }

        // Location controls
        function updateRadius(value) {
            document.getElementById('radius-value').textContent = value + ' km';
            // TODO: Update nearby professionals based on new radius
        }

        function toggleLocationSharing() {
            // TODO: Toggle location sharing functionality
            console.log('Location sharing toggled');
        }

        // Initialize app
        (function() {
            // Show welcome screen by default
            showScreen('welcome-screen');
        })();
    </script>
</body>
</html>