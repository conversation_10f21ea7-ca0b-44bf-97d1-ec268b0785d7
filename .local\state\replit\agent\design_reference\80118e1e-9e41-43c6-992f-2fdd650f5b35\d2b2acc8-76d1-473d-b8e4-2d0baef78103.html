<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>1234 Koi Hai? - Maritime Community Discovery</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'navy-blue': '#1e3a5f',
                        'ocean-teal': '#0891b2',
                        'light-teal': '#67e8f9',
                        'maritime-gray': '#374151'
                    },
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif']
                    }
                }
            }
        };
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-50 font-inter">
    
    <!-- Mobile Navigation Header -->
    <header class="bg-navy-blue text-white shadow-lg fixed top-0 w-full z-50">
        <div class="max-w-7xl mx-auto px-4 py-3">
            <div class="flex items-center justify-between">
                <div class="flex items-center space-x-3">
                    <i class="fas fa-anchor text-ocean-teal text-xl"></i>
                    <h1 class="text-lg font-bold">1234 Koi Hai?</h1>
                </div>
                <div class="flex items-center space-x-3">
                    <button class="p-2 rounded-lg bg-ocean-teal text-white">
                        <i class="fas fa-search"></i>
                    </button>
                    <button class="p-2 rounded-lg bg-gray-700 text-white">
                        <i class="fas fa-user"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="pt-16 pb-20">
        
        <!-- Hero Section -->
        <section class="bg-gradient-to-br from-navy-blue to-ocean-teal text-white py-8">
            <div class="max-w-7xl mx-auto px-4">
                <div class="text-center space-y-4">
                    <h2 class="text-2xl md:text-3xl font-bold">Find Your Maritime Community</h2>
                    <p class="text-light-teal max-w-md mx-auto">Connect with officers, crew, and sailors nearby. Join location-based groups for knowledge sharing and social connections.</p>
                </div>
            </div>
        </section>

        <!-- Discovery Mode Toggle -->
        <section class="bg-white shadow-sm border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4 py-4">
                <div class="flex justify-center space-x-1 bg-gray-100 rounded-lg p-1">
                    <button id="port-mode" class="flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors bg-ocean-teal text-white">
                        <i class="fas fa-ship mr-2"></i>Port Mode
                    </button>
                    <button id="airport-mode" class="flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors text-gray-700 hover:bg-gray-200">
                        <i class="fas fa-plane mr-2"></i>Airport Mode
                    </button>
                </div>
            </div>
        </section>

        <!-- Current Location & Nearby Detection -->
        <section class="bg-white border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4 py-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                        <div class="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                        <div>
                            <p class="font-medium text-gray-900">Hamburg Port</p>
                            <p class="text-sm text-gray-500">12 officers nearby • 8 in transit</p>
                        </div>
                    </div>
                    <button class="text-ocean-teal font-medium text-sm">
                        Change Location
                    </button>
                </div>
            </div>
        </section>

        <!-- QAAQ AI Question Bar -->
        <section class="bg-white border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4 py-4">
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-robot text-ocean-teal"></i>
                    </div>
                    <input type="text" 
                           placeholder="Ask QAAQ AI about machinery, procedures, or find specific groups..."
                           class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-ocean-teal focus:border-transparent">
                    <div class="absolute inset-y-0 right-0 flex items-center pr-3">
                        <button class="bg-ocean-teal text-white px-4 py-1 rounded text-sm font-medium">
                            Ask
                        </button>
                    </div>
                </div>
                <p class="text-xs text-gray-500 mt-2">
                    <i class="fas fa-magic mr-1"></i>
                    Auto-categorizes to CPSS groups based on location and topic
                </p>
            </div>
        </section>

        <!-- Group Categories -->
        <section class="max-w-7xl mx-auto px-4 py-6">
            
            <!-- Official QAAQ Groups Section -->
            <div class="mb-8">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">
                        <i class="fas fa-shield-alt text-ocean-teal mr-2"></i>
                        Official QAAQ Groups
                    </h3>
                    <span class="text-sm text-gray-500">Moderated by QAAQ Community</span>
                </div>
                
                <!-- Official Group Card -->
                <div class="bg-white rounded-lg shadow-md border-l-4 border-ocean-teal overflow-hidden">
                    <div class="p-4">
                        <div class="flex items-start justify-between">
                            <div class="flex-1">
                                <h4 class="font-semibold text-gray-900 mb-1">QAAQ Hamburg Port</h4>
                                <p class="text-sm text-gray-600 mb-2">Official knowledge-sharing group for Hamburg Port maritime community</p>
                                <div class="flex items-center space-x-4 text-sm text-gray-500">
                                    <span><i class="fas fa-users mr-1"></i>847 members</span>
                                    <span><i class="fas fa-comments mr-1"></i>23 active today</span>
                                    <span><i class="fas fa-clock mr-1"></i>Last activity: 5m ago</span>
                                </div>
                            </div>
                            <div class="flex flex-col items-end space-y-2 ml-4">
                                <span class="bg-ocean-teal text-white text-xs font-medium px-2 py-1 rounded-full">QAAQ Official</span>
                                <button class="bg-navy-blue text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-800 transition-colors">
                                    <i class="fab fa-whatsapp mr-2"></i>Join Group
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="bg-gray-50 px-4 py-2 border-t border-gray-200">
                        <p class="text-xs text-gray-600">
                            <i class="fas fa-info-circle mr-1"></i>
                            Primary technical discussions, machinery Q&A, and professional knowledge sharing
                        </p>
                    </div>
                </div>
            </div>

            <!-- User Groups Section -->
            <div class="mb-8">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg font-semibold text-gray-900">
                        <i class="fas fa-users text-light-teal mr-2"></i>
                        Community Groups
                    </h3>
                    <button class="text-ocean-teal font-medium text-sm">
                        <i class="fas fa-plus mr-1"></i>Create Group
                    </button>
                </div>

                <!-- User Group Cards Grid -->
                <div class="space-y-4">
                    
                    <!-- User Group 1 -->
                    <div class="bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden">
                        <div class="p-4">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2 mb-1">
                                        <h4 class="font-semibold text-gray-900">Hamburg Night Shift Crew</h4>
                                        <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">User Group</span>
                                    </div>
                                    <p class="text-sm text-gray-600 mb-2">Connect with fellow night shift workers for meal sharing and company</p>
                                    <div class="flex items-center space-x-4 text-sm text-gray-500">
                                        <span><i class="fas fa-users mr-1"></i>24 members</span>
                                        <span><i class="fas fa-moon mr-1"></i>Night Shift</span>
                                        <span><i class="fas fa-utensils mr-1"></i>Food & Social</span>
                                    </div>
                                </div>
                                <button class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-200 transition-colors">
                                    <i class="fab fa-whatsapp mr-2"></i>Join
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- User Group 2 -->
                    <div class="bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden">
                        <div class="p-4">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2 mb-1">
                                        <h4 class="font-semibold text-gray-900">Hamburg Port Taxi Share</h4>
                                        <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">User Group</span>
                                    </div>
                                    <p class="text-sm text-gray-600 mb-2">Share taxis to/from port, airport, and city center</p>
                                    <div class="flex items-center space-x-4 text-sm text-gray-500">
                                        <span><i class="fas fa-users mr-1"></i>156 members</span>
                                        <span><i class="fas fa-taxi mr-1"></i>Transport</span>
                                        <span><i class="fas fa-clock mr-1"></i>Active: 2h ago</span>
                                    </div>
                                </div>
                                <button class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-200 transition-colors">
                                    <i class="fab fa-whatsapp mr-2"></i>Join
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- User Group 3 -->
                    <div class="bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden">
                        <div class="p-4">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2 mb-1">
                                        <h4 class="font-semibold text-gray-900">Hamburg Football Sundays</h4>
                                        <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">User Group</span>
                                    </div>
                                    <p class="text-sm text-gray-600 mb-2">Weekly football matches and sports activities for maritime crew</p>
                                    <div class="flex items-center space-x-4 text-sm text-gray-500">
                                        <span><i class="fas fa-users mr-1"></i>32 members</span>
                                        <span><i class="fas fa-futbol mr-1"></i>Sports</span>
                                        <span><i class="fas fa-calendar mr-1"></i>Every Sunday</span>
                                    </div>
                                </div>
                                <button class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-200 transition-colors">
                                    <i class="fab fa-whatsapp mr-2"></i>Join
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- User Group 4 -->
                    <div class="bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden">
                        <div class="p-4">
                            <div class="flex items-start justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2 mb-1">
                                        <h4 class="font-semibold text-gray-900">Hamburg Indian Crew Meetup</h4>
                                        <span class="bg-blue-100 text-blue-800 text-xs font-medium px-2 py-1 rounded-full">User Group</span>
                                    </div>
                                    <p class="text-sm text-gray-600 mb-2">Connect with Indian maritime professionals in Hamburg for cultural events</p>
                                    <div class="flex items-center space-x-4 text-sm text-gray-500">
                                        <span><i class="fas fa-users mr-1"></i>89 members</span>
                                        <span><i class="fas fa-globe-asia mr-1"></i>Cultural</span>
                                        <span><i class="fas fa-heart mr-1"></i>Community</span>
                                    </div>
                                </div>
                                <button class="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg text-sm font-medium hover:bg-gray-200 transition-colors">
                                    <i class="fab fa-whatsapp mr-2"></i>Join
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Activity Section -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-activity text-light-teal mr-2"></i>
                    Recent Community Activity
                </h3>
                
                <div class="bg-white rounded-lg shadow-md border border-gray-200 overflow-hidden">
                    <div class="divide-y divide-gray-200">
                        
                        <!-- Activity Item 1 -->
                        <div class="p-4">
                            <div class="flex items-start space-x-3">
                                <!-- A professional headshot of a maritime officer in uniform -->
                                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=150&h=150" alt="Chief Engineer profile" class="w-10 h-10 rounded-full object-cover">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2">
                                        <p class="font-medium text-gray-900">Piyush Gupta</p>
                                        <span class="text-xs text-gray-500">Chief Engineer</span>
                                        <span class="text-xs text-gray-400">•</span>
                                        <span class="text-xs text-gray-500">5 minutes ago</span>
                                    </div>
                                    <p class="text-sm text-gray-600 mt-1">Asked about MAN B&W 6G50ME engine troubleshooting in <span class="text-ocean-teal font-medium">QAAQ Hamburg Port</span></p>
                                    <div class="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                                        <span><i class="fas fa-comments mr-1"></i>3 replies</span>
                                        <span><i class="fas fa-eye mr-1"></i>12 views</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Activity Item 2 -->
                        <div class="p-4">
                            <div class="flex items-start space-x-3">
                                <!-- A maritime crew member in work clothes -->
                                <img src="https://pixabay.com/get/g652daff20f11750a687aaa2464e86d8801cdfe2d7dc22a8900c6c01204cc43014fcba453e236e7dc6166ea92193a34762164ac3ca65f755e782b2b1b03e72c87_1280.jpg" alt="2nd Engineer profile" class="w-10 h-10 rounded-full object-cover">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2">
                                        <p class="font-medium text-gray-900">Marcus Weber</p>
                                        <span class="text-xs text-gray-500">2nd Engineer</span>
                                        <span class="text-xs text-gray-400">•</span>
                                        <span class="text-xs text-gray-500">15 minutes ago</span>
                                    </div>
                                    <p class="text-sm text-gray-600 mt-1">Organizing taxi share to city center in <span class="text-blue-600 font-medium">Hamburg Port Taxi Share</span></p>
                                    <div class="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                                        <span><i class="fas fa-users mr-1"></i>2 joined</span>
                                        <span><i class="fas fa-taxi mr-1"></i>Departure: 18:00</span>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Activity Item 3 -->
                        <div class="p-4">
                            <div class="flex items-start space-x-3">
                                <!-- A maritime officer working on deck -->
                                <img src="https://images.unsplash.com/photo-1568602471122-7832951cc4c5?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=150&h=150" alt="4th Engineer profile" class="w-10 h-10 rounded-full object-cover">
                                <div class="flex-1">
                                    <div class="flex items-center space-x-2">
                                        <p class="font-medium text-gray-900">Ahmed Hassan</p>
                                        <span class="text-xs text-gray-500">4th Engineer</span>
                                        <span class="text-xs text-gray-400">•</span>
                                        <span class="text-xs text-gray-500">1 hour ago</span>
                                    </div>
                                    <p class="text-sm text-gray-600 mt-1">Posted photos from ship maintenance in <span class="text-ocean-teal font-medium">QAAQ Hamburg Port</span></p>
                                    <div class="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                                        <span><i class="fas fa-camera mr-1"></i>3 photos</span>
                                        <span><i class="fas fa-heart mr-1"></i>8 likes</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Location-based Discovery Section -->
            <div class="mb-8">
                <h3 class="text-lg font-semibold text-gray-900 mb-4">
                    <i class="fas fa-map-marker-alt text-light-teal mr-2"></i>
                    Explore Other Locations
                </h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    
                    <!-- Location Card 1 -->
                    <div class="bg-white rounded-lg shadow-md border border-gray-200 p-4">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="font-semibold text-gray-900">Rotterdam Port</h4>
                            <span class="text-sm text-gray-500">45km away</span>
                        </div>
                        <div class="space-y-2 text-sm text-gray-600">
                            <div class="flex items-center justify-between">
                                <span>QAAQ Rotterdam Port</span>
                                <span class="text-ocean-teal font-medium">1,234 members</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span>+ 8 community groups</span>
                                <span class="text-gray-500">326 total members</span>
                            </div>
                        </div>
                        <button class="w-full mt-3 bg-gray-100 text-gray-700 py-2 rounded-lg text-sm font-medium hover:bg-gray-200 transition-colors">
                            View Groups
                        </button>
                    </div>

                    <!-- Location Card 2 -->
                    <div class="bg-white rounded-lg shadow-md border border-gray-200 p-4">
                        <div class="flex items-center justify-between mb-3">
                            <h4 class="font-semibold text-gray-900">Bremen Port</h4>
                            <span class="text-sm text-gray-500">120km away</span>
                        </div>
                        <div class="space-y-2 text-sm text-gray-600">
                            <div class="flex items-center justify-between">
                                <span>QAAQ Bremen Port</span>
                                <span class="text-ocean-teal font-medium">567 members</span>
                            </div>
                            <div class="flex items-center justify-between">
                                <span>+ 5 community groups</span>
                                <span class="text-gray-500">189 total members</span>
                            </div>
                        </div>
                        <button class="w-full mt-3 bg-gray-100 text-gray-700 py-2 rounded-lg text-sm font-medium hover:bg-gray-200 transition-colors">
                            View Groups
                        </button>
                    </div>
                </div>
            </div>

        </section>

    </main>

    <!-- Bottom Navigation -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 shadow-lg">
        <div class="max-w-7xl mx-auto px-4">
            <div class="flex justify-around py-2">
                <button class="flex flex-col items-center py-2 px-3 text-ocean-teal">
                    <i class="fas fa-home text-lg mb-1"></i>
                    <span class="text-xs font-medium">Home</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3 text-gray-500">
                    <i class="fas fa-compass text-lg mb-1"></i>
                    <span class="text-xs">Discover</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3 text-gray-500">
                    <i class="fas fa-comments text-lg mb-1"></i>
                    <span class="text-xs">Groups</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3 text-gray-500">
                    <i class="fas fa-robot text-lg mb-1"></i>
                    <span class="text-xs">QAAQ AI</span>
                </button>
                <button class="flex flex-col items-center py-2 px-3 text-gray-500">
                    <i class="fas fa-user text-lg mb-1"></i>
                    <span class="text-xs">Profile</span>
                </button>
            </div>
        </div>
    </nav>

    <script>
        (function() {
            // Mode Toggle Functionality
            const portModeBtn = document.getElementById('port-mode');
            const airportModeBtn = document.getElementById('airport-mode');

            function switchMode(activeBtn, inactiveBtn) {
                activeBtn.classList.add('bg-ocean-teal', 'text-white');
                activeBtn.classList.remove('text-gray-700', 'hover:bg-gray-200');
                
                inactiveBtn.classList.remove('bg-ocean-teal', 'text-white');
                inactiveBtn.classList.add('text-gray-700', 'hover:bg-gray-200');
            }

            portModeBtn.addEventListener('click', () => {
                switchMode(portModeBtn, airportModeBtn);
                // TODO: Implement port mode logic, update location detection and groups
            });

            airportModeBtn.addEventListener('click', () => {
                switchMode(airportModeBtn, portModeBtn);
                // TODO: Implement airport mode logic, update location detection and groups
            });

            // Join Group Functionality
            const joinButtons = document.querySelectorAll('button:contains("Join")');
            joinButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // TODO: Implement WhatsApp group joining logic
                    console.log('Joining group via WhatsApp...');
                });
            });

            // QAAQ AI Question Bar
            const questionInput = document.querySelector('input[placeholder*="Ask QAAQ AI"]');
            const askButton = document.querySelector('button:contains("Ask")');
            
            askButton.addEventListener('click', () => {
                const question = questionInput.value.trim();
                if (question) {
                    // TODO: Implement QAAQ AI integration and CPSS categorization
                    console.log('Asking QAAQ AI:', question);
                    questionInput.value = '';
                }
            });

            questionInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    askButton.click();
                }
            });

            // Location Detection and Updates
            function updateLocationData() {
                // TODO: Implement real-time location detection
                // TODO: Update nearby officers count
                // TODO: Refresh group listings based on location
                console.log('Updating location-based data...');
            }

            // Auto-refresh location data every 30 seconds
            setInterval(updateLocationData, 30000);

            // Group Creation Modal
            const createGroupBtn = document.querySelector('button:contains("Create Group")');
            createGroupBtn.addEventListener('click', () => {
                // TODO: Implement group creation modal/form
                console.log('Opening group creation form...');
            });

        })();
    </script>

</body>
</html>