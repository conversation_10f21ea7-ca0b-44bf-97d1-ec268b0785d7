<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>1234 Koi Hai? - Maritime Professional Discovery</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'navy-primary': '#1B365D',
                        'ocean-teal': '#2C7A7B',
                        'light-teal': '#81E6D9',
                        'teal-50': '#E6FFFA',
                    },
                    fontFamily: {
                        'inter': ['Inter', 'system-ui', 'sans-serif'],
                    },
                    animation: {
                        'wave': 'wave 2s ease-in-out infinite',
                        'fade-in': 'fadeIn 0.5s ease-in-out',
                        'slide-up': 'slideUp 0.3s ease-out',
                    }
                }
            }
        };
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @keyframes wave {
            0%, 100% { transform: translateY(0); }
            50% { transform: translateY(-10px); }
        }
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        @keyframes slideUp {
            from { transform: translateY(100%); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        .wave-bg {
            background: linear-gradient(135deg, #1B365D 0%, #2C7A7B 100%);
        }
        .card-hover {
            transition: all 0.3s ease;
        }
        .card-hover:hover {
            transform: translateY(-4px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        .filter-panel {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.95);
        }
    </style>
</head>
<body class="font-inter bg-gray-50">
    <!-- @COMPONENT: Header [navigation and branding] -->
    <header class="bg-white shadow-sm border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <!-- Logo and Branding -->
                <div class="flex items-center space-x-3">
                    <div class="flex items-center justify-center w-10 h-10 bg-navy-primary rounded-full">
                        <i class="fas fa-anchor text-white text-lg"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-navy-primary">1234 Koi Hai?</h1>
                        <p class="text-sm text-gray-500">Maritime Discovery</p>
                    </div>
                </div>

                <!-- Navigation -->
                <nav class="hidden md:flex items-center space-x-6">
                    <a href="#" class="text-gray-600 hover:text-navy-primary transition-colors">
                        <i class="fas fa-compass mr-2"></i>Discover
                    </a>
                    <a href="#" class="text-gray-600 hover:text-navy-primary transition-colors">
                        <i class="fas fa-users mr-2"></i>Community
                    </a>
                    <a href="#" class="text-gray-600 hover:text-navy-primary transition-colors">
                        <i class="fas fa-question-circle mr-2"></i>QAAQ
                    </a>
                    <div class="flex items-center space-x-2">
                        <img src="https://ui-avatars.com/api/?name=PG&background=1B365D&color=fff&size=32" alt="Profile" class="w-8 h-8 rounded-full">
                        <span class="text-sm font-medium text-gray-700">Piyush G.</span>
                    </div>
                </nav>

                <!-- Mobile menu button -->
                <button class="md:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100">
                    <i class="fas fa-bars text-lg"></i>
                </button>
            </div>
        </div>
    </header>
    <!-- @END_COMPONENT: Header -->

    <!-- @COMPONENT: HeroSection [main discovery interface] -->
    <section class="wave-bg py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-4xl mx-auto text-center">
            <!-- Hero Content -->
            <div class="mb-8">
                <h2 class="text-4xl md:text-5xl font-bold text-white mb-4">
                    Who's There?
                    <span class="block text-2xl md:text-3xl font-normal text-light-teal mt-2">
                        Discover maritime professionals around you
                    </span>
                </h2>
                <p class="text-lg text-gray-200 max-w-2xl mx-auto">
                    Connect with fellow seafarers, officers, and crew members in your area. Build your maritime network.
                </p>
            </div>

            <!-- Location Display -->
            <div class="flex items-center justify-center mb-8 text-white">
                <i class="fas fa-map-marker-alt text-light-teal mr-2"></i>
                <span class="text-lg">Istanbul Port, Turkey</span>
                <button class="ml-3 text-light-teal hover:text-white transition-colors">
                    <i class="fas fa-edit text-sm"></i>
                </button>
            </div>

            <!-- Main Discovery Button -->
            <button class="bg-white text-navy-primary px-12 py-4 rounded-full text-xl font-semibold hover:bg-gray-50 transition-all duration-300 transform hover:scale-105 shadow-lg animate-wave">
                <i class="fas fa-search mr-3"></i>
                Who's There?
            </button>

            <!-- Distance Slider -->
            <div class="mt-8 max-w-md mx-auto">
                <label class="block text-white text-sm font-medium mb-3">Search Radius</label>
                <div class="relative">
                    <input type="range" min="1" max="50" value="10" class="w-full h-2 bg-white bg-opacity-20 rounded-lg appearance-none cursor-pointer slider">
                    <div class="flex justify-between text-sm text-gray-200 mt-2">
                        <span>1km</span>
                        <span class="font-medium text-light-teal">10km</span>
                        <span>50km</span>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- @END_COMPONENT: HeroSection -->

    <!-- @COMPONENT: FilterPanel [subtle side filters] -->
    <div class="fixed right-4 top-1/2 transform -translate-y-1/2 z-10">
        <div class="filter-panel rounded-lg p-4 shadow-lg border border-gray-200 w-48" id="filterPanel">
            <h3 class="text-sm font-semibold text-gray-700 mb-3 flex items-center">
                <i class="fas fa-filter mr-2"></i>Filters
            </h3>
            <div class="space-y-3">
                <label class="flex items-center">
                    <input type="checkbox" class="rounded border-gray-300 text-ocean-teal focus:ring-ocean-teal">
                    <span class="ml-2 text-sm text-gray-600">Officers only</span>
                </label>
                <label class="flex items-center">
                    <input type="checkbox" class="rounded border-gray-300 text-ocean-teal focus:ring-ocean-teal">
                    <span class="ml-2 text-sm text-gray-600">Crew only</span>
                </label>
                <label class="flex items-center">
                    <input type="checkbox" class="rounded border-gray-300 text-ocean-teal focus:ring-ocean-teal">
                    <span class="ml-2 text-sm text-gray-600">Same nationality</span>
                </label>
            </div>
        </div>
    </div>
    <!-- @END_COMPONENT: FilterPanel -->

    <!-- @COMPONENT: ResultsSection [discovered professionals] -->
    <section class="py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-7xl mx-auto">
            <!-- Results Header -->
            <div class="flex items-center justify-between mb-8">
                <div>
                    <h3 class="text-2xl font-bold text-gray-900">Maritime Professionals Nearby</h3>
                    <p class="text-gray-600 mt-1">24 professionals found within 10km</p>
                </div>
                <div class="flex items-center space-x-4">
                    <select class="border border-gray-300 rounded-md px-3 py-2 text-sm">
                        <option>Sort by Distance</option>
                        <option>Sort by Rank</option>
                        <option>Sort by Experience</option>
                    </select>
                </div>
            </div>

            <!-- @MAP: professionals.map(professional => ( -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                <!-- Professional Card 1 -->
                <div class="card-hover bg-white rounded-xl shadow-md border border-gray-200 p-6">
                    <div class="flex items-center justify-between mb-4">
                        <!-- Maritime professional from Turkey working on container ships -->
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&w=64&h=64&fit=crop&crop=face" alt="Professional profile" class="w-12 h-12 rounded-full object-cover">
                        <div class="flex items-center space-x-2">
                            <img src="https://flagcdn.com/w20/tr.png" alt="Turkey flag" class="w-5 h-3">
                            <span class="text-xs bg-navy-primary text-white px-2 py-1 rounded-full">2.5km</span>
                        </div>
                    </div>
                    <h4 class="font-semibold text-gray-900 mb-1">Mehmet Özkan</h4>
                    <p class="text-ocean-teal font-medium text-sm mb-1">Chief Engineer</p>
                    <p class="text-gray-600 text-sm mb-3">MSC Container Ship</p>
                    <div class="flex items-center text-xs text-gray-500 mb-4">
                        <i class="fas fa-ship mr-1"></i>
                        <span>15 years experience</span>
                    </div>
                    <button class="w-full bg-ocean-teal text-white py-2 rounded-lg text-sm font-medium hover:bg-ocean-teal/90 transition-colors">
                        Connect
                    </button>
                </div>

                <!-- Professional Card 2 -->
                <div class="card-hover bg-white rounded-xl shadow-md border border-gray-200 p-6">
                    <div class="flex items-center justify-between mb-4">
                        <!-- International sailor from Philippines working on tanker vessels -->
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&w=64&h=64&fit=crop&crop=face" alt="Professional profile" class="w-12 h-12 rounded-full object-cover">
                        <div class="flex items-center space-x-2">
                            <img src="https://flagcdn.com/w20/ph.png" alt="Philippines flag" class="w-5 h-3">
                            <span class="text-xs bg-navy-primary text-white px-2 py-1 rounded-full">4.2km</span>
                        </div>
                    </div>
                    <h4 class="font-semibold text-gray-900 mb-1">Carlos Santos</h4>
                    <p class="text-ocean-teal font-medium text-sm mb-1">2nd Officer</p>
                    <p class="text-gray-600 text-sm mb-3">Product Tanker</p>
                    <div class="flex items-center text-xs text-gray-500 mb-4">
                        <i class="fas fa-anchor mr-1"></i>
                        <span>8 years experience</span>
                    </div>
                    <button class="w-full bg-ocean-teal text-white py-2 rounded-lg text-sm font-medium hover:bg-ocean-teal/90 transition-colors">
                        Connect
                    </button>
                </div>

                <!-- Professional Card 3 -->
                <div class="card-hover bg-white rounded-xl shadow-md border border-gray-200 p-6">
                    <div class="flex items-center justify-between mb-4">
                        <!-- Female maritime officer representing diversity in shipping -->
                        <img src="https://pixabay.com/get/g23e44b0432b9eb9b2ce785bc8b830fa058a5042a8a1c3b119e6b7f033a476bedc6c14520ba605644a8a32eedc4741d8c539e85d4b79215952133e0635be10f26_1280.jpg" alt="Professional profile" class="w-12 h-12 rounded-full object-cover">
                        <div class="flex items-center space-x-2">
                            <img src="https://flagcdn.com/w20/no.png" alt="Norway flag" class="w-5 h-3">
                            <span class="text-xs bg-navy-primary text-white px-2 py-1 rounded-full">6.8km</span>
                        </div>
                    </div>
                    <h4 class="font-semibold text-gray-900 mb-1">Anna Larsen</h4>
                    <p class="text-ocean-teal font-medium text-sm mb-1">Captain</p>
                    <p class="text-gray-600 text-sm mb-3">Offshore Supply Vessel</p>
                    <div class="flex items-center text-xs text-gray-500 mb-4">
                        <i class="fas fa-compass mr-1"></i>
                        <span>22 years experience</span>
                    </div>
                    <button class="w-full bg-ocean-teal text-white py-2 rounded-lg text-sm font-medium hover:bg-ocean-teal/90 transition-colors">
                        Connect
                    </button>
                </div>

                <!-- Professional Card 4 -->
                <div class="card-hover bg-white rounded-xl shadow-md border border-gray-200 p-6">
                    <div class="flex items-center justify-between mb-4">
                        <!-- Young maritime engineer representing new generation of seafarers -->
                        <img src="https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&w=64&h=64&fit=crop&crop=face" alt="Professional profile" class="w-12 h-12 rounded-full object-cover">
                        <div class="flex items-center space-x-2">
                            <img src="https://flagcdn.com/w20/in.png" alt="India flag" class="w-5 h-3">
                            <span class="text-xs bg-navy-primary text-white px-2 py-1 rounded-full">3.1km</span>
                        </div>
                    </div>
                    <h4 class="font-semibold text-gray-900 mb-1">Rajesh Kumar</h4>
                    <p class="text-ocean-teal font-medium text-sm mb-1">3rd Engineer</p>
                    <p class="text-gray-600 text-sm mb-3">Bulk Carrier</p>
                    <div class="flex items-center text-xs text-gray-500 mb-4">
                        <i class="fas fa-cog mr-1"></i>
                        <span>5 years experience</span>
                    </div>
                    <button class="w-full bg-ocean-teal text-white py-2 rounded-lg text-sm font-medium hover:bg-ocean-teal/90 transition-colors">
                        Connect
                    </button>
                </div>

                <!-- Professional Card 5 -->
                <div class="card-hover bg-white rounded-xl shadow-md border border-gray-200 p-6">
                    <div class="flex items-center justify-between mb-4">
                        <!-- Experienced deck officer from Greece with maritime heritage -->
                        <img src="https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?ixlib=rb-4.0.3&w=64&h=64&fit=crop&crop=face" alt="Professional profile" class="w-12 h-12 rounded-full object-cover">
                        <div class="flex items-center space-x-2">
                            <img src="https://flagcdn.com/w20/gr.png" alt="Greece flag" class="w-5 h-3">
                            <span class="text-xs bg-navy-primary text-white px-2 py-1 rounded-full">7.5km</span>
                        </div>
                    </div>
                    <h4 class="font-semibold text-gray-900 mb-1">Dimitrios Kostas</h4>
                    <p class="text-ocean-teal font-medium text-sm mb-1">Chief Officer</p>
                    <p class="text-gray-600 text-sm mb-3">Dry Cargo Ship</p>
                    <div class="flex items-center text-xs text-gray-500 mb-4">
                        <i class="fas fa-map mr-1"></i>
                        <span>18 years experience</span>
                    </div>
                    <button class="w-full bg-ocean-teal text-white py-2 rounded-lg text-sm font-medium hover:bg-ocean-teal/90 transition-colors">
                        Connect
                    </button>
                </div>

                <!-- Professional Card 6 -->
                <div class="card-hover bg-white rounded-xl shadow-md border border-gray-200 p-6">
                    <div class="flex items-center justify-between mb-4">
                        <!-- Maritime electrical officer showcasing technical expertise -->
                        <img src="https://images.unsplash.com/photo-1507591064344-4c6ce005b128?ixlib=rb-4.0.3&w=64&h=64&fit=crop&crop=face" alt="Professional profile" class="w-12 h-12 rounded-full object-cover">
                        <div class="flex items-center space-x-2">
                            <img src="https://flagcdn.com/w20/dk.png" alt="Denmark flag" class="w-5 h-3">
                            <span class="text-xs bg-navy-primary text-white px-2 py-1 rounded-full">9.2km</span>
                        </div>
                    </div>
                    <h4 class="font-semibold text-gray-900 mb-1">Lars Nielsen</h4>
                    <p class="text-ocean-teal font-medium text-sm mb-1">Electrical Officer</p>
                    <p class="text-gray-600 text-sm mb-3">RoRo Ferry</p>
                    <div class="flex items-center text-xs text-gray-500 mb-4">
                        <i class="fas fa-bolt mr-1"></i>
                        <span>12 years experience</span>
                    </div>
                    <button class="w-full bg-ocean-teal text-white py-2 rounded-lg text-sm font-medium hover:bg-ocean-teal/90 transition-colors">
                        Connect
                    </button>
                </div>

                <!-- Professional Card 7 -->
                <div class="card-hover bg-white rounded-xl shadow-md border border-gray-200 p-6">
                    <div class="flex items-center justify-between mb-4">
                        <!-- Young female maritime cadet representing future of shipping -->
                        <img src="https://images.unsplash.com/photo-1580489944761-15a19d654956?ixlib=rb-4.0.3&w=64&h=64&fit=crop&crop=face" alt="Professional profile" class="w-12 h-12 rounded-full object-cover">
                        <div class="flex items-center space-x-2">
                            <img src="https://flagcdn.com/w20/gb.png" alt="UK flag" class="w-5 h-3">
                            <span class="text-xs bg-navy-primary text-white px-2 py-1 rounded-full">5.7km</span>
                        </div>
                    </div>
                    <h4 class="font-semibold text-gray-900 mb-1">Emma Thompson</h4>
                    <p class="text-ocean-teal font-medium text-sm mb-1">Deck Cadet</p>
                    <p class="text-gray-600 text-sm mb-3">Training Ship</p>
                    <div class="flex items-center text-xs text-gray-500 mb-4">
                        <i class="fas fa-graduation-cap mr-1"></i>
                        <span>1st year cadet</span>
                    </div>
                    <button class="w-full bg-ocean-teal text-white py-2 rounded-lg text-sm font-medium hover:bg-ocean-teal/90 transition-colors">
                        Connect
                    </button>
                </div>

                <!-- Professional Card 8 -->
                <div class="card-hover bg-white rounded-xl shadow-md border border-gray-200 p-6">
                    <div class="flex items-center justify-between mb-4">
                        <!-- Experienced maritime engineer from Ukraine -->
                        <img src="https://images.unsplash.com/photo-1500648767791-00dcc994a43e?ixlib=rb-4.0.3&w=64&h=64&fit=crop&crop=face" alt="Professional profile" class="w-12 h-12 rounded-full object-cover">
                        <div class="flex items-center space-x-2">
                            <img src="https://flagcdn.com/w20/ua.png" alt="Ukraine flag" class="w-5 h-3">
                            <span class="text-xs bg-navy-primary text-white px-2 py-1 rounded-full">8.9km</span>
                        </div>
                    </div>
                    <h4 class="font-semibold text-gray-900 mb-1">Viktor Petrenko</h4>
                    <p class="text-ocean-teal font-medium text-sm mb-1">2nd Engineer</p>
                    <p class="text-gray-600 text-sm mb-3">Chemical Tanker</p>
                    <div class="flex items-center text-xs text-gray-500 mb-4">
                        <i class="fas fa-tools mr-1"></i>
                        <span>11 years experience</span>
                    </div>
                    <button class="w-full bg-ocean-teal text-white py-2 rounded-lg text-sm font-medium hover:bg-ocean-teal/90 transition-colors">
                        Connect
                    </button>
                </div>
            </div>
            <!-- @END_MAP -->

            <!-- Load More Button -->
            <div class="text-center mt-12">
                <button class="bg-white border border-gray-300 text-gray-700 px-8 py-3 rounded-lg font-medium hover:bg-gray-50 transition-colors">
                    Load More Professionals
                </button>
            </div>
        </div>
    </section>
    <!-- @END_COMPONENT: ResultsSection -->

    <!-- @COMPONENT: QAAQIntegration [AI question bar] -->
    <section class="bg-navy-primary py-16 px-4 sm:px-6 lg:px-8">
        <div class="max-w-4xl mx-auto text-center">
            <div class="mb-8">
                <h3 class="text-3xl font-bold text-white mb-4">Got a Technical Question?</h3>
                <p class="text-gray-300 text-lg">Ask our AI or connect with maritime engineers who know the answer</p>
            </div>

            <!-- QAAQ Integration -->
            <div class="bg-white rounded-xl p-6 shadow-xl">
                <div class="flex items-center space-x-4 mb-4">
                    <div class="flex items-center justify-center w-12 h-12 bg-ocean-teal rounded-full">
                        <i class="fas fa-robot text-white text-xl"></i>
                    </div>
                    <div class="text-left">
                        <h4 class="font-semibold text-gray-900">QAAQ AI Assistant</h4>
                        <p class="text-sm text-gray-600">Get instant answers to maritime technical questions</p>
                    </div>
                </div>
                
                <div class="relative">
                    <input type="text" 
                           placeholder="Ask about engine issues, equipment troubleshooting, regulations..."
                           class="w-full p-4 border border-gray-300 rounded-lg text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-ocean-teal focus:border-transparent">
                    <button class="absolute right-2 top-2 bg-ocean-teal text-white p-2 rounded-md hover:bg-ocean-teal/90 transition-colors">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>

                <div class="flex items-center justify-center mt-4 text-sm text-gray-600">
                    <i class="fas fa-external-link-alt mr-2"></i>
                    <span>Powered by QAAQ Maritime Engineering Hub</span>
                </div>
            </div>
        </div>
    </section>
    <!-- @END_COMPONENT: QAAQIntegration -->

    <!-- @COMPONENT: CommunityFeatures [groups and networking] -->
    <section class="py-16 px-4 sm:px-6 lg:px-8 bg-gray-50">
        <div class="max-w-7xl mx-auto">
            <div class="text-center mb-12">
                <h3 class="text-3xl font-bold text-gray-900 mb-4">Join Maritime Communities</h3>
                <p class="text-gray-600 text-lg max-w-2xl mx-auto">
                    Connect with like-minded professionals in specialized groups and forums
                </p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                <!-- Community Group 1 -->
                <div class="bg-white rounded-xl shadow-md border border-gray-200 p-6">
                    <div class="flex items-center justify-between mb-4">
                        <!-- Maritime engineers networking in professional setting -->
                        <img src="https://images.unsplash.com/photo-1552664730-d307ca884978?ixlib=rb-4.0.3&w=400&h=200&fit=crop" alt="Engineers networking" class="w-full h-32 object-cover rounded-lg">
                    </div>
                    <h4 class="font-semibold text-gray-900 mb-2">Chief Engineers Network</h4>
                    <p class="text-gray-600 text-sm mb-4">Share experiences, troubleshooting tips, and best practices</p>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500">342 members</span>
                        <button class="bg-ocean-teal text-white px-4 py-2 rounded-lg text-sm hover:bg-ocean-teal/90 transition-colors">
                            Join Group
                        </button>
                    </div>
                </div>

                <!-- Community Group 2 -->
                <div class="bg-white rounded-xl shadow-md border border-gray-200 p-6">
                    <div class="flex items-center justify-between mb-4">
                        <!-- Diverse maritime crew showing unity and teamwork -->
                        <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&w=400&h=200&fit=crop" alt="Maritime crew unity" class="w-full h-32 object-cover rounded-lg">
                    </div>
                    <h4 class="font-semibold text-gray-900 mb-2">Deck Officers Forum</h4>
                    <p class="text-gray-600 text-sm mb-4">Navigation, cargo operations, and safety discussions</p>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500">198 members</span>
                        <button class="bg-ocean-teal text-white px-4 py-2 rounded-lg text-sm hover:bg-ocean-teal/90 transition-colors">
                            Join Group
                        </button>
                    </div>
                </div>

                <!-- Community Group 3 -->
                <div class="bg-white rounded-xl shadow-md border border-gray-200 p-6">
                    <div class="flex items-center justify-between mb-4">
                        <!-- International sailors meeting at port showing global maritime community -->
                        <img src="https://images.unsplash.com/photo-1559827260-dc66d52bef19?ixlib=rb-4.0.3&w=400&h=200&fit=crop" alt="International sailors meeting" class="w-full h-32 object-cover rounded-lg">
                    </div>
                    <h4 class="font-semibold text-gray-900 mb-2">Istanbul Port Community</h4>
                    <p class="text-gray-600 text-sm mb-4">Local maritime professionals in Istanbul area</p>
                    <div class="flex items-center justify-between">
                        <span class="text-sm text-gray-500">89 members</span>
                        <button class="bg-ocean-teal text-white px-4 py-2 rounded-lg text-sm hover:bg-ocean-teal/90 transition-colors">
                            Join Group
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- @END_COMPONENT: CommunityFeatures -->

    <!-- @COMPONENT: Footer [app information and links] -->
    <footer class="bg-navy-primary text-white py-12 px-4 sm:px-6 lg:px-8">
        <div class="max-w-7xl mx-auto">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <!-- Brand Section -->
                <div class="md:col-span-2">
                    <div class="flex items-center space-x-3 mb-4">
                        <div class="flex items-center justify-center w-10 h-10 bg-ocean-teal rounded-full">
                            <i class="fas fa-anchor text-white text-lg"></i>
                        </div>
                        <div>
                            <h4 class="text-xl font-bold">1234 Koi Hai?</h4>
                            <p class="text-gray-300 text-sm">Maritime Professional Discovery</p>
                        </div>
                    </div>
                    <p class="text-gray-300 mb-4 max-w-md">
                        Connecting maritime professionals worldwide. Discover, network, and grow your career in the maritime industry.
                    </p>
                    <div class="flex items-center space-x-4">
                        <span class="text-sm text-gray-300">Powered by QAAQ</span>
                        <span class="text-gray-500">|</span>
                        <span class="text-sm text-gray-300">WhatsApp Bot: +905363694997</span>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h5 class="font-semibold mb-4">Quick Links</h5>
                    <ul class="space-y-2 text-sm text-gray-300">
                        <li><a href="#" class="hover:text-light-teal transition-colors">Discover Professionals</a></li>
                        <li><a href="#" class="hover:text-light-teal transition-colors">Join Communities</a></li>
                        <li><a href="#" class="hover:text-light-teal transition-colors">QAAQ Technical Hub</a></li>
                        <li><a href="#" class="hover:text-light-teal transition-colors">Mobile App</a></li>
                    </ul>
                </div>

                <!-- Support -->
                <div>
                    <h5 class="font-semibold mb-4">Support</h5>
                    <ul class="space-y-2 text-sm text-gray-300">
                        <li><a href="#" class="hover:text-light-teal transition-colors">Help Center</a></li>
                        <li><a href="#" class="hover:text-light-teal transition-colors">Privacy Policy</a></li>
                        <li><a href="#" class="hover:text-light-teal transition-colors">Terms of Service</a></li>
                        <li><a href="#" class="hover:text-light-teal transition-colors">Contact Us</a></li>
                    </ul>
                </div>
            </div>

            <div class="border-t border-gray-600 mt-8 pt-8 text-center text-sm text-gray-400">
                <p>&copy; 2024 1234 Koi Hai? Maritime Discovery Platform. All rights reserved.</p>
            </div>
        </div>
    </footer>
    <!-- @END_COMPONENT: Footer -->

    <!-- @COMPONENT: MobileBottomNav [mobile navigation] -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 md:hidden">
        <div class="flex items-center justify-around py-3">
            <button class="flex flex-col items-center space-y-1 text-ocean-teal">
                <i class="fas fa-search text-lg"></i>
                <span class="text-xs">Discover</span>
            </button>
            <button class="flex flex-col items-center space-y-1 text-gray-400">
                <i class="fas fa-users text-lg"></i>
                <span class="text-xs">Community</span>
            </button>
            <button class="flex flex-col items-center space-y-1 text-gray-400">
                <i class="fas fa-question-circle text-lg"></i>
                <span class="text-xs">QAAQ</span>
            </button>
            <button class="flex flex-col items-center space-y-1 text-gray-400">
                <i class="fas fa-user text-lg"></i>
                <span class="text-xs">Profile</span>
            </button>
        </div>
    </nav>
    <!-- @END_COMPONENT: MobileBottomNav -->

    <script>
        // TODO: Implement business logic, API calls, and state management
        
        (function() {
            // Initialize filter panel visibility
            const filterPanel = document.getElementById('filterPanel');
            let isFilterVisible = true;

            // Distance slider functionality
            const slider = document.querySelector('.slider');
            if (slider) {
                slider.addEventListener('input', function() {
                    const value = this.value;
                    const display = document.querySelector('.font-medium.text-light-teal');
                    if (display) {
                        display.textContent = value + 'km';
                    }
                    // TODO: Update search results based on distance
                });
            }

            // Filter checkboxes
            const filterCheckboxes = document.querySelectorAll('input[type="checkbox"]');
            filterCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    // TODO: Apply filters to search results
                    console.log('Filter changed:', this.nextElementSibling.textContent, this.checked);
                });
            });

            // Main discovery button
            const discoveryButton = document.querySelector('button:has(.fa-search)');
            if (discoveryButton) {
                discoveryButton.addEventListener('click', function() {
                    // TODO: Trigger discovery search with current filters and location
                    console.log('Discovery search triggered');
                    
                    // Scroll to results section
                    const resultsSection = document.querySelector('section:has(.grid)');
                    if (resultsSection) {
                        resultsSection.scrollIntoView({ behavior: 'smooth' });
                    }
                });
            }

            // Connect buttons
            const connectButtons = document.querySelectorAll('button:contains("Connect")');
            connectButtons.forEach(button => {
                if (button.textContent.includes('Connect')) {
                    button.addEventListener('click', function() {
                        // TODO: Implement connection request functionality
                        this.textContent = 'Connecting...';
                        this.classList.add('opacity-50');
                        
                        setTimeout(() => {
                            this.textContent = 'Connected';
                            this.classList.remove('opacity-50');
                            this.classList.remove('bg-ocean-teal');
                            this.classList.add('bg-green-500');
                        }, 1500);
                    });
                }
            });

            // QAAQ AI input
            const qaaqInput = document.querySelector('input[placeholder*="Ask about engine"]');
            if (qaaqInput) {
                qaaqInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        // TODO: Submit question to QAAQ AI system
                        console.log('QAAQ question submitted:', this.value);
                    }
                });
            }

            // Mobile responsive adjustments
            function handleResize() {
                const filterPanel = document.getElementById('filterPanel');
                if (window.innerWidth < 768) {
                    // Hide filter panel on mobile, show as modal when needed
                    filterPanel.style.display = 'none';
                } else {
                    filterPanel.style.display = 'block';
                }
            }

            window.addEventListener('resize', handleResize);
            handleResize(); // Initial check

            // Animate cards on scroll
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('animate-fade-in');
                    }
                });
            }, observerOptions);

            // Observe all profile cards
            document.querySelectorAll('.card-hover').forEach(card => {
                observer.observe(card);
            });
        })();
    </script>
</body>
</html>