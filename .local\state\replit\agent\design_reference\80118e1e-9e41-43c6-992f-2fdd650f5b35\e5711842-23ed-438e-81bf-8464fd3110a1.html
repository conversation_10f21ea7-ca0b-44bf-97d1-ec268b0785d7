<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON>nect - Maritime Professional Discovery</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'navy': '#1e3a8a',
                        'ocean-teal': '#0f766e',
                        'light-teal': '#5eead4',
                        'maritime-gray': '#64748b'
                    },
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif']
                    }
                }
            }
        };
    </script>
</head>
<body class="font-inter bg-slate-50 text-slate-800">
    <!-- Navigation Header -->
    <!-- @COMPONENT: NavigationHeader -->
    <nav class="bg-white shadow-sm border-b border-slate-200 sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-3">
                    <div class="w-10 h-10 bg-gradient-to-br from-navy to-ocean-teal rounded-lg flex items-center justify-center">
                        <i class="fas fa-fish text-white text-lg"></i>
                    </div>
                    <div>
                        <h1 class="text-xl font-bold text-navy">Koi Connect</h1>
                        <p class="text-xs text-maritime-gray">Powered by QAAQ</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="text-maritime-gray hover:text-navy transition-colors">
                        <i class="fas fa-bell text-lg"></i>
                    </button>
                    <div class="w-8 h-8 bg-gradient-to-br from-ocean-teal to-light-teal rounded-full flex items-center justify-center">
                        <span class="text-white text-sm font-semibold" data-bind="user.initials">JD</span>
                    </div>
                </div>
            </div>
        </div>
    </nav>
    <!-- @END_COMPONENT: NavigationHeader -->

    <!-- Hero Section -->
    <!-- @COMPONENT: HeroSection -->
    <!-- A panoramic view of Hamburg port with container ships and cranes at sunset -->
    <section class="relative h-96 bg-cover bg-center" style="background-image: linear-gradient(rgba(30, 58, 138, 0.7), rgba(15, 118, 110, 0.7)), url('https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&h=800');">
        <div class="absolute inset-0 flex items-center justify-center">
            <div class="text-center text-white max-w-4xl px-4">
                <h1 class="text-5xl font-bold mb-4">Who's There?</h1>
                <p class="text-xl mb-8 opacity-90">Discover maritime professionals at your port, verify sailing experience, and connect with verified sailors worldwide</p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <button class="bg-white text-navy px-8 py-3 rounded-xl font-semibold hover:bg-slate-100 transition-colors shadow-lg">
                        <i class="fas fa-map-marker-alt mr-2"></i>
                        Discover Nearby
                    </button>
                    <button class="border-2 border-white text-white px-8 py-3 rounded-xl font-semibold hover:bg-white hover:text-navy transition-colors">
                        <i class="fas fa-file-upload mr-2"></i>
                        Upload CDC
                    </button>
                </div>
            </div>
        </div>
    </section>
    <!-- @END_COMPONENT: HeroSection -->

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <!-- Discovery Interface -->
        <!-- @COMPONENT: DiscoveryInterface -->
        <section class="mb-12">
            <div class="flex items-center justify-between mb-6">
                <h2 class="text-2xl font-bold text-navy">Maritime Professionals Nearby</h2>
                <div class="flex items-center space-x-3">
                    <select class="border border-slate-300 rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-ocean-teal focus:border-transparent">
                        <option>Hamburg Port</option>
                        <option>Rotterdam Port</option>
                        <option>Singapore Port</option>
                    </select>
                    <button class="bg-ocean-teal text-white px-4 py-2 rounded-lg text-sm hover:bg-ocean-teal/90 transition-colors">
                        <i class="fas fa-filter mr-1"></i>
                        Filter
                    </button>
                </div>
            </div>

            <!-- Professional Cards Grid -->
            <!-- @MAP: professionals.map(professional => ( -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                <!-- Professional Card 1 -->
                <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-6 hover:shadow-md transition-shadow" data-mock="true">
                    <!-- Professional sailor headshot with maritime uniform -->
                    <div class="flex items-start space-x-4 mb-4">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150" alt="Captain James Miller" class="w-16 h-16 rounded-full object-cover">
                        <div class="flex-1">
                            <h3 class="font-semibold text-lg text-navy" data-bind="professional.name">Captain James Miller</h3>
                            <p class="text-maritime-gray text-sm" data-bind="professional.rank">Master Mariner</p>
                            <p class="text-ocean-teal text-sm font-medium" data-bind="professional.vessel">MV Hamburg Express</p>
                        </div>
                        <div class="text-right">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <i class="fas fa-circle text-green-500 text-xs mr-1"></i>
                                Online
                            </span>
                        </div>
                    </div>
                    
                    <div class="space-y-2 mb-4">
                        <div class="flex justify-between text-sm">
                            <span class="text-maritime-gray">Experience:</span>
                            <span class="font-medium" data-bind="professional.experience">15 years</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-maritime-gray">QAAQ Rating:</span>
                            <div class="flex items-center">
                                <div class="flex text-yellow-400 text-xs">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                                <span class="ml-1 text-maritime-gray" data-bind="professional.qaaqRating">4.9</span>
                            </div>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-maritime-gray">Last Seen:</span>
                            <span class="font-medium" data-bind="professional.lastSeen">2 hours ago</span>
                        </div>
                    </div>

                    <div class="flex space-x-2">
                        <button class="flex-1 bg-ocean-teal text-white py-2 px-4 rounded-lg text-sm font-medium hover:bg-ocean-teal/90 transition-colors" data-event="click:connectWithProfessional">
                            <i class="fab fa-whatsapp mr-1"></i>
                            Connect
                        </button>
                        <button class="px-4 py-2 border border-slate-300 rounded-lg text-sm hover:bg-slate-50 transition-colors" data-event="click:viewProfile">
                            <i class="fas fa-user mr-1"></i>
                            Profile
                        </button>
                    </div>
                </div>

                <!-- Professional Card 2 -->
                <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-6 hover:shadow-md transition-shadow" data-mock="true">
                    <!-- Professional maritime engineer with safety helmet -->
                    <div class="flex items-start space-x-4 mb-4">
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=150&h=150" alt="Chief Engineer Rodriguez" class="w-16 h-16 rounded-full object-cover">
                        <div class="flex-1">
                            <h3 class="font-semibold text-lg text-navy" data-bind="professional.name">Chief Engineer Rodriguez</h3>
                            <p class="text-maritime-gray text-sm" data-bind="professional.rank">Chief Engineer</p>
                            <p class="text-ocean-teal text-sm font-medium" data-bind="professional.vessel">MV Atlantic Carrier</p>
                        </div>
                        <div class="text-right">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                                <i class="fas fa-circle text-yellow-500 text-xs mr-1"></i>
                                Away
                            </span>
                        </div>
                    </div>
                    
                    <div class="space-y-2 mb-4">
                        <div class="flex justify-between text-sm">
                            <span class="text-maritime-gray">Experience:</span>
                            <span class="font-medium" data-bind="professional.experience">12 years</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-maritime-gray">QAAQ Rating:</span>
                            <div class="flex items-center">
                                <div class="flex text-yellow-400 text-xs">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="far fa-star"></i>
                                </div>
                                <span class="ml-1 text-maritime-gray" data-bind="professional.qaaqRating">4.7</span>
                            </div>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-maritime-gray">Last Seen:</span>
                            <span class="font-medium" data-bind="professional.lastSeen">1 day ago</span>
                        </div>
                    </div>

                    <div class="flex space-x-2">
                        <button class="flex-1 bg-ocean-teal text-white py-2 px-4 rounded-lg text-sm font-medium hover:bg-ocean-teal/90 transition-colors" data-event="click:connectWithProfessional">
                            <i class="fab fa-whatsapp mr-1"></i>
                            Connect
                        </button>
                        <button class="px-4 py-2 border border-slate-300 rounded-lg text-sm hover:bg-slate-50 transition-colors" data-event="click:viewProfile">
                            <i class="fas fa-user mr-1"></i>
                            Profile
                        </button>
                    </div>
                </div>

                <!-- Professional Card 3 -->
                <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-6 hover:shadow-md transition-shadow" data-mock="true">
                    <!-- Professional female sailor with maritime background -->
                    <div class="flex items-start space-x-4 mb-4">
                        <img src="https://pixabay.com/get/g89e7d4e2a54a60f2a2460f38f6cf5c9b9c812bd0b84e14f78e6be943b594b5e7b7abd489f61df8a66805d4fb74d75ea408adede5fc06d1cc41d699b0777fd441_1280.jpg" alt="Second Officer Chen" class="w-16 h-16 rounded-full object-cover">
                        <div class="flex-1">
                            <h3 class="font-semibold text-lg text-navy" data-bind="professional.name">Second Officer Chen</h3>
                            <p class="text-maritime-gray text-sm" data-bind="professional.rank">Second Officer</p>
                            <p class="text-ocean-teal text-sm font-medium" data-bind="professional.vessel">MV Pacific Navigator</p>
                        </div>
                        <div class="text-right">
                            <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <i class="fas fa-circle text-green-500 text-xs mr-1"></i>
                                Online
                            </span>
                        </div>
                    </div>
                    
                    <div class="space-y-2 mb-4">
                        <div class="flex justify-between text-sm">
                            <span class="text-maritime-gray">Experience:</span>
                            <span class="font-medium" data-bind="professional.experience">8 years</span>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-maritime-gray">QAAQ Rating:</span>
                            <div class="flex items-center">
                                <div class="flex text-yellow-400 text-xs">
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                    <i class="fas fa-star"></i>
                                </div>
                                <span class="ml-1 text-maritime-gray" data-bind="professional.qaaqRating">4.8</span>
                            </div>
                        </div>
                        <div class="flex justify-between text-sm">
                            <span class="text-maritime-gray">Last Seen:</span>
                            <span class="font-medium" data-bind="professional.lastSeen">30 min ago</span>
                        </div>
                    </div>

                    <div class="flex space-x-2">
                        <button class="flex-1 bg-ocean-teal text-white py-2 px-4 rounded-lg text-sm font-medium hover:bg-ocean-teal/90 transition-colors" data-event="click:connectWithProfessional">
                            <i class="fab fa-whatsapp mr-1"></i>
                            Connect
                        </button>
                        <button class="px-4 py-2 border border-slate-300 rounded-lg text-sm hover:bg-slate-50 transition-colors" data-event="click:viewProfile">
                            <i class="fas fa-user mr-1"></i>
                            Profile
                        </button>
                    </div>
                </div>
            </div>
            <!-- @END_MAP )) -->
        </section>
        <!-- @END_COMPONENT: DiscoveryInterface -->

        <!-- CDC Upload Section -->
        <!-- @COMPONENT: CDCUploadSection -->
        <section class="mb-12 bg-white rounded-xl shadow-sm border border-slate-200 p-8">
            <div class="text-center mb-8">
                <div class="w-16 h-16 bg-gradient-to-br from-navy to-ocean-teal rounded-xl flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-file-medical text-white text-2xl"></i>
                </div>
                <h2 class="text-2xl font-bold text-navy mb-2">Verify Your Sailing Experience</h2>
                <p class="text-maritime-gray">Upload your CDC (Continuous Discharge Certificate) to automatically verify your sailing history and connect with vessels you've served on</p>
            </div>

            <div class="max-w-2xl mx-auto">
                <!-- Upload Area -->
                <div class="border-2 border-dashed border-slate-300 rounded-xl p-8 text-center hover:border-ocean-teal transition-colors cursor-pointer" data-event="click:openFileDialog">
                    <i class="fas fa-cloud-upload-alt text-4xl text-maritime-gray mb-4"></i>
                    <h3 class="text-lg font-semibold text-navy mb-2">Drop your CDC pages here</h3>
                    <p class="text-maritime-gray mb-4">or click to browse files</p>
                    <p class="text-sm text-maritime-gray">Supported formats: PDF, JPG, PNG (Max 10MB per file)</p>
                    
                    <!-- @FUNCTIONALITY: File upload with drag-and-drop support and OCR processing -->
                    <input type="file" accept=".pdf,.jpg,.jpeg,.png" multiple class="hidden" data-implementation="FileUpload component with OCR integration">
                </div>

                <!-- Processing Status -->
                <!-- @STATE: uploadStatus:string = 'idle' | 'uploading' | 'processing' | 'complete' -->
                <div class="mt-6 bg-blue-50 border border-blue-200 rounded-lg p-4" style="display: none;" data-bind="uploadStatus === 'processing'">
                    <div class="flex items-center">
                        <div class="animate-spin rounded-full h-5 w-5 border-b-2 border-ocean-teal mr-3"></div>
                        <div>
                            <h4 class="font-medium text-navy">Processing your CDC documents...</h4>
                            <p class="text-sm text-maritime-gray">Extracting vessel information, sign-on dates, and IMO numbers</p>
                        </div>
                    </div>
                </div>

                <!-- Extracted Information Preview -->
                <div class="mt-6 space-y-4" data-mock="true">
                    <h4 class="font-semibold text-navy">Preview Extracted Information:</h4>
                    
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                        <h5 class="font-medium text-green-800 mb-2">
                            <i class="fas fa-check-circle mr-2"></i>
                            Successfully Extracted
                        </h5>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                            <div>
                                <span class="text-maritime-gray">Vessel Name:</span>
                                <span class="font-medium ml-2" data-bind="extractedData.vesselName">MV Atlantic Pioneer</span>
                            </div>
                            <div>
                                <span class="text-maritime-gray">IMO Number:</span>
                                <span class="font-medium ml-2" data-bind="extractedData.imoNumber">9234567</span>
                            </div>
                            <div>
                                <span class="text-maritime-gray">Sign-on Date:</span>
                                <span class="font-medium ml-2" data-bind="extractedData.signOnDate">15/03/2023</span>
                            </div>
                            <div>
                                <span class="text-maritime-gray">Sign-off Date:</span>
                                <span class="font-medium ml-2" data-bind="extractedData.signOffDate">22/09/2023</span>
                            </div>
                            <div>
                                <span class="text-maritime-gray">Rank:</span>
                                <span class="font-medium ml-2" data-bind="extractedData.rank">Third Engineer</span>
                            </div>
                            <div>
                                <span class="text-maritime-gray">Duration:</span>
                                <span class="font-medium ml-2" data-bind="extractedData.duration">6 months, 7 days</span>
                            </div>
                        </div>
                    </div>

                    <div class="flex space-x-4">
                        <button class="flex-1 bg-ocean-teal text-white py-3 px-6 rounded-lg font-medium hover:bg-ocean-teal/90 transition-colors" data-event="click:confirmAndSave">
                            <i class="fas fa-save mr-2"></i>
                            Confirm & Add to Profile
                        </button>
                        <button class="px-6 py-3 border border-slate-300 rounded-lg text-maritime-gray hover:bg-slate-50 transition-colors" data-event="click:editManually">
                            <i class="fas fa-edit mr-2"></i>
                            Edit Manually
                        </button>
                    </div>
                </div>
            </div>
        </section>
        <!-- @END_COMPONENT: CDCUploadSection -->

        <!-- Vessel Experience Timeline -->
        <!-- @COMPONENT: VesselTimelineSection -->
        <section class="mb-12">
            <h2 class="text-2xl font-bold text-navy mb-6">Your Sailing Experience Timeline</h2>
            
            <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-6">
                <!-- Timeline Items -->
                <!-- @MAP: sailingHistory.map(experience => ( -->
                <div class="space-y-6">
                    <!-- Timeline Item 1 -->
                    <div class="flex space-x-4" data-mock="true">
                        <div class="flex flex-col items-center">
                            <div class="w-12 h-12 bg-gradient-to-br from-navy to-ocean-teal rounded-full flex items-center justify-center">
                                <i class="fas fa-ship text-white"></i>
                            </div>
                            <div class="w-px h-16 bg-slate-300 mt-4"></div>
                        </div>
                        <div class="flex-1 pb-8">
                            <div class="flex items-start justify-between">
                                <div>
                                    <h3 class="font-semibold text-navy text-lg" data-bind="experience.vesselName">MV Atlantic Pioneer</h3>
                                    <p class="text-maritime-gray text-sm" data-bind="experience.imoNumber">IMO: 9234567</p>
                                    <p class="text-ocean-teal font-medium" data-bind="experience.rank">Third Engineer</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm text-maritime-gray" data-bind="experience.duration">Mar 2023 - Sep 2023</p>
                                    <p class="text-xs text-maritime-gray" data-bind="experience.durationText">6 months, 7 days</p>
                                </div>
                            </div>
                            <div class="mt-3 flex items-center space-x-4">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-check mr-1"></i>
                                    CDC Verified
                                </span>
                                <button class="text-ocean-teal text-sm hover:underline" data-event="click:viewCrewmates">
                                    <i class="fas fa-users mr-1"></i>
                                    View 12 Crewmates
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Timeline Item 2 -->
                    <div class="flex space-x-4" data-mock="true">
                        <div class="flex flex-col items-center">
                            <div class="w-12 h-12 bg-gradient-to-br from-navy to-ocean-teal rounded-full flex items-center justify-center">
                                <i class="fas fa-ship text-white"></i>
                            </div>
                            <div class="w-px h-16 bg-slate-300 mt-4"></div>
                        </div>
                        <div class="flex-1 pb-8">
                            <div class="flex items-start justify-between">
                                <div>
                                    <h3 class="font-semibold text-navy text-lg" data-bind="experience.vesselName">MV Northern Star</h3>
                                    <p class="text-maritime-gray text-sm" data-bind="experience.imoNumber">IMO: 9187432</p>
                                    <p class="text-ocean-teal font-medium" data-bind="experience.rank">Fourth Engineer</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm text-maritime-gray" data-bind="experience.duration">Aug 2022 - Jan 2023</p>
                                    <p class="text-xs text-maritime-gray" data-bind="experience.durationText">5 months, 12 days</p>
                                </div>
                            </div>
                            <div class="mt-3 flex items-center space-x-4">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-check mr-1"></i>
                                    CDC Verified
                                </span>
                                <button class="text-ocean-teal text-sm hover:underline" data-event="click:viewCrewmates">
                                    <i class="fas fa-users mr-1"></i>
                                    View 8 Crewmates
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Timeline Item 3 -->
                    <div class="flex space-x-4" data-mock="true">
                        <div class="flex flex-col items-center">
                            <div class="w-12 h-12 bg-gradient-to-br from-navy to-ocean-teal rounded-full flex items-center justify-center">
                                <i class="fas fa-ship text-white"></i>
                            </div>
                        </div>
                        <div class="flex-1">
                            <div class="flex items-start justify-between">
                                <div>
                                    <h3 class="font-semibold text-navy text-lg" data-bind="experience.vesselName">MV Horizon Explorer</h3>
                                    <p class="text-maritime-gray text-sm" data-bind="experience.imoNumber">IMO: 9156789</p>
                                    <p class="text-ocean-teal font-medium" data-bind="experience.rank">Engine Cadet</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm text-maritime-gray" data-bind="experience.duration">Feb 2022 - Jul 2022</p>
                                    <p class="text-xs text-maritime-gray" data-bind="experience.durationText">5 months, 3 days</p>
                                </div>
                            </div>
                            <div class="mt-3 flex items-center space-x-4">
                                <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                    <i class="fas fa-check mr-1"></i>
                                    CDC Verified
                                </span>
                                <button class="text-ocean-teal text-sm hover:underline" data-event="click:viewCrewmates">
                                    <i class="fas fa-users mr-1"></i>
                                    View 15 Crewmates
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- @END_MAP )) -->

                <div class="text-center pt-6 border-t border-slate-200">
                    <button class="text-ocean-teal font-medium hover:underline" data-event="click:addManualEntry">
                        <i class="fas fa-plus mr-2"></i>
                        Add Manual Entry
                    </button>
                </div>
            </div>
        </section>
        <!-- @END_COMPONENT: VesselTimelineSection -->

        <!-- WhatsApp-Style Connection Interface -->
        <!-- @COMPONENT: ConnectionInterface -->
        <section class="mb-12">
            <h2 class="text-2xl font-bold text-navy mb-6">Recent Connections</h2>
            
            <div class="bg-white rounded-xl shadow-sm border border-slate-200 overflow-hidden">
                <!-- Connection List -->
                <!-- @MAP: connections.map(connection => ( -->
                <div class="divide-y divide-slate-200">
                    <!-- Connection Item 1 -->
                    <div class="p-4 hover:bg-slate-50 cursor-pointer transition-colors" data-mock="true" data-event="click:openChat">
                        <div class="flex items-center space-x-4">
                            <!-- Professional sailor with maritime uniform -->
                            <div class="relative">
                                <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&h=100" alt="Captain Miller" class="w-12 h-12 rounded-full object-cover">
                                <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-white rounded-full"></div>
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between">
                                    <h3 class="font-semibold text-navy truncate" data-bind="connection.name">Captain James Miller</h3>
                                    <span class="text-xs text-maritime-gray" data-bind="connection.lastMessage.time">2:34 PM</span>
                                </div>
                                <p class="text-sm text-maritime-gray truncate" data-bind="connection.lastMessage.text">Thanks for the MAN B&W troubleshooting tip! Really helped us out...</p>
                                <div class="flex items-center mt-1 space-x-2">
                                    <span class="text-xs text-ocean-teal font-medium" data-bind="connection.commonVessel">MV Hamburg Express</span>
                                    <span class="text-xs text-maritime-gray">•</span>
                                    <span class="text-xs text-maritime-gray" data-bind="connection.qaaqRating">QAAQ: 4.9★</span>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="w-2 h-2 bg-ocean-teal rounded-full" data-bind="connection.unreadCount > 0"></span>
                                <i class="fab fa-whatsapp text-green-500 text-lg"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Connection Item 2 -->
                    <div class="p-4 hover:bg-slate-50 cursor-pointer transition-colors" data-mock="true" data-event="click:openChat">
                        <div class="flex items-center space-x-4">
                            <!-- Professional maritime engineer -->
                            <div class="relative">
                                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&h=100" alt="Chief Engineer Rodriguez" class="w-12 h-12 rounded-full object-cover">
                                <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-yellow-500 border-2 border-white rounded-full"></div>
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between">
                                    <h3 class="font-semibold text-navy truncate" data-bind="connection.name">Chief Engineer Rodriguez</h3>
                                    <span class="text-xs text-maritime-gray" data-bind="connection.lastMessage.time">Yesterday</span>
                                </div>
                                <p class="text-sm text-maritime-gray truncate" data-bind="connection.lastMessage.text">The Alfa Laval purifier issue you mentioned - I had the same problem...</p>
                                <div class="flex items-center mt-1 space-x-2">
                                    <span class="text-xs text-ocean-teal font-medium" data-bind="connection.commonVessel">MV Atlantic Carrier</span>
                                    <span class="text-xs text-maritime-gray">•</span>
                                    <span class="text-xs text-maritime-gray" data-bind="connection.qaaqRating">QAAQ: 4.7★</span>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <i class="fab fa-whatsapp text-green-500 text-lg"></i>
                            </div>
                        </div>
                    </div>

                    <!-- Connection Item 3 -->
                    <div class="p-4 hover:bg-slate-50 cursor-pointer transition-colors" data-mock="true" data-event="click:openChat">
                        <div class="flex items-center space-x-4">
                            <!-- Professional female sailor -->
                            <div class="relative">
                                <img src="https://pixabay.com/get/g3e22dc81b8fa5061f828bc7a338c4802725c59e6c649328ae86c8d56586501ef2c71f0da680d2c51fa9d210be59ee8b8f519e76f73e16ed3bea571c0daf11267_1280.jpg" alt="Second Officer Chen" class="w-12 h-12 rounded-full object-cover">
                                <div class="absolute -bottom-1 -right-1 w-4 h-4 bg-green-500 border-2 border-white rounded-full"></div>
                            </div>
                            <div class="flex-1 min-w-0">
                                <div class="flex items-center justify-between">
                                    <h3 class="font-semibold text-navy truncate" data-bind="connection.name">Second Officer Chen</h3>
                                    <span class="text-xs text-maritime-gray" data-bind="connection.lastMessage.time">10:15 AM</span>
                                </div>
                                <p class="text-sm text-maritime-gray truncate" data-bind="connection.lastMessage.text">Great meeting you at Hamburg port! Let's stay in touch...</p>
                                <div class="flex items-center mt-1 space-x-2">
                                    <span class="text-xs text-ocean-teal font-medium" data-bind="connection.commonVessel">MV Pacific Navigator</span>
                                    <span class="text-xs text-maritime-gray">•</span>
                                    <span class="text-xs text-maritime-gray" data-bind="connection.qaaqRating">QAAQ: 4.8★</span>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <span class="w-2 h-2 bg-ocean-teal rounded-full" data-bind="connection.unreadCount > 0"></span>
                                <i class="fab fa-whatsapp text-green-500 text-lg"></i>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- @END_MAP )) -->
            </div>
        </section>
        <!-- @END_COMPONENT: ConnectionInterface -->

        <!-- QAAQ Integration Section -->
        <!-- @COMPONENT: QAAQIntegrationSection -->
        <section class="mb-12 bg-gradient-to-br from-navy to-ocean-teal rounded-xl text-white p-8">
            <div class="flex items-center justify-between">
                <div>
                    <h2 class="text-2xl font-bold mb-2">Powered by QAAQ</h2>
                    <p class="text-blue-100 mb-4">Your maritime expertise and connections, enhanced by the world's largest maritime engineering knowledge base</p>
                    <div class="flex items-center space-x-6 text-sm">
                        <div class="flex items-center">
                            <i class="fas fa-users mr-2"></i>
                            <span>1,203+ Maritime Engineers</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-question-circle mr-2"></i>
                            <span>2,847+ Questions Answered</span>
                        </div>
                        <div class="flex items-center">
                            <i class="fas fa-cogs mr-2"></i>
                            <span>156+ Machine Types</span>
                        </div>
                    </div>
                </div>
                <div class="text-right">
                    <!-- Modern maritime control room with digital displays -->
                    <img src="https://images.unsplash.com/photo-1558618666-fcd25c85cd64?ixlib=rb-4.0.3&auto=format&fit=crop&w=300&h=200" alt="Maritime control room" class="rounded-lg shadow-lg w-48 h-32 object-cover">
                </div>
            </div>
            <div class="flex space-x-4 mt-6">
                <button class="bg-white text-navy px-6 py-3 rounded-lg font-semibold hover:bg-blue-50 transition-colors">
                    <i class="fas fa-external-link-alt mr-2"></i>
                    Visit QAAQ Platform
                </button>
                <button class="border-2 border-white text-white px-6 py-3 rounded-lg font-semibold hover:bg-white hover:text-navy transition-colors">
                    <i class="fab fa-whatsapp mr-2"></i>
                    Try QAAQ Bot
                </button>
            </div>
        </section>
        <!-- @END_COMPONENT: QAAQIntegrationSection -->
    </div>

    <!-- Footer -->
    <!-- @COMPONENT: Footer -->
    <footer class="bg-navy text-white py-12">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <div class="flex items-center space-x-3 mb-4">
                        <div class="w-10 h-10 bg-gradient-to-br from-ocean-teal to-light-teal rounded-lg flex items-center justify-center">
                            <i class="fas fa-fish text-white text-lg"></i>
                        </div>
                        <div>
                            <h3 class="text-xl font-bold">Koi Connect</h3>
                            <p class="text-xs text-blue-200">Maritime Professional Discovery</p>
                        </div>
                    </div>
                    <p class="text-blue-200 text-sm">Connecting maritime professionals worldwide through verified sailing experience and shared vessel history.</p>
                </div>
                
                <div>
                    <h4 class="font-semibold mb-4">Features</h4>
                    <ul class="space-y-2 text-sm text-blue-200">
                        <li><a href="#" class="hover:text-white transition-colors">Professional Discovery</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">CDC Verification</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Vessel Timeline</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">WhatsApp Integration</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="font-semibold mb-4">QAAQ Platform</h4>
                    <ul class="space-y-2 text-sm text-blue-200">
                        <li><a href="#" class="hover:text-white transition-colors">Maritime Q&A</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Technical Support</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Knowledge Base</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">WhatsApp Bot</a></li>
                    </ul>
                </div>
                
                <div>
                    <h4 class="font-semibold mb-4">Connect</h4>
                    <ul class="space-y-2 text-sm text-blue-200">
                        <li><a href="#" class="hover:text-white transition-colors">Privacy Policy</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Terms of Service</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Support</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Contact Us</a></li>
                    </ul>
                </div>
            </div>
            
            <div class="border-t border-blue-800 mt-8 pt-8 text-center text-blue-200 text-sm">
                <p>&copy; 2024 Koi Connect. Powered by QAAQ Maritime Platform. All rights reserved.</p>
            </div>
        </div>
    </footer>
    <!-- @END_COMPONENT: Footer -->

    <!-- JavaScript for Interactions -->
    <script>
        (function() {
            // TODO: Implement business logic, API calls, and state management
            
            // Mock interactivity for demonstration
            document.addEventListener('DOMContentLoaded', function() {
                // File upload simulation
                const uploadArea = document.querySelector('[data-event="click:openFileDialog"]');
                if (uploadArea) {
                    uploadArea.addEventListener('click', function() {
                        console.log('File dialog would open here');
                        // TODO: Implement file upload with OCR processing
                    });
                }

                // Connection buttons
                const connectButtons = document.querySelectorAll('[data-event="click:connectWithProfessional"]');
                connectButtons.forEach(button => {
                    button.addEventListener('click', function() {
                        console.log('WhatsApp connection would be initiated here');
                        // TODO: Implement WhatsApp integration
                    });
                });

                // Profile view buttons
                const profileButtons = document.querySelectorAll('[data-event="click:viewProfile"]');
                profileButtons.forEach(button => {
                    button.addEventListener('click', function() {
                        console.log('Profile modal would open here');
                        // TODO: Implement profile view modal
                    });
                });

                // Chat opening
                const chatItems = document.querySelectorAll('[data-event="click:openChat"]');
                chatItems.forEach(item => {
                    item.addEventListener('click', function() {
                        console.log('Chat interface would open here');
                        // TODO: Implement chat interface
                    });
                });
            });
        })();
    </script>
</body>
</html>