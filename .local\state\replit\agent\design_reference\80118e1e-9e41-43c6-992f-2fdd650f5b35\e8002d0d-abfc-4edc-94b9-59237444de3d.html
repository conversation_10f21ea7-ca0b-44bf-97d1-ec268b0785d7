<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Koi - Maritime Connections</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'navy': '#1E3A8A',
                        'ocean-teal': '#0D9488',
                        'light-ocean': '#E6FFFA',
                        'maritime-gray': '#64748B'
                    },
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif']
                    }
                }
            }
        };
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-light-ocean font-inter">

    <!-- Mobile App Container -->
    <div class="max-w-md mx-auto bg-white min-h-screen relative overflow-hidden">
        
        <!-- Header -->
        <div class="bg-navy text-white px-4 py-3 flex items-center justify-between">
            <div class="flex items-center space-x-3">
                <div class="w-8 h-8 bg-ocean-teal rounded-full flex items-center justify-center">
                    <i class="fas fa-fish text-white text-sm"></i>
                </div>
                <h1 class="text-xl font-semibold">Koi</h1>
            </div>
            <div class="flex items-center space-x-3">
                <button class="text-white hover:text-ocean-teal">
                    <i class="fas fa-search text-lg"></i>
                </button>
                <button class="text-white hover:text-ocean-teal">
                    <i class="fas fa-bell text-lg"></i>
                </button>
            </div>
        </div>

        <!-- Main Discovery Interface -->
        <div class="relative h-screen pb-20">
            
            <!-- Location & Distance Controls -->
            <div class="bg-white shadow-sm px-4 py-3 border-b border-gray-200">
                <div class="flex items-center justify-between mb-3">
                    <div class="flex items-center space-x-2">
                        <i class="fas fa-map-marker-alt text-ocean-teal"></i>
                        <span class="text-sm font-medium text-gray-700">Port of Istanbul</span>
                        <button class="text-ocean-teal text-sm font-medium">Change</button>
                    </div>
                    <button class="bg-ocean-teal text-white px-3 py-1 rounded-full text-sm font-medium">
                        <i class="fas fa-compass mr-1"></i>
                        Live
                    </button>
                </div>
                
                <!-- Distance Slider -->
                <div class="flex items-center space-x-3">
                    <span class="text-sm text-gray-600 w-12">1km</span>
                    <div class="flex-1 relative">
                        <input type="range" min="1" max="50" value="25" class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider">
                        <style>
                            .slider::-webkit-slider-thumb {
                                appearance: none;
                                height: 20px;
                                width: 20px;
                                border-radius: 50%;
                                background: #0D9488;
                                cursor: pointer;
                            }
                        </style>
                    </div>
                    <span class="text-sm text-gray-600 w-12">50km</span>
                    <span class="text-sm font-semibold text-ocean-teal w-12">25km</span>
                </div>
            </div>

            <!-- Who's There Header -->
            <div class="bg-gradient-to-r from-navy to-ocean-teal text-white px-4 py-4">
                <h2 class="text-2xl font-bold mb-1">Who's There?</h2>
                <p class="text-sm opacity-90">12 sailors within 25km • 3 at your port</p>
            </div>

            <!-- Sailor Discovery Cards -->
            <div class="flex-1 relative overflow-hidden">
                
                <!-- Card Stack -->
                <div class="absolute inset-0 p-4">
                    
                    <!-- Top Card -->
                    <div class="bg-white rounded-2xl shadow-xl border border-gray-100 h-full relative overflow-hidden transform transition-transform duration-300">
                        <!-- Profile Image -->
                        <!-- A professional sailor in naval uniform standing confidently on a ship deck -->
                        <div class="h-64 bg-cover bg-center relative" style="background-image: url('https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&h=600');">
                            <div class="absolute top-4 right-4 bg-green-500 text-white px-2 py-1 rounded-full text-xs font-medium">
                                <i class="fas fa-circle text-xs mr-1"></i>Online
                            </div>
                            <div class="absolute bottom-4 left-4 right-4">
                                <div class="bg-black bg-opacity-50 backdrop-blur-sm rounded-lg p-3 text-white">
                                    <h3 class="text-xl font-bold">Captain James Rodriguez</h3>
                                    <p class="text-sm opacity-90">Master Mariner • MSC Vessels</p>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Card Content -->
                        <div class="p-4 space-y-4">
                            <!-- Distance & Location -->
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-2">
                                    <i class="fas fa-map-marker-alt text-ocean-teal"></i>
                                    <span class="text-sm text-gray-600">2.3km away</span>
                                </div>
                                <div class="flex items-center space-x-2">
                                    <i class="fas fa-ship text-navy"></i>
                                    <span class="text-sm text-gray-600">Container Ship</span>
                                </div>
                            </div>

                            <!-- Vessel Info -->
                            <div class="bg-light-ocean rounded-lg p-3">
                                <div class="flex items-center justify-between mb-2">
                                    <span class="text-sm font-medium text-gray-700">Current Vessel</span>
                                    <span class="text-xs text-ocean-teal font-medium">IMO: 9234567</span>
                                </div>
                                <h4 class="font-semibold text-gray-800">MSC Istanbul</h4>
                                <p class="text-sm text-gray-600">Berthed at Terminal 3 • Next port: Piraeus</p>
                            </div>

                            <!-- Experience & Certificates -->
                            <div class="grid grid-cols-2 gap-3">
                                <div class="text-center">
                                    <div class="text-lg font-bold text-navy">15+</div>
                                    <div class="text-xs text-gray-600">Years Experience</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-lg font-bold text-ocean-teal">COC I/II</div>
                                    <div class="text-xs text-gray-600">Certificate</div>
                                </div>
                            </div>

                            <!-- Shared Connections -->
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-users text-maritime-gray text-sm"></i>
                                <span class="text-sm text-gray-600">2 mutual connections</span>
                                <div class="flex -space-x-2">
                                    <!-- Small profile photos of mutual connections -->
                                    <div class="w-6 h-6 bg-gray-300 rounded-full border-2 border-white"></div>
                                    <div class="w-6 h-6 bg-gray-400 rounded-full border-2 border-white"></div>
                                </div>
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="absolute bottom-4 left-4 right-4 flex space-x-3">
                            <button class="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 py-3 rounded-xl font-medium transition-colors">
                                <i class="fas fa-times mr-2"></i>Pass
                            </button>
                            <button class="flex-1 bg-ocean-teal hover:bg-opacity-90 text-white py-3 rounded-xl font-medium transition-colors">
                                <i class="fas fa-hand-paper mr-2"></i>Say Hello
                            </button>
                        </div>
                    </div>

                    <!-- Second Card (Partially Visible) -->
                    <div class="absolute inset-0 bg-white rounded-2xl shadow-lg border border-gray-100 transform scale-95 -translate-y-4 z-10">
                        <!-- A merchant marine officer reviewing documents at a ship's navigation table -->
                        <div class="h-64 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1566576912321-d58ddd7a6088?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&h=600');">
                            <div class="absolute bottom-4 left-4 right-4">
                                <div class="bg-black bg-opacity-50 backdrop-blur-sm rounded-lg p-3 text-white">
                                    <h3 class="text-lg font-bold">Chief Eng. Maria Santos</h3>
                                    <p class="text-sm opacity-90">Chief Engineer • Bulk Carrier</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Third Card (Background) -->
                    <div class="absolute inset-0 bg-white rounded-2xl shadow border border-gray-100 transform scale-90 -translate-y-8 z-0">
                        <!-- Maritime crew members working together on deck equipment -->
                        <div class="h-64 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1568605117036-5fe5e7bab0b7?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=800&h=600');"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bottom Navigation -->
        <div class="absolute bottom-0 left-0 right-0 bg-white border-t border-gray-200">
            <div class="flex items-center justify-around py-2">
                <button class="flex flex-col items-center py-2 px-4 text-ocean-teal">
                    <i class="fas fa-compass text-xl mb-1"></i>
                    <span class="text-xs font-medium">Discover</span>
                </button>
                <button class="flex flex-col items-center py-2 px-4 text-maritime-gray hover:text-ocean-teal">
                    <i class="fas fa-comments text-xl mb-1"></i>
                    <span class="text-xs">Chats</span>
                    <div class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">3</div>
                </button>
                <button class="flex flex-col items-center py-2 px-4 text-maritime-gray hover:text-ocean-teal">
                    <i class="fas fa-map text-xl mb-1"></i>
                    <span class="text-xs">Map</span>
                </button>
                <button class="flex flex-col items-center py-2 px-4 text-maritime-gray hover:text-ocean-teal">
                    <i class="fas fa-user text-xl mb-1"></i>
                    <span class="text-xs">Profile</span>
                </button>
            </div>
        </div>

        <!-- Chat Interface Overlay (Hidden by default) -->
        <div class="absolute inset-0 bg-white transform translate-x-full transition-transform duration-300" id="chatOverlay">
            <!-- WhatsApp-style Chat Header -->
            <div class="bg-navy text-white px-4 py-3 flex items-center space-x-3">
                <button class="text-white hover:text-ocean-teal" onclick="closeChatOverlay()">
                    <i class="fas fa-arrow-left text-lg"></i>
                </button>
                <div class="w-10 h-10 bg-ocean-teal rounded-full flex items-center justify-center">
                    <i class="fas fa-user text-white"></i>
                </div>
                <div class="flex-1">
                    <h3 class="font-semibold">Captain James Rodriguez</h3>
                    <p class="text-sm opacity-75">Online • MSC Istanbul</p>
                </div>
                <button class="text-white hover:text-ocean-teal">
                    <i class="fas fa-phone text-lg"></i>
                </button>
            </div>

            <!-- Chat Messages -->
            <div class="flex-1 p-4 space-y-3 bg-gray-50 min-h-screen pb-20">
                <div class="flex justify-center">
                    <span class="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-xs">Today</span>
                </div>
                
                <!-- Incoming Message -->
                <div class="flex space-x-2">
                    <div class="w-8 h-8 bg-ocean-teal rounded-full flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-user text-white text-sm"></i>
                    </div>
                    <div class="bg-white rounded-2xl rounded-tl-none px-4 py-2 max-w-xs shadow-sm">
                        <p class="text-sm">Hey! I see you're also at Istanbul port. Are you familiar with the new MARPOL regulations?</p>
                        <span class="text-xs text-gray-500 mt-1 block">14:32</span>
                    </div>
                </div>

                <!-- Outgoing Message -->
                <div class="flex justify-end">
                    <div class="bg-ocean-teal text-white rounded-2xl rounded-tr-none px-4 py-2 max-w-xs">
                        <p class="text-sm">Yes! Just completed the training last month. Happy to share notes if you need them.</p>
                        <span class="text-xs text-ocean-100 mt-1 block">14:35</span>
                    </div>
                </div>

                <!-- System Message -->
                <div class="flex justify-center">
                    <div class="bg-blue-100 text-blue-800 px-3 py-2 rounded-lg text-xs">
                        <i class="fas fa-shield-alt mr-1"></i>
                        This conversation is verified through QAAQ maritime database
                    </div>
                </div>
            </div>

            <!-- Chat Input -->
            <div class="absolute bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4">
                <div class="flex items-center space-x-3">
                    <button class="text-maritime-gray hover:text-ocean-teal">
                        <i class="fas fa-paperclip text-lg"></i>
                    </button>
                    <div class="flex-1 bg-gray-100 rounded-full px-4 py-2">
                        <input type="text" placeholder="Type a message..." class="w-full bg-transparent outline-none text-sm">
                    </div>
                    <button class="bg-ocean-teal text-white w-10 h-10 rounded-full flex items-center justify-center">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Profile Management Overlay -->
        <div class="absolute inset-0 bg-white transform translate-x-full transition-transform duration-300" id="profileOverlay">
            <!-- Profile Header -->
            <div class="bg-navy text-white px-4 py-3 flex items-center space-x-3">
                <button class="text-white hover:text-ocean-teal" onclick="closeProfileOverlay()">
                    <i class="fas fa-arrow-left text-lg"></i>
                </button>
                <h3 class="text-lg font-semibold">My Profile</h3>
            </div>

            <!-- Profile Content -->
            <div class="p-4 space-y-6 pb-20">
                
                <!-- Profile Picture & Basic Info -->
                <div class="text-center space-y-4">
                    <!-- A professional headshot of a maritime officer in uniform -->
                    <div class="w-24 h-24 bg-cover bg-center rounded-full mx-auto border-4 border-ocean-teal" style="background-image: url('https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=400&h=400');"></div>
                    <div>
                        <h3 class="text-xl font-bold text-gray-800">Piyush Gupta</h3>
                        <p class="text-maritime-gray">Marine Engineer • 21+ years</p>
                        <div class="flex items-center justify-center space-x-2 mt-2">
                            <i class="fas fa-map-marker-alt text-ocean-teal text-sm"></i>
                            <span class="text-sm text-gray-600">Istanbul, Turkey</span>
                        </div>
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="grid grid-cols-3 gap-4">
                    <div class="text-center bg-light-ocean rounded-lg p-3">
                        <div class="text-lg font-bold text-navy">157</div>
                        <div class="text-xs text-gray-600">Connections</div>
                    </div>
                    <div class="text-center bg-light-ocean rounded-lg p-3">
                        <div class="text-lg font-bold text-ocean-teal">95%</div>
                        <div class="text-xs text-gray-600">Profile Complete</div>
                    </div>
                    <div class="text-center bg-light-ocean rounded-lg p-3">
                        <div class="text-lg font-bold text-navy">MEO I</div>
                        <div class="text-xs text-gray-600">Highest Cert</div>
                    </div>
                </div>

                <!-- Marine Documents Section -->
                <div class="space-y-4">
                    <div class="flex items-center justify-between">
                        <h4 class="text-lg font-semibold text-gray-800">Marine Documents</h4>
                        <button class="text-ocean-teal text-sm font-medium">Manage All</button>
                    </div>
                    
                    <!-- Document Categories -->
                    <div class="grid grid-cols-2 gap-3">
                        <div class="bg-white border border-gray-200 rounded-lg p-3">
                            <!-- Professional documents folder with maritime certificates -->
                            <div class="w-full h-16 bg-cover bg-center rounded mb-2" style="background-image: url('https://images.unsplash.com/photo-1568667256549-094345857637?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=400&h=200');"></div>
                            <h5 class="font-medium text-sm text-gray-800">Certificates</h5>
                            <p class="text-xs text-gray-600">MEO I, STCW, COC</p>
                            <div class="flex items-center mt-1">
                                <div class="w-2 h-2 bg-green-500 rounded-full mr-2"></div>
                                <span class="text-xs text-green-600">All Valid</span>
                            </div>
                        </div>
                        
                        <div class="bg-white border border-gray-200 rounded-lg p-3">
                            <!-- Passport and identification documents -->
                            <div class="w-full h-16 bg-cover bg-center rounded mb-2" style="background-image: url('https://images.unsplash.com/photo-1589578527966-fdac0f44566c?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=400&h=200');"></div>
                            <h5 class="font-medium text-sm text-gray-800">Identity Docs</h5>
                            <p class="text-xs text-gray-600">Passport, CDC, Visa</p>
                            <div class="flex items-center mt-1">
                                <div class="w-2 h-2 bg-yellow-500 rounded-full mr-2"></div>
                                <span class="text-xs text-yellow-600">Renewal Due</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Resume Builder -->
                <div class="bg-gradient-to-r from-navy to-ocean-teal rounded-lg p-4 text-white">
                    <div class="flex items-center space-x-3">
                        <i class="fas fa-file-alt text-2xl"></i>
                        <div class="flex-1">
                            <h4 class="font-semibold">Resume Builder</h4>
                            <p class="text-sm opacity-90">Generate professional maritime resumes</p>
                        </div>
                        <button class="bg-white bg-opacity-20 hover:bg-opacity-30 px-3 py-1 rounded-full text-sm font-medium transition-colors">
                            Create
                        </button>
                    </div>
                </div>

                <!-- Share Profile -->
                <div class="space-y-3">
                    <h4 class="text-lg font-semibold text-gray-800">Share Profile</h4>
                    <div class="bg-light-ocean rounded-lg p-4">
                        <div class="flex items-center justify-between">
                            <div>
                                <p class="text-sm font-medium text-gray-800">Profile Link</p>
                                <p class="text-xs text-gray-600">koi.app/profile/piyush-gupta</p>
                            </div>
                            <button class="bg-ocean-teal text-white px-3 py-2 rounded-lg text-sm font-medium">
                                <i class="fas fa-copy mr-1"></i>Copy
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Settings -->
                <div class="space-y-3">
                    <h4 class="text-lg font-semibold text-gray-800">Settings</h4>
                    <div class="space-y-2">
                        <button class="w-full text-left p-3 bg-white rounded-lg border border-gray-200 hover:bg-gray-50">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <i class="fas fa-shield-alt text-maritime-gray"></i>
                                    <span class="text-sm font-medium">Privacy Settings</span>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>
                        </button>
                        
                        <button class="w-full text-left p-3 bg-white rounded-lg border border-gray-200 hover:bg-gray-50">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <i class="fas fa-bell text-maritime-gray"></i>
                                    <span class="text-sm font-medium">Notifications</span>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>
                        </button>
                        
                        <button class="w-full text-left p-3 bg-white rounded-lg border border-gray-200 hover:bg-gray-50">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center space-x-3">
                                    <i class="fas fa-link text-maritime-gray"></i>
                                    <span class="text-sm font-medium">Connect to QAAQ</span>
                                </div>
                                <i class="fas fa-chevron-right text-gray-400"></i>
                            </div>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Map View Overlay -->
        <div class="absolute inset-0 bg-white transform translate-x-full transition-transform duration-300" id="mapOverlay">
            <!-- Map Header -->
            <div class="bg-navy text-white px-4 py-3 flex items-center space-x-3">
                <button class="text-white hover:text-ocean-teal" onclick="closeMapOverlay()">
                    <i class="fas fa-arrow-left text-lg"></i>
                </button>
                <h3 class="text-lg font-semibold">Nearby Sailors</h3>
                <div class="ml-auto flex items-center space-x-2">
                    <button class="bg-ocean-teal px-3 py-1 rounded-full text-sm">
                        <i class="fas fa-layer-group mr-1"></i>Ports
                    </button>
                </div>
            </div>

            <!-- Map Container -->
            <div class="relative h-full bg-blue-50">
                <!-- Simulated map background with port layout -->
                <div class="absolute inset-0 bg-cover bg-center" style="background-image: url('https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=1200&h=800');">
                    <div class="absolute inset-0 bg-blue-900 bg-opacity-30"></div>
                </div>

                <!-- Map Markers -->
                <div class="absolute top-32 left-16">
                    <div class="relative">
                        <div class="w-12 h-12 bg-ocean-teal rounded-full flex items-center justify-center shadow-lg border-4 border-white">
                            <i class="fas fa-user text-white"></i>
                        </div>
                        <div class="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-white px-2 py-1 rounded shadow text-xs font-medium whitespace-nowrap">
                            Captain James
                        </div>
                        <div class="absolute top-12 left-1/2 transform -translate-x-1/2 bg-white px-2 py-1 rounded shadow text-xs text-gray-600">
                            2.3km
                        </div>
                    </div>
                </div>

                <div class="absolute top-48 right-20">
                    <div class="relative">
                        <div class="w-10 h-10 bg-navy rounded-full flex items-center justify-center shadow-lg border-3 border-white">
                            <i class="fas fa-user text-white text-sm"></i>
                        </div>
                        <div class="absolute -top-6 left-1/2 transform -translate-x-1/2 bg-white px-2 py-1 rounded shadow text-xs font-medium whitespace-nowrap">
                            Chief Eng. Maria
                        </div>
                    </div>
                </div>

                <div class="absolute bottom-32 left-1/3">
                    <div class="relative">
                        <div class="w-10 h-10 bg-maritime-gray rounded-full flex items-center justify-center shadow-lg border-3 border-white">
                            <i class="fas fa-user text-white text-sm"></i>
                        </div>
                        <div class="absolute -top-6 left-1/2 transform -translate-x-1/2 bg-white px-2 py-1 rounded shadow text-xs font-medium whitespace-nowrap">
                            2nd Eng. Alex
                        </div>
                    </div>
                </div>

                <!-- Current Location -->
                <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                    <div class="relative">
                        <div class="w-4 h-4 bg-blue-500 rounded-full border-4 border-white shadow-lg"></div>
                        <div class="absolute w-12 h-12 border-2 border-blue-500 border-opacity-30 rounded-full -top-4 -left-4 animate-ping"></div>
                    </div>
                </div>

                <!-- Map Controls -->
                <div class="absolute bottom-24 right-4 space-y-2">
                    <button class="w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center text-maritime-gray hover:text-ocean-teal">
                        <i class="fas fa-plus"></i>
                    </button>
                    <button class="w-12 h-12 bg-white rounded-full shadow-lg flex items-center justify-center text-maritime-gray hover:text-ocean-teal">
                        <i class="fas fa-minus"></i>
                    </button>
                    <button class="w-12 h-12 bg-ocean-teal rounded-full shadow-lg flex items-center justify-center text-white">
                        <i class="fas fa-crosshairs"></i>
                    </button>
                </div>

                <!-- Sailor Info Card (when marker is tapped) -->
                <div class="absolute bottom-4 left-4 right-4 bg-white rounded-xl shadow-xl p-4">
                    <div class="flex items-center space-x-3">
                        <!-- Professional maritime officer in uniform -->
                        <div class="w-16 h-16 bg-cover bg-center rounded-full border-2 border-ocean-teal" style="background-image: url('https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=400&h=400');"></div>
                        <div class="flex-1">
                            <h4 class="font-semibold text-gray-800">Captain James Rodriguez</h4>
                            <p class="text-sm text-gray-600">Master Mariner • MSC Istanbul</p>
                            <div class="flex items-center space-x-4 mt-1">
                                <span class="text-xs text-ocean-teal font-medium">2.3km away</span>
                                <span class="text-xs text-gray-500">• Online now</span>
                            </div>
                        </div>
                        <button class="bg-ocean-teal text-white px-4 py-2 rounded-lg text-sm font-medium">
                            Say Hello
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- WhatsApp Quick Connect Float -->
        <div class="fixed bottom-24 right-4 z-50">
            <button class="w-14 h-14 bg-green-500 rounded-full shadow-lg flex items-center justify-center text-white hover:bg-green-600 transition-colors">
                <i class="fab fa-whatsapp text-2xl"></i>
            </button>
        </div>
    </div>

    <script>
        // TODO: Implement business logic, API calls, and state management
        (function() {
            // Navigation Functions
            window.openChatOverlay = function() {
                document.getElementById('chatOverlay').style.transform = 'translateX(0)';
            };

            window.closeChatOverlay = function() {
                document.getElementById('chatOverlay').style.transform = 'translateX(100%)';
            };

            window.openProfileOverlay = function() {
                document.getElementById('profileOverlay').style.transform = 'translateX(0)';
            };

            window.closeProfileOverlay = function() {
                document.getElementById('profileOverlay').style.transform = 'translateX(100%)';
            };

            window.openMapOverlay = function() {
                document.getElementById('mapOverlay').style.transform = 'translateX(0)';
            };

            window.closeMapOverlay = function() {
                document.getElementById('mapOverlay').style.transform = 'translateX(100%)';
            };

            // Bottom Navigation Handler
            document.addEventListener('DOMContentLoaded', function() {
                const navButtons = document.querySelectorAll('.bottom-nav-btn');
                
                // Chat Navigation
                document.querySelectorAll('[data-nav="chats"]').forEach(btn => {
                    btn.addEventListener('click', openChatOverlay);
                });
                
                // Profile Navigation  
                document.querySelectorAll('[data-nav="profile"]').forEach(btn => {
                    btn.addEventListener('click', openProfileOverlay);
                });
                
                // Map Navigation
                document.querySelectorAll('[data-nav="map"]').forEach(btn => {
                    btn.addEventListener('click', openMapOverlay);
                });

                // Say Hello Button
                document.querySelectorAll('button:contains("Say Hello")').forEach(btn => {
                    btn.addEventListener('click', function() {
                        // TODO: Implement connection logic
                        openChatOverlay();
                    });
                });

                // Card swiping simulation
                let currentCard = 0;
                const cards = document.querySelectorAll('.sailor-card');
                
                function showNextCard() {
                    if (currentCard < cards.length - 1) {
                        currentCard++;
                        updateCardStack();
                    }
                }

                function updateCardStack() {
                    // TODO: Implement card stack animation logic
                }

                // Distance slider update
                const distanceSlider = document.querySelector('input[type="range"]');
                const distanceDisplay = document.querySelector('.text-ocean-teal.w-12');
                
                distanceSlider?.addEventListener('input', function() {
                    distanceDisplay.textContent = this.value + 'km';
                    // TODO: Update sailor discovery based on distance
                });
            });

            // Bottom navigation click handlers
            document.addEventListener('click', function(e) {
                if (e.target.closest('[data-nav="chats"]')) {
                    openChatOverlay();
                } else if (e.target.closest('[data-nav="profile"]')) {
                    openProfileOverlay();
                } else if (e.target.closest('[data-nav="map"]')) {
                    openMapOverlay();
                }
            });
        })();
    </script>
</body>
</html>