<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Koi Maritime - Professional Document & Resume Management</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'navy': '#1e3a8a',
                        'ocean': '#0d9488',
                        'ocean-light': '#e0f2fe'
                    },
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif']
                    }
                }
            }
        }
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body class="bg-gray-50 font-inter">
    <!-- Navigation Header -->
    <nav class="bg-navy shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <i class="fas fa-anchor text-ocean text-2xl mr-3"></i>
                        <span class="text-white text-xl font-bold">Koi Maritime</span>
                    </div>
                    <div class="hidden md:ml-6 md:flex md:space-x-8">
                        <a href="#" class="text-white hover:text-ocean px-3 py-2 text-sm font-medium">Documents</a>
                        <a href="#" class="text-gray-300 hover:text-white px-3 py-2 text-sm font-medium">Resume Builder</a>
                        <a href="#" class="text-gray-300 hover:text-white px-3 py-2 text-sm font-medium">Discovery</a>
                        <a href="#" class="text-gray-300 hover:text-white px-3 py-2 text-sm font-medium">QAAQ Integration</a>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <div class="bg-green-500 text-white px-3 py-1 rounded-full text-xs flex items-center">
                        <i class="fab fa-whatsapp mr-1"></i>
                        WhatsApp Bot Active
                    </div>
                    <div class="relative">
                        <!-- Profile image of a maritime professional -->
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=150&h=150" alt="Maritime Professional Profile" class="h-8 w-8 rounded-full">
                    </div>
                </div>
            </div>
        </div>
    </nav>

    <div class="flex h-screen bg-gray-50">
        <!-- Sidebar -->
        <div class="w-64 bg-white shadow-lg">
            <div class="p-6">
                <div class="bg-ocean-light rounded-lg p-4 mb-6">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm font-medium text-gray-700">Document Status</span>
                        <i class="fas fa-chart-line text-ocean"></i>
                    </div>
                    <div class="text-2xl font-bold text-navy">94%</div>
                    <div class="text-xs text-gray-600">Documents up-to-date</div>
                </div>

                <nav class="space-y-2">
                    <a href="#" class="bg-navy text-white flex items-center px-4 py-3 rounded-lg font-medium">
                        <i class="fas fa-folder-open mr-3"></i>
                        Documents Folder
                    </a>
                    <a href="#" class="text-gray-700 hover:bg-gray-100 flex items-center px-4 py-3 rounded-lg">
                        <i class="fas fa-file-alt mr-3"></i>
                        Resume Builder
                    </a>
                    <a href="#" class="text-gray-700 hover:bg-gray-100 flex items-center px-4 py-3 rounded-lg">
                        <i class="fas fa-users mr-3"></i>
                        Who's There?
                    </a>
                    <a href="#" class="text-gray-700 hover:bg-gray-100 flex items-center px-4 py-3 rounded-lg">
                        <i class="fas fa-clock mr-3"></i>
                        Expiry Timeline
                    </a>
                    <a href="#" class="text-gray-700 hover:bg-gray-100 flex items-center px-4 py-3 rounded-lg">
                        <i class="fab fa-whatsapp mr-3"></i>
                        WhatsApp Bot
                    </a>
                    <a href="#" class="text-gray-700 hover:bg-gray-100 flex items-center px-4 py-3 rounded-lg">
                        <i class="fas fa-link mr-3"></i>
                        QAAQ Integration
                    </a>
                </nav>
            </div>
        </div>

        <!-- Main Content -->
        <div class="flex-1 overflow-hidden">
            <div class="p-8">
                <!-- Header Section -->
                <div class="mb-8">
                    <h1 class="text-3xl font-bold text-navy mb-2">Marine Documents Folder</h1>
                    <p class="text-gray-600">Manage your maritime certifications, documents, and generate professional resumes</p>
                </div>

                <!-- Quick Actions -->
                <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
                    <div class="bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow cursor-pointer border-l-4 border-ocean">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-800">Upload Document</h3>
                                <p class="text-sm text-gray-600">With OCR extraction</p>
                            </div>
                            <i class="fas fa-upload text-ocean text-2xl"></i>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow cursor-pointer border-l-4 border-green-500">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-800">Generate Resume</h3>
                                <p class="text-sm text-gray-600">Seafarer or Shore format</p>
                            </div>
                            <i class="fas fa-file-pdf text-green-500 text-2xl"></i>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow cursor-pointer border-l-4 border-blue-500">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-800">Who's There?</h3>
                                <p class="text-sm text-gray-600">Find nearby mariners</p>
                            </div>
                            <i class="fas fa-map-marker-alt text-blue-500 text-2xl"></i>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-lg p-6 shadow-md hover:shadow-lg transition-shadow cursor-pointer border-l-4 border-purple-500">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-800">WhatsApp Share</h3>
                                <p class="text-sm text-gray-600">Professional profile</p>
                            </div>
                            <i class="fab fa-whatsapp text-purple-500 text-2xl"></i>
                        </div>
                    </div>
                </div>

                <!-- Document Categories -->
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                    <!-- Left Column - Document Categories -->
                    <div class="space-y-6">
                        <h2 class="text-xl font-bold text-navy mb-4">Document Categories</h2>
                        
                        <!-- Passport Section -->
                        <div class="bg-white rounded-lg shadow-md">
                            <div class="p-6 border-b border-gray-200">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <i class="fas fa-passport text-navy text-xl mr-3"></i>
                                        <h3 class="text-lg font-semibold">Passport</h3>
                                    </div>
                                    <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">Valid</span>
                                </div>
                            </div>
                            <div class="p-6">
                                <!-- Sample passport document display -->
                                <img src="https://images.unsplash.com/photo-1554224155-8d04cb21cd6c?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=400&h=250" alt="Passport document sample" class="w-full h-40 object-cover rounded-lg mb-4">
                                <div class="grid grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <span class="text-gray-600">Number:</span>
                                        <span class="font-medium ml-2" data-bind="passport.number">********</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-600">Expires:</span>
                                        <span class="font-medium ml-2" data-bind="passport.expiry">27-Jun-2027</span>
                                    </div>
                                </div>
                                <button class="mt-4 bg-ocean text-white px-4 py-2 rounded-lg text-sm hover:bg-ocean/90">
                                    <i class="fas fa-edit mr-2"></i>Update Document
                                </button>
                            </div>
                        </div>

                        <!-- CDC Section -->
                        <div class="bg-white rounded-lg shadow-md">
                            <div class="p-6 border-b border-gray-200">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <i class="fas fa-id-card text-navy text-xl mr-3"></i>
                                        <h3 class="text-lg font-semibold">CDC (Continuous Discharge Certificate)</h3>
                                    </div>
                                    <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">Valid</span>
                                </div>
                            </div>
                            <div class="p-6">
                                <!-- Sample CDC document -->
                                <img src="https://images.unsplash.com/photo-1586953208448-b95a79798f07?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=400&h=250" alt="CDC certificate document" class="w-full h-40 object-cover rounded-lg mb-4">
                                <div class="grid grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <span class="text-gray-600">CDC No:</span>
                                        <span class="font-medium ml-2" data-bind="cdc.number">*********</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-600">Expires:</span>
                                        <span class="font-medium ml-2" data-bind="cdc.expiry">9-Aug-2032</span>
                                    </div>
                                </div>
                                <button class="mt-4 bg-ocean text-white px-4 py-2 rounded-lg text-sm hover:bg-ocean/90">
                                    <i class="fas fa-edit mr-2"></i>Update Document
                                </button>
                            </div>
                        </div>

                        <!-- COC Section -->
                        <div class="bg-white rounded-lg shadow-md">
                            <div class="p-6 border-b border-gray-200">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center">
                                        <i class="fas fa-certificate text-navy text-xl mr-3"></i>
                                        <h3 class="text-lg font-semibold">COC (Certificate of Competency)</h3>
                                    </div>
                                    <span class="bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs">Valid</span>
                                </div>
                            </div>
                            <div class="p-6">
                                <div class="grid grid-cols-2 gap-4 text-sm">
                                    <div>
                                        <span class="text-gray-600">Certificate:</span>
                                        <span class="font-medium ml-2" data-bind="coc.type">MEO Class I (Motor)</span>
                                    </div>
                                    <div>
                                        <span class="text-gray-600">Expires:</span>
                                        <span class="font-medium ml-2" data-bind="coc.expiry">6-Sep-2027</span>
                                    </div>
                                </div>
                                <button class="mt-4 bg-ocean text-white px-4 py-2 rounded-lg text-sm hover:bg-ocean/90">
                                    <i class="fas fa-edit mr-2"></i>Update Document
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Right Column - Resume Builder & Discovery -->
                    <div class="space-y-6">
                        <!-- Resume Builder -->
                        <div class="bg-white rounded-lg shadow-md">
                            <div class="p-6 border-b border-gray-200">
                                <h3 class="text-lg font-semibold flex items-center">
                                    <i class="fas fa-file-pdf text-navy mr-3"></i>
                                    Professional Resume Generator
                                </h3>
                            </div>
                            <div class="p-6">
                                <div class="grid grid-cols-2 gap-4 mb-6">
                                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-ocean cursor-pointer">
                                        <i class="fas fa-ship text-ocean text-2xl mb-2"></i>
                                        <h4 class="font-medium">Seafarer Format</h4>
                                        <p class="text-xs text-gray-600">Detailed sailing experience</p>
                                    </div>
                                    <div class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center hover:border-ocean cursor-pointer">
                                        <i class="fas fa-building text-ocean text-2xl mb-2"></i>
                                        <h4 class="font-medium">Shore Job Format</h4>
                                        <p class="text-xs text-gray-600">Corporate professional style</p>
                                    </div>
                                </div>
                                
                                <!-- Resume Preview -->
                                <div class="bg-gray-50 rounded-lg p-4 mb-4">
                                    <h5 class="font-medium mb-2">Latest Generated Resume</h5>
                                    <div class="flex items-center justify-between bg-white p-3 rounded border">
                                        <div class="flex items-center">
                                            <i class="fas fa-file-pdf text-red-500 mr-3"></i>
                                            <div>
                                                <span class="font-medium text-sm" data-bind="resume.filename">Piyush_Gupta_Seafarer_Resume.pdf</span>
                                                <p class="text-xs text-gray-600" data-bind="resume.generated">Generated 2 hours ago</p>
                                            </div>
                                        </div>
                                        <button class="text-ocean hover:text-ocean/80">
                                            <i class="fas fa-download"></i>
                                        </button>
                                    </div>
                                </div>
                                
                                <button class="w-full bg-navy text-white py-3 rounded-lg font-medium hover:bg-navy/90">
                                    Generate New Resume
                                </button>
                            </div>
                        </div>

                        <!-- Who's There Discovery -->
                        <div class="bg-white rounded-lg shadow-md">
                            <div class="p-6 border-b border-gray-200">
                                <h3 class="text-lg font-semibold flex items-center">
                                    <i class="fas fa-users text-navy mr-3"></i>
                                    Who's There? - Maritime Discovery
                                </h3>
                            </div>
                            <div class="p-6">
                                <div class="mb-4">
                                    <div class="flex items-center justify-between mb-2">
                                        <span class="text-sm font-medium">Current Location</span>
                                        <i class="fas fa-map-marker-alt text-ocean"></i>
                                    </div>
                                    <div class="bg-ocean-light rounded-lg p-3">
                                        <span class="font-medium" data-bind="location.current">Istanbul, Turkey</span>
                                        <p class="text-xs text-gray-600">Port: Istanbul Terminal</p>
                                    </div>
                                </div>

                                <!-- Nearby Maritime Professionals -->
                                <div class="space-y-3">
                                    <h5 class="font-medium text-sm">Nearby Maritime Professionals</h5>
                                    
                                    <!-- Professional profile of a maritime engineer -->
                                    <div class="flex items-center justify-between bg-gray-50 p-3 rounded-lg">
                                        <div class="flex items-center">
                                            <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=100&h=100" alt="Maritime professional headshot" class="w-10 h-10 rounded-full mr-3">
                                            <div>
                                                <span class="font-medium text-sm" data-mock="true">Chief Engineer</span>
                                                <p class="text-xs text-gray-600" data-mock="true">MT Vessel Name • 2.3km away</p>
                                            </div>
                                        </div>
                                        <button class="text-green-500 hover:text-green-600">
                                            <i class="fab fa-whatsapp text-lg"></i>
                                        </button>
                                    </div>

                                    <!-- Another maritime professional -->
                                    <div class="flex items-center justify-between bg-gray-50 p-3 rounded-lg">
                                        <div class="flex items-center">
                                            <img src="https://images.unsplash.com/photo-1560250097-0b93528c311a?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=100&h=100" alt="Maritime officer profile photo" class="w-10 h-10 rounded-full mr-3">
                                            <div>
                                                <span class="font-medium text-sm" data-mock="true">2nd Engineer</span>
                                                <p class="text-xs text-gray-600" data-mock="true">MS Container Ship • 5.7km away</p>
                                            </div>
                                        </div>
                                        <button class="text-green-500 hover:text-green-600">
                                            <i class="fab fa-whatsapp text-lg"></i>
                                        </button>
                                    </div>
                                </div>

                                <button class="w-full mt-4 bg-green-500 text-white py-2 rounded-lg text-sm hover:bg-green-600">
                                    <i class="fab fa-whatsapp mr-2"></i>
                                    Share My Profile
                                </button>
                            </div>
                        </div>

                        <!-- QAAQ Integration -->
                        <div class="bg-white rounded-lg shadow-md">
                            <div class="p-6 border-b border-gray-200">
                                <h3 class="text-lg font-semibold flex items-center">
                                    <i class="fas fa-link text-navy mr-3"></i>
                                    QAAQ Integration
                                </h3>
                            </div>
                            <div class="p-6">
                                <div class="flex items-center justify-between bg-navy/5 p-3 rounded-lg mb-4">
                                    <div>
                                        <span class="font-medium text-sm">Connected to QAAQ</span>
                                        <p class="text-xs text-gray-600">Marine Engineering Q&A Platform</p>
                                    </div>
                                    <div class="text-green-500">
                                        <i class="fas fa-check-circle"></i>
                                    </div>
                                </div>
                                <div class="grid grid-cols-2 gap-4 text-center">
                                    <div>
                                        <div class="text-2xl font-bold text-navy" data-bind="qaaq.questions">127</div>
                                        <div class="text-xs text-gray-600">Questions Asked</div>
                                    </div>
                                    <div>
                                        <div class="text-2xl font-bold text-ocean" data-bind="qaaq.reputation">8.4k</div>
                                        <div class="text-xs text-gray-600">Reputation Points</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Expiry Timeline -->
                <div class="mt-8">
                    <h2 class="text-xl font-bold text-navy mb-4">Document Expiry Timeline</h2>
                    <div class="bg-white rounded-lg shadow-md p-6">
                        <div class="space-y-4">
                            <!-- Timeline item -->
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-green-500 rounded-full mr-4"></div>
                                <div class="flex-1">
                                    <div class="flex justify-between items-center">
                                        <span class="font-medium" data-bind="timeline.document1">CDC Certificate</span>
                                        <span class="text-sm text-gray-600" data-bind="timeline.date1">Expires in 8 years</span>
                                    </div>
                                    <div class="text-xs text-gray-500" data-bind="timeline.details1">Valid until 9-Aug-2032</div>
                                </div>
                            </div>
                            
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-yellow-500 rounded-full mr-4"></div>
                                <div class="flex-1">
                                    <div class="flex justify-between items-center">
                                        <span class="font-medium" data-bind="timeline.document2">COC Class I</span>
                                        <span class="text-sm text-gray-600" data-bind="timeline.date2">Expires in 3 years</span>
                                    </div>
                                    <div class="text-xs text-gray-500" data-bind="timeline.details2">Valid until 6-Sep-2027</div>
                                </div>
                            </div>
                            
                            <div class="flex items-center">
                                <div class="w-3 h-3 bg-red-500 rounded-full mr-4"></div>
                                <div class="flex-1">
                                    <div class="flex justify-between items-center">
                                        <span class="font-medium" data-bind="timeline.document3">Medical Certificate</span>
                                        <span class="text-sm text-red-600" data-bind="timeline.date3">Renewal required</span>
                                    </div>
                                    <div class="text-xs text-gray-500" data-bind="timeline.details3">Contact for renewal</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- WhatsApp Bot Integration Panel (Hidden by default) -->
    <div id="whatsapp-panel" class="fixed bottom-4 right-4 bg-white rounded-lg shadow-xl border border-gray-200 w-80 max-h-96 overflow-hidden hidden">
        <div class="bg-green-500 text-white p-4 flex items-center justify-between">
            <div class="flex items-center">
                <i class="fab fa-whatsapp mr-2"></i>
                <span class="font-medium">Koi Maritime Bot</span>
            </div>
            <button onclick="toggleWhatsAppPanel()" class="text-white hover:text-gray-200">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="p-4">
            <div class="space-y-3 mb-4 max-h-48 overflow-y-auto">
                <div class="bg-gray-100 rounded-lg p-3 text-sm">
                    <span class="font-medium">Bot:</span> Welcome to Koi Maritime! I can help you with document reminders, resume generation, and finding nearby maritime professionals.
                </div>
                <div class="bg-green-100 rounded-lg p-3 text-sm ml-8">
                    <span class="font-medium">You:</span> Send my resume to Chief Engineer
                </div>
                <div class="bg-gray-100 rounded-lg p-3 text-sm">
                    <span class="font-medium">Bot:</span> I've shared your latest seafarer resume with the Chief Engineer nearby. They'll receive it via WhatsApp.
                </div>
            </div>
            <div class="flex">
                <input type="text" placeholder="Type a message..." class="flex-1 border border-gray-300 rounded-l-lg px-3 py-2 text-sm">
                <button class="bg-green-500 text-white px-4 py-2 rounded-r-lg hover:bg-green-600">
                    <i class="fas fa-paper-plane"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Floating WhatsApp Button -->
    <button onclick="toggleWhatsAppPanel()" class="fixed bottom-4 right-4 bg-green-500 text-white p-4 rounded-full shadow-lg hover:bg-green-600 transition-colors">
        <i class="fab fa-whatsapp text-2xl"></i>
    </button>

    <script>
        (function() {
            // Toggle WhatsApp panel visibility
            window.toggleWhatsAppPanel = function() {
                const panel = document.getElementById('whatsapp-panel');
                const button = document.querySelector('.fixed.bottom-4.right-4.bg-green-500');
                
                if (panel.classList.contains('hidden')) {
                    panel.classList.remove('hidden');
                    button.style.display = 'none';
                } else {
                    panel.classList.add('hidden');
                    button.style.display = 'block';
                }
            };

            // TODO: Implement OCR document processing
            // TODO: Implement resume generation logic
            // TODO: Implement WhatsApp bot API integration
            // TODO: Implement location-based discovery
            // TODO: Implement QAAQ platform integration
            // TODO: Implement document expiry notifications
        })();
    </script>
</body>
</html>