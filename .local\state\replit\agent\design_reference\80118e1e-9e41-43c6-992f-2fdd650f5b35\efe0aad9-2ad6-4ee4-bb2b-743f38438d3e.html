<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>1234 Koi Hai? - Maritime Professionals Network</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'navy': '#1e40af',
                        'ocean': '#0891b2',
                        'ocean-light': '#0ea5e9',
                        'maritime-blue': '#dbeafe'
                    },
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif']
                    }
                }
            }
        };
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <meta name="theme-color" content="#1e40af">
    <link rel="manifest" href="manifest.json">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
</head>
<body class="font-inter bg-gray-50">
    <!-- @COMPONENT: AppContainer -->
    <div id="app-container" class="min-h-screen">
        
        <!-- @COMPONENT: NavigationHeader -->
        <nav class="bg-navy text-white shadow-lg fixed top-0 w-full z-50">
            <div class="max-w-7xl mx-auto px-4">
                <div class="flex justify-between items-center h-16">
                    <div class="flex items-center space-x-2">
                        <i class="fas fa-anchor text-ocean text-xl"></i>
                        <span class="font-bold text-lg">1234 Koi Hai?</span>
                    </div>
                    <div class="hidden md:flex items-center space-x-6">
                        <a href="#home" class="hover:text-ocean transition-colors">Home</a>
                        <a href="#discovery" class="hover:text-ocean transition-colors">Discovery</a>
                        <a href="#profile" class="hover:text-ocean transition-colors">Profile</a>
                        <a href="#contact" class="hover:text-ocean transition-colors">Support</a>
                    </div>
                    <button class="md:hidden">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </nav>
        <!-- @END_COMPONENT: NavigationHeader -->

        <!-- @COMPONENT: MainContent -->
        <main class="pt-16">
            
            <!-- @COMPONENT: HeroSection -->
            <section id="home" class="bg-gradient-to-br from-navy via-ocean to-ocean-light text-white">
                <!-- Maritime professionals networking event with diverse officers -->
                <div class="absolute inset-0 bg-black bg-opacity-40"></div>
                <div style="background-image: url('https://pixabay.com/get/g8ea01b74a243d244b1ae5c3382aaf4bb92e09086a5a257809302d6e2b85fd719eb002c774ed58901d5c63c2d0aaab93c439b1bcdcd010a1127c65ea6a7c0dee1_1280.jpg'); background-size: cover; background-position: center;" class="relative">
                    <div class="absolute inset-0 bg-gradient-to-br from-navy/80 via-ocean/70 to-ocean-light/60"></div>
                    <div class="relative max-w-7xl mx-auto px-4 py-20 lg:py-32">
                        <div class="text-center">
                            <h1 class="text-4xl md:text-6xl lg:text-7xl font-bold mb-6">
                                1234 <span class="text-ocean-light">Koi Hai?</span>
                            </h1>
                            <p class="text-xl md:text-2xl mb-8 text-gray-200 max-w-3xl mx-auto">
                                Discover maritime professionals around you. Connect with officers and crew worldwide.
                            </p>
                            <div class="flex flex-col sm:flex-row gap-4 justify-center items-center">
                                <button class="bg-ocean hover:bg-ocean-light text-white px-8 py-4 rounded-xl font-semibold text-lg transition-colors shadow-lg">
                                    <i class="fas fa-search mr-2"></i>
                                    Start Discovery
                                </button>
                                <button class="bg-white/10 hover:bg-white/20 text-white px-8 py-4 rounded-xl font-semibold text-lg transition-colors border border-white/20">
                                    <i class="fab fa-whatsapp mr-2"></i>
                                    WhatsApp Bot
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <!-- @END_COMPONENT: HeroSection -->

            <!-- @COMPONENT: DiscoverySection -->
            <section id="discovery" class="py-16 bg-white">
                <div class="max-w-7xl mx-auto px-4">
                    <div class="text-center mb-12">
                        <h2 class="text-3xl md:text-4xl font-bold text-navy mb-4">
                            Who's There?
                        </h2>
                        <p class="text-gray-600 text-lg max-w-2xl mx-auto">
                            Discover maritime professionals nearby or on vessels around you
                        </p>
                    </div>

                    <!-- @COMPONENT: DiscoveryTabs -->
                    <div class="max-w-4xl mx-auto">
                        <div class="flex border-b border-gray-200 mb-8">
                            <button id="officers-tab" class="flex-1 py-4 px-6 text-center font-semibold border-b-2 border-ocean text-ocean">
                                <i class="fas fa-user-tie mr-2"></i>
                                Officers Nearby
                            </button>
                            <button id="crew-tab" class="flex-1 py-4 px-6 text-center font-semibold text-gray-500 hover:text-ocean transition-colors">
                                <i class="fas fa-users mr-2"></i>
                                Crew Nearby
                            </button>
                        </div>

                        <!-- @COMPONENT: OfficersTab -->
                        <div id="officers-content" class="space-y-6">
                            <!-- @MAP: officers.map(officer => ( -->
                            <div class="bg-white rounded-xl shadow-md border border-gray-100 p-6 hover:shadow-lg transition-shadow" data-mock="true">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-4">
                                        <div class="w-16 h-16 bg-gradient-to-br from-navy to-ocean rounded-full flex items-center justify-center text-white text-xl font-bold">
                                            PG
                                        </div>
                                        <div>
                                            <h3 class="font-semibold text-lg text-gray-900" data-bind="officer.name">Piyush Gupta</h3>
                                            <p class="text-ocean font-medium" data-bind="officer.rank">Chief Engineer</p>
                                            <p class="text-gray-600 text-sm" data-bind="officer.vessel">MT Solar Claire</p>
                                            <div class="flex items-center text-sm text-gray-500 mt-1">
                                                <i class="fas fa-map-marker-alt mr-1"></i>
                                                <span data-bind="officer.location">0.8 km away</span>
                                            </div>
                                        </div>
                                    </div>
                                    <button class="bg-ocean hover:bg-ocean-light text-white px-6 py-2 rounded-lg font-medium transition-colors">
                                        <i class="fas fa-handshake mr-2"></i>
                                        Connect
                                    </button>
                                </div>
                            </div>

                            <div class="bg-white rounded-xl shadow-md border border-gray-100 p-6 hover:shadow-lg transition-shadow" data-mock="true">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-4">
                                        <div class="w-16 h-16 bg-gradient-to-br from-ocean to-ocean-light rounded-full flex items-center justify-center text-white text-xl font-bold">
                                            AK
                                        </div>
                                        <div>
                                            <h3 class="font-semibold text-lg text-gray-900">Arjun Kumar</h3>
                                            <p class="text-ocean font-medium">Second Engineer</p>
                                            <p class="text-gray-600 text-sm">MV Ocean Pioneer</p>
                                            <div class="flex items-center text-sm text-gray-500 mt-1">
                                                <i class="fas fa-map-marker-alt mr-1"></i>
                                                <span>1.2 km away</span>
                                            </div>
                                        </div>
                                    </div>
                                    <button class="bg-ocean hover:bg-ocean-light text-white px-6 py-2 rounded-lg font-medium transition-colors">
                                        <i class="fas fa-handshake mr-2"></i>
                                        Connect
                                    </button>
                                </div>
                            </div>

                            <div class="bg-white rounded-xl shadow-md border border-gray-100 p-6 hover:shadow-lg transition-shadow" data-mock="true">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-4">
                                        <div class="w-16 h-16 bg-gradient-to-br from-navy to-ocean-light rounded-full flex items-center justify-center text-white text-xl font-bold">
                                            RS
                                        </div>
                                        <div>
                                            <h3 class="font-semibold text-lg text-gray-900">Rajesh Sharma</h3>
                                            <p class="text-ocean font-medium">Third Engineer</p>
                                            <p class="text-gray-600 text-sm">MS Baltic Star</p>
                                            <div class="flex items-center text-sm text-gray-500 mt-1">
                                                <i class="fas fa-map-marker-alt mr-1"></i>
                                                <span>2.1 km away</span>
                                            </div>
                                        </div>
                                    </div>
                                    <button class="bg-ocean hover:bg-ocean-light text-white px-6 py-2 rounded-lg font-medium transition-colors">
                                        <i class="fas fa-handshake mr-2"></i>
                                        Connect
                                    </button>
                                </div>
                            </div>
                            <!-- @END_MAP )) -->
                        </div>

                        <!-- @COMPONENT: CrewTab -->
                        <div id="crew-content" class="space-y-6 hidden">
                            <!-- @MAP: crew.map(member => ( -->
                            <div class="bg-white rounded-xl shadow-md border border-gray-100 p-6 hover:shadow-lg transition-shadow" data-mock="true">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-4">
                                        <div class="w-16 h-16 bg-gradient-to-br from-ocean to-navy rounded-full flex items-center justify-center text-white text-xl font-bold">
                                            MK
                                        </div>
                                        <div>
                                            <h3 class="font-semibold text-lg text-gray-900">Manoj Kumar</h3>
                                            <p class="text-ocean font-medium">Bosun</p>
                                            <p class="text-gray-600 text-sm">MV Atlantic Gem</p>
                                            <div class="flex items-center text-sm text-gray-500 mt-1">
                                                <i class="fas fa-ship mr-1"></i>
                                                <span>Same vessel</span>
                                            </div>
                                        </div>
                                    </div>
                                    <button class="bg-ocean hover:bg-ocean-light text-white px-6 py-2 rounded-lg font-medium transition-colors">
                                        <i class="fas fa-handshake mr-2"></i>
                                        Connect
                                    </button>
                                </div>
                            </div>

                            <div class="bg-white rounded-xl shadow-md border border-gray-100 p-6 hover:shadow-lg transition-shadow" data-mock="true">
                                <div class="flex items-center justify-between">
                                    <div class="flex items-center space-x-4">
                                        <div class="w-16 h-16 bg-gradient-to-br from-ocean-light to-ocean rounded-full flex items-center justify-center text-white text-xl font-bold">
                                            VN
                                        </div>
                                        <div>
                                            <h3 class="font-semibold text-lg text-gray-900">Vikram Nair</h3>
                                            <p class="text-ocean font-medium">Able Seaman</p>
                                            <p class="text-gray-600 text-sm">MV Pacific Star</p>
                                            <div class="flex items-center text-sm text-gray-500 mt-1">
                                                <i class="fas fa-ship mr-1"></i>
                                                <span>Nearby vessel</span>
                                            </div>
                                        </div>
                                    </div>
                                    <button class="bg-ocean hover:bg-ocean-light text-white px-6 py-2 rounded-lg font-medium transition-colors">
                                        <i class="fas fa-handshake mr-2"></i>
                                        Connect
                                    </button>
                                </div>
                            </div>
                            <!-- @END_MAP )) -->
                        </div>
                    </div>
                    <!-- @END_COMPONENT: DiscoveryTabs -->
                </div>
            </section>
            <!-- @END_COMPONENT: DiscoverySection -->

            <!-- @COMPONENT: FeaturesSection -->
            <section class="py-16 bg-maritime-blue">
                <div class="max-w-7xl mx-auto px-4">
                    <div class="text-center mb-12">
                        <h2 class="text-3xl md:text-4xl font-bold text-navy mb-4">
                            Why Maritime Professionals Choose Us
                        </h2>
                        <p class="text-gray-600 text-lg">
                            Connect, collaborate, and advance your maritime career
                        </p>
                    </div>

                    <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                        <div class="bg-white rounded-xl p-8 shadow-md hover:shadow-lg transition-shadow">
                            <div class="w-16 h-16 bg-ocean rounded-xl flex items-center justify-center mb-6">
                                <i class="fas fa-location-arrow text-white text-2xl"></i>
                            </div>
                            <h3 class="text-xl font-semibold text-navy mb-4">Location-Based Discovery</h3>
                            <p class="text-gray-600">Find maritime professionals near your current location or vessel position in real-time.</p>
                        </div>

                        <div class="bg-white rounded-xl p-8 shadow-md hover:shadow-lg transition-shadow">
                            <div class="w-16 h-16 bg-navy rounded-xl flex items-center justify-center mb-6">
                                <i class="fab fa-whatsapp text-white text-2xl"></i>
                            </div>
                            <h3 class="text-xl font-semibold text-navy mb-4">WhatsApp Integration</h3>
                            <p class="text-gray-600">Seamless integration with WhatsApp for instant communication and bot assistance.</p>
                        </div>

                        <div class="bg-white rounded-xl p-8 shadow-md hover:shadow-lg transition-shadow">
                            <div class="w-16 h-16 bg-ocean-light rounded-xl flex items-center justify-center mb-6">
                                <i class="fas fa-file-alt text-white text-2xl"></i>
                            </div>
                            <h3 class="text-xl font-semibold text-navy mb-4">Document Management</h3>
                            <p class="text-gray-600">OCR-powered document tracking and resume builder for maritime professionals.</p>
                        </div>

                        <div class="bg-white rounded-xl p-8 shadow-md hover:shadow-lg transition-shadow">
                            <div class="w-16 h-16 bg-navy rounded-xl flex items-center justify-center mb-6">
                                <i class="fas fa-shield-alt text-white text-2xl"></i>
                            </div>
                            <h3 class="text-xl font-semibold text-navy mb-4">QAAQ Verified</h3>
                            <p class="text-gray-600">Integration with QAAQ database ensures verified maritime professionals only.</p>
                        </div>

                        <div class="bg-white rounded-xl p-8 shadow-md hover:shadow-lg transition-shadow">
                            <div class="w-16 h-16 bg-ocean rounded-xl flex items-center justify-center mb-6">
                                <i class="fas fa-mobile-alt text-white text-2xl"></i>
                            </div>
                            <h3 class="text-xl font-semibold text-navy mb-4">PWA Ready</h3>
                            <p class="text-gray-600">Progressive Web App technology for native app-like experience on any device.</p>
                        </div>

                        <div class="bg-white rounded-xl p-8 shadow-md hover:shadow-lg transition-shadow">
                            <div class="w-16 h-16 bg-ocean-light rounded-xl flex items-center justify-center mb-6">
                                <i class="fas fa-globe text-white text-2xl"></i>
                            </div>
                            <h3 class="text-xl font-semibold text-navy mb-4">Global Network</h3>
                            <p class="text-gray-600">Connect with maritime professionals worldwide, expanding your professional network.</p>
                        </div>
                    </div>
                </div>
            </section>
            <!-- @END_COMPONENT: FeaturesSection -->

            <!-- @COMPONENT: ChatPreviewSection -->
            <section class="py-16 bg-white">
                <div class="max-w-7xl mx-auto px-4">
                    <div class="text-center mb-12">
                        <h2 class="text-3xl md:text-4xl font-bold text-navy mb-4">
                            WhatsApp-Style Communication
                        </h2>
                        <p class="text-gray-600 text-lg">
                            Familiar chat interface for seamless professional networking
                        </p>
                    </div>

                    <div class="max-w-md mx-auto">
                        <!-- @COMPONENT: ChatInterface -->
                        <div class="bg-gray-100 rounded-t-xl p-4 border-b">
                            <div class="flex items-center space-x-3">
                                <div class="w-10 h-10 bg-gradient-to-br from-navy to-ocean rounded-full flex items-center justify-center text-white font-semibold">
                                    PG
                                </div>
                                <div>
                                    <h4 class="font-semibold text-gray-900">Piyush Gupta</h4>
                                    <p class="text-sm text-green-600">
                                        <i class="fas fa-circle text-xs mr-1"></i>
                                        Online
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-white p-4 space-y-4 h-80 overflow-y-auto">
                            <!-- @FUNCTIONALITY: WhatsApp-style chat messages with timestamp and read receipts -->
                            <div class="flex justify-start">
                                <div class="bg-gray-200 rounded-lg rounded-bl-none p-3 max-w-xs">
                                    <p class="text-gray-800">Hello! I see you're also in Mumbai port. Are you familiar with MAN B&W engines?</p>
                                    <p class="text-xs text-gray-500 mt-1">2:30 PM</p>
                                </div>
                            </div>

                            <div class="flex justify-end">
                                <div class="bg-ocean text-white rounded-lg rounded-br-none p-3 max-w-xs">
                                    <p>Yes, I'm a Chief Engineer with 21+ years experience. Happy to help!</p>
                                    <p class="text-xs text-ocean-light mt-1">2:32 PM ✓✓</p>
                                </div>
                            </div>

                            <div class="flex justify-start">
                                <div class="bg-gray-200 rounded-lg rounded-bl-none p-3 max-w-xs">
                                    <p class="text-gray-800">Great! Having issues with cylinder 3 temperature. Can we discuss?</p>
                                    <p class="text-xs text-gray-500 mt-1">2:33 PM</p>
                                </div>
                            </div>

                            <div class="flex justify-end">
                                <div class="bg-ocean text-white rounded-lg rounded-br-none p-3 max-w-xs">
                                    <p>Absolutely! Let's troubleshoot this together.</p>
                                    <p class="text-xs text-ocean-light mt-1">2:35 PM ✓✓</p>
                                </div>
                            </div>
                        </div>

                        <div class="bg-gray-100 rounded-b-xl p-4">
                            <div class="flex items-center space-x-2">
                                <input type="text" placeholder="Type a message..." class="flex-1 bg-white rounded-full px-4 py-2 border border-gray-300 focus:outline-none focus:border-ocean">
                                <button class="bg-ocean text-white w-10 h-10 rounded-full flex items-center justify-center hover:bg-ocean-light transition-colors">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                            </div>
                        </div>
                        <!-- @END_COMPONENT: ChatInterface -->
                    </div>
                </div>
            </section>
            <!-- @END_COMPONENT: ChatPreviewSection -->

            <!-- @COMPONENT: CTASection -->
            <section class="py-16 bg-gradient-to-r from-navy to-ocean text-white">
                <!-- Ship crew working together on deck -->
                <div style="background-image: url('https://images.unsplash.com/photo-1544551763-46a013bb70d5?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&h=1080'); background-size: cover; background-position: center;" class="relative">
                    <div class="absolute inset-0 bg-gradient-to-r from-navy/90 to-ocean/80"></div>
                    <div class="relative max-w-7xl mx-auto px-4 text-center">
                        <h2 class="text-3xl md:text-5xl font-bold mb-6">
                            Ready to Connect?
                        </h2>
                        <p class="text-xl mb-8 text-gray-200 max-w-2xl mx-auto">
                            Join thousands of maritime professionals discovering and connecting worldwide
                        </p>
                        <div class="flex flex-col sm:flex-row gap-4 justify-center">
                            <button class="bg-white text-navy px-8 py-4 rounded-xl font-semibold text-lg hover:bg-gray-100 transition-colors shadow-lg">
                                <i class="fas fa-rocket mr-2"></i>
                                Get Started Now
                            </button>
                            <button class="border-2 border-white text-white px-8 py-4 rounded-xl font-semibold text-lg hover:bg-white hover:text-navy transition-colors">
                                <i class="fab fa-whatsapp mr-2"></i>
                                +90 536 369 4997
                            </button>
                        </div>
                    </div>
                </div>
            </section>
            <!-- @END_COMPONENT: CTASection -->

            <!-- @COMPONENT: CompliancePages -->
            <section id="compliance" class="py-16 bg-gray-50">
                <div class="max-w-7xl mx-auto px-4">
                    <div class="text-center mb-12">
                        <h2 class="text-3xl md:text-4xl font-bold text-navy mb-4">
                            Legal & Compliance
                        </h2>
                        <p class="text-gray-600 text-lg">
                            Your privacy and data security are our top priorities
                        </p>
                    </div>

                    <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                        <div class="bg-white rounded-xl p-6 shadow-md hover:shadow-lg transition-shadow text-center">
                            <i class="fas fa-shield-alt text-ocean text-3xl mb-4"></i>
                            <h3 class="font-semibold text-navy mb-2">Privacy Policy</h3>
                            <p class="text-gray-600 text-sm mb-4">How we protect and use your data</p>
                            <button class="text-ocean hover:text-navy font-medium">Read Policy</button>
                        </div>

                        <div class="bg-white rounded-xl p-6 shadow-md hover:shadow-lg transition-shadow text-center">
                            <i class="fas fa-file-contract text-ocean text-3xl mb-4"></i>
                            <h3 class="font-semibold text-navy mb-2">Terms of Service</h3>
                            <p class="text-gray-600 text-sm mb-4">Terms and conditions of use</p>
                            <button class="text-ocean hover:text-navy font-medium">Read Terms</button>
                        </div>

                        <div class="bg-white rounded-xl p-6 shadow-md hover:shadow-lg transition-shadow text-center">
                            <i class="fas fa-user-slash text-ocean text-3xl mb-4"></i>
                            <h3 class="font-semibold text-navy mb-2">Account Deletion</h3>
                            <p class="text-gray-600 text-sm mb-4">Request account and data removal</p>
                            <button class="text-ocean hover:text-navy font-medium">Delete Account</button>
                        </div>

                        <div class="bg-white rounded-xl p-6 shadow-md hover:shadow-lg transition-shadow text-center">
                            <i class="fas fa-download text-ocean text-3xl mb-4"></i>
                            <h3 class="font-semibold text-navy mb-2">Data Export</h3>
                            <p class="text-gray-600 text-sm mb-4">Download your personal data</p>
                            <button class="text-ocean hover:text-navy font-medium">Export Data</button>
                        </div>
                    </div>

                    <!-- @COMPONENT: CookieConsent -->
                    <div id="cookie-consent" class="fixed bottom-4 left-4 right-4 bg-white border border-gray-200 rounded-xl shadow-lg p-4 md:p-6 z-40">
                        <div class="flex flex-col md:flex-row items-start md:items-center justify-between gap-4">
                            <div class="flex-1">
                                <h4 class="font-semibold text-navy mb-2">Cookie Consent</h4>
                                <p class="text-gray-600 text-sm">We use cookies to enhance your experience and provide personalized maritime professional connections.</p>
                            </div>
                            <div class="flex gap-2">
                                <button class="bg-gray-200 text-gray-700 px-4 py-2 rounded-lg text-sm hover:bg-gray-300 transition-colors">
                                    Customize
                                </button>
                                <button class="bg-ocean text-white px-4 py-2 rounded-lg text-sm hover:bg-ocean-light transition-colors">
                                    Accept All
                                </button>
                            </div>
                        </div>
                    </div>
                    <!-- @END_COMPONENT: CookieConsent -->
                </div>
            </section>
            <!-- @END_COMPONENT: CompliancePages -->

        </main>
        <!-- @END_COMPONENT: MainContent -->

        <!-- @COMPONENT: BottomNavigation -->
        <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 md:hidden z-40">
            <div class="flex justify-around py-2">
                <button class="flex flex-col items-center p-2 text-ocean">
                    <i class="fas fa-home text-xl"></i>
                    <span class="text-xs mt-1">Home</span>
                </button>
                <button class="flex flex-col items-center p-2 text-gray-500 hover:text-ocean transition-colors">
                    <i class="fas fa-search text-xl"></i>
                    <span class="text-xs mt-1">Discover</span>
                </button>
                <button class="flex flex-col items-center p-2 text-gray-500 hover:text-ocean transition-colors">
                    <i class="fas fa-comments text-xl"></i>
                    <span class="text-xs mt-1">Chats</span>
                </button>
                <button class="flex flex-col items-center p-2 text-gray-500 hover:text-ocean transition-colors">
                    <i class="fas fa-user text-xl"></i>
                    <span class="text-xs mt-1">Profile</span>
                </button>
            </div>
        </nav>
        <!-- @END_COMPONENT: BottomNavigation -->

        <!-- @COMPONENT: FloatingActionButton -->
        <button class="fixed bottom-20 md:bottom-8 right-4 bg-ocean hover:bg-ocean-light text-white w-14 h-14 rounded-full shadow-lg flex items-center justify-center transition-colors z-30">
            <i class="fab fa-whatsapp text-2xl"></i>
        </button>
        <!-- @END_COMPONENT: FloatingActionButton -->

        <!-- @COMPONENT: Footer -->
        <footer id="contact" class="bg-navy text-white py-12">
            <div class="max-w-7xl mx-auto px-4">
                <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                    <div>
                        <div class="flex items-center space-x-2 mb-4">
                            <i class="fas fa-anchor text-ocean text-xl"></i>
                            <span class="font-bold text-lg">1234 Koi Hai?</span>
                        </div>
                        <p class="text-gray-300 text-sm">
                            Connecting maritime professionals worldwide through innovative technology and community.
                        </p>
                    </div>

                    <div>
                        <h4 class="font-semibold mb-4">Quick Links</h4>
                        <ul class="space-y-2 text-sm text-gray-300">
                            <li><a href="#home" class="hover:text-ocean transition-colors">Home</a></li>
                            <li><a href="#discovery" class="hover:text-ocean transition-colors">Discovery</a></li>
                            <li><a href="#features" class="hover:text-ocean transition-colors">Features</a></li>
                            <li><a href="https://qaaqit.replit.app/" class="hover:text-ocean transition-colors">QAAQ Platform</a></li>
                        </ul>
                    </div>

                    <div>
                        <h4 class="font-semibold mb-4">Legal</h4>
                        <ul class="space-y-2 text-sm text-gray-300">
                            <li><a href="#privacy" class="hover:text-ocean transition-colors">Privacy Policy</a></li>
                            <li><a href="#terms" class="hover:text-ocean transition-colors">Terms of Service</a></li>
                            <li><a href="#delete" class="hover:text-ocean transition-colors">Delete Account</a></li>
                            <li><a href="#export" class="hover:text-ocean transition-colors">Export Data</a></li>
                        </ul>
                    </div>

                    <div>
                        <h4 class="font-semibold mb-4">Contact & Support</h4>
                        <div class="space-y-3 text-sm text-gray-300">
                            <div class="flex items-center space-x-2">
                                <i class="fab fa-whatsapp text-ocean"></i>
                                <span>+90 536 369 4997</span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-envelope text-ocean"></i>
                                <span><EMAIL></span>
                            </div>
                            <div class="flex items-center space-x-2">
                                <i class="fas fa-globe text-ocean"></i>
                                <span>Available 24/7</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-300 text-sm">
                    <p>&copy; 2025 1234 Koi Hai? - Maritime Professionals Network. All rights reserved.</p>
                    <p class="mt-2">Powered by QAAQ Maritime Engineering Knowledge Hub</p>
                </div>
            </div>
        </footer>
        <!-- @END_COMPONENT: Footer -->

    </div>
    <!-- @END_COMPONENT: AppContainer -->

    <!-- @FUNCTIONALITY: Tab switching for discovery section -->
    <script>
        (function() {
            // TODO: Implement full tab switching functionality with state management
            const officersTab = document.getElementById('officers-tab');
            const crewTab = document.getElementById('crew-tab');
            const officersContent = document.getElementById('officers-content');
            const crewContent = document.getElementById('crew-content');

            function switchToOfficers() {
                officersTab.classList.add('border-ocean', 'text-ocean');
                officersTab.classList.remove('text-gray-500');
                crewTab.classList.remove('border-ocean', 'text-ocean');
                crewTab.classList.add('text-gray-500');
                
                officersContent.classList.remove('hidden');
                crewContent.classList.add('hidden');
            }

            function switchToCrew() {
                crewTab.classList.add('border-ocean', 'text-ocean');
                crewTab.classList.remove('text-gray-500');
                officersTab.classList.remove('border-ocean', 'text-ocean');
                officersTab.classList.add('text-gray-500');
                
                crewContent.classList.remove('hidden');
                officersContent.classList.add('hidden');
            }

            officersTab.addEventListener('click', switchToOfficers);
            crewTab.addEventListener('click', switchToCrew);

            // TODO: Implement location-based discovery API integration
            // TODO: Implement WhatsApp bot integration
            // TODO: Implement PWA service worker registration
            // TODO: Implement profile management and document OCR
        })();
    </script>

</body>
</html>