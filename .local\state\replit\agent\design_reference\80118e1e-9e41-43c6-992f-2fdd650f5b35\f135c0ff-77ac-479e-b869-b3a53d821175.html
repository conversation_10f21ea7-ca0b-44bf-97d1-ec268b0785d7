<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QaaqConnect - Shore Leave Discovery for Maritime Professionals</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'navy': '#1e3a8a',
                        'ocean-teal': '#0891b2',
                        'ocean-light': '#0ea5e9',
                        'maritime-gray': '#64748b'
                    },
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif']
                    }
                }
            }
        };
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body class="font-inter bg-gray-50">

    <!-- @COMPONENT: AppHeader [sticky navigation with branding] -->
    <header class="bg-white shadow-sm sticky top-0 z-50 border-b border-gray-100">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0 flex items-center">
                        <i class="fas fa-anchor text-navy text-2xl mr-3"></i>
                        <h1 class="text-xl font-bold text-navy">QaaqConnect</h1>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="text-maritime-gray hover:text-navy transition-colors">
                        <i class="fas fa-bell text-lg"></i>
                    </button>
                    <button class="text-maritime-gray hover:text-navy transition-colors">
                        <i class="fas fa-user-circle text-lg"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>
    <!-- @END_COMPONENT: AppHeader -->

    <!-- @COMPONENT: HeroSection [main search and tagline] -->
    <section class="bg-gradient-to-br from-navy via-blue-800 to-ocean-teal text-white py-8 px-4">
        <div class="max-w-4xl mx-auto text-center">
            <h2 class="text-3xl md:text-4xl font-bold mb-3">Shore Leave Made Simple</h2>
            <p class="text-lg mb-2 text-blue-100">1234 Koi Hai?</p>
            <p class="text-sm mb-8 text-blue-200 max-w-2xl mx-auto">
                Discover the best shore leave experiences near your port. From local restaurants to safe areas, 
                we've got maritime professionals covered worldwide.
            </p>
            
            <!-- @COMPONENT: SearchBar [AI-powered search with prompts] -->
            <div class="relative max-w-2xl mx-auto">
                <div class="bg-white rounded-xl shadow-lg p-1">
                    <div class="flex items-center">
                        <i class="fas fa-search text-maritime-gray ml-4 mr-3"></i>
                        <input 
                            type="text" 
                            placeholder="Ask about your next shore leave..."
                            class="flex-1 py-3 text-gray-700 focus:outline-none"
                            data-bind="searchQuery"
                            data-mock="true"
                        />
                        <button class="bg-ocean-teal hover:bg-ocean-light text-white px-6 py-3 rounded-lg font-medium transition-colors">
                            Search
                        </button>
                    </div>
                </div>
                
                <!-- Search Suggestions -->
                <div class="mt-4 flex flex-wrap gap-2 justify-center">
                    <span class="bg-blue-800 bg-opacity-50 text-blue-100 px-3 py-1 rounded-full text-sm">
                        "Best restaurants in Hamburg port?"
                    </span>
                    <span class="bg-blue-800 bg-opacity-50 text-blue-100 px-3 py-1 rounded-full text-sm">
                        "Cheap SIM cards in Singapore?"
                    </span>
                    <span class="bg-blue-800 bg-opacity-50 text-blue-100 px-3 py-1 rounded-full text-sm">
                        "Safe areas in Rotterdam at night?"
                    </span>
                </div>
            </div>
            <!-- @END_COMPONENT: SearchBar -->
        </div>
    </section>
    <!-- @END_COMPONENT: HeroSection -->

    <!-- @COMPONENT: MapSection [interactive map with controls] -->
    <section class="bg-white py-6 px-4 border-b border-gray-200">
        <div class="max-w-7xl mx-auto">
            <div class="flex items-center justify-between mb-4">
                <h3 class="text-lg font-semibold text-gray-900">Discover Nearby</h3>
                <div class="flex items-center space-x-2">
                    <button class="bg-gray-100 hover:bg-gray-200 w-8 h-8 rounded-full flex items-center justify-center transition-colors">
                        <i class="fas fa-minus text-sm text-gray-600"></i>
                    </button>
                    <span class="text-sm font-medium text-gray-700 min-w-16 text-center">50km</span>
                    <button class="bg-gray-100 hover:bg-gray-200 w-8 h-8 rounded-full flex items-center justify-center transition-colors">
                        <i class="fas fa-plus text-sm text-gray-600"></i>
                    </button>
                </div>
            </div>
            
            <!-- Map Container -->
            <div class="relative bg-gray-100 rounded-xl overflow-hidden h-64 md:h-80">
                <!-- Simulated map background -->
                <div class="absolute inset-0 bg-gradient-to-br from-blue-100 to-blue-200"></div>
                
                <!-- Map pins -->
                <div class="absolute top-1/3 left-1/4 transform -translate-x-1/2 -translate-y-1/2">
                    <div class="bg-navy text-white w-8 h-8 rounded-full flex items-center justify-center shadow-lg">
                        <i class="fas fa-anchor text-sm"></i>
                    </div>
                </div>
                
                <div class="absolute top-2/3 right-1/3 transform -translate-x-1/2 -translate-y-1/2">
                    <div class="bg-ocean-teal text-white w-8 h-8 rounded-full flex items-center justify-center shadow-lg">
                        <i class="fas fa-utensils text-sm"></i>
                    </div>
                </div>
                
                <div class="absolute bottom-1/4 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                    <div class="bg-green-600 text-white w-8 h-8 rounded-full flex items-center justify-center shadow-lg">
                        <i class="fas fa-shopping-cart text-sm"></i>
                    </div>
                </div>
                
                <!-- Current location pin -->
                <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                    <div class="bg-red-500 text-white w-10 h-10 rounded-full flex items-center justify-center shadow-lg border-2 border-white">
                        <i class="fas fa-user text-sm"></i>
                    </div>
                </div>
                
                <!-- Map controls -->
                <div class="absolute bottom-4 right-4 flex flex-col space-y-2">
                    <button class="bg-white shadow-lg w-10 h-10 rounded-lg flex items-center justify-center hover:bg-gray-50 transition-colors">
                        <i class="fas fa-crosshairs text-gray-700"></i>
                    </button>
                    <button class="bg-white shadow-lg w-10 h-10 rounded-lg flex items-center justify-center hover:bg-gray-50 transition-colors">
                        <i class="fas fa-layer-group text-gray-700"></i>
                    </button>
                </div>
                
                <!-- Map legend -->
                <div class="absolute top-4 left-4 bg-white bg-opacity-90 rounded-lg p-3 text-xs">
                    <div class="flex items-center mb-1">
                        <div class="w-3 h-3 bg-navy rounded-full mr-2"></div>
                        <span>Ports</span>
                    </div>
                    <div class="flex items-center mb-1">
                        <div class="w-3 h-3 bg-ocean-teal rounded-full mr-2"></div>
                        <span>Restaurants</span>
                    </div>
                    <div class="flex items-center">
                        <div class="w-3 h-3 bg-green-600 rounded-full mr-2"></div>
                        <span>Shopping</span>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- @END_COMPONENT: MapSection -->

    <!-- @COMPONENT: CategorySection [activity categories] -->
    <section class="py-8 px-4">
        <div class="max-w-7xl mx-auto">
            <h3 class="text-xl font-semibold text-gray-900 mb-6">Popular Shore Leave Activities</h3>
            
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
                <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-100 hover:shadow-md transition-shadow cursor-pointer">
                    <div class="text-center">
                        <i class="fas fa-utensils text-2xl text-ocean-teal mb-2"></i>
                        <p class="font-medium text-gray-900">Restaurants</p>
                        <p class="text-xs text-gray-500">Local cuisine</p>
                    </div>
                </div>
                
                <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-100 hover:shadow-md transition-shadow cursor-pointer">
                    <div class="text-center">
                        <i class="fas fa-shopping-bag text-2xl text-green-600 mb-2"></i>
                        <p class="font-medium text-gray-900">Shopping</p>
                        <p class="text-xs text-gray-500">Supplies & gifts</p>
                    </div>
                </div>
                
                <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-100 hover:shadow-md transition-shadow cursor-pointer">
                    <div class="text-center">
                        <i class="fas fa-shield-alt text-2xl text-navy mb-2"></i>
                        <p class="font-medium text-gray-900">Safety</p>
                        <p class="text-xs text-gray-500">Safe areas</p>
                    </div>
                </div>
                
                <div class="bg-white rounded-xl p-4 shadow-sm border border-gray-100 hover:shadow-md transition-shadow cursor-pointer">
                    <div class="text-center">
                        <i class="fas fa-bus text-2xl text-purple-600 mb-2"></i>
                        <p class="font-medium text-gray-900">Transport</p>
                        <p class="text-xs text-gray-500">Get around</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- @END_COMPONENT: CategorySection -->

    <!-- @COMPONENT: FeaturedExperiences [shore leave activities showcase] -->
    <section class="py-8 px-4 bg-gray-50">
        <div class="max-w-7xl mx-auto">
            <h3 class="text-xl font-semibold text-gray-900 mb-6">Featured Shore Leave Experiences</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <!-- Sailors exploring city -->
                <!-- Group of sailors walking through a vibrant European port city with colorful buildings -->
                <div class="bg-white rounded-xl overflow-hidden shadow-sm border border-gray-100 hover:shadow-md transition-shadow" data-mock="true">
                    <img src="https://images.unsplash.com/photo-1526495124232-a04e1849168c?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=400&h=250" alt="Sailors exploring European port city" class="w-full h-48 object-cover">
                    <div class="p-4">
                        <h4 class="font-semibold text-gray-900 mb-2">Hamburg Port District Walking Tour</h4>
                        <p class="text-sm text-gray-600 mb-3">Explore historic Speicherstadt and modern HafenCity with fellow crew members</p>
                        <div class="flex items-center justify-between">
                            <span class="text-ocean-teal font-medium">Free</span>
                            <div class="flex items-center text-sm text-gray-500">
                                <i class="fas fa-star text-yellow-400 mr-1"></i>
                                <span>4.8 (127 reviews)</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Port leisure experience -->
                <!-- Modern port terminal with comfortable seating areas and WiFi zones for crew relaxation -->
                <div class="bg-white rounded-xl overflow-hidden shadow-sm border border-gray-100 hover:shadow-md transition-shadow" data-mock="true">
                    <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=400&h=250" alt="Modern port terminal relaxation area" class="w-full h-48 object-cover">
                    <div class="p-4">
                        <h4 class="font-semibold text-gray-900 mb-2">Singapore Port Crew Lounge</h4>
                        <p class="text-sm text-gray-600 mb-3">24/7 WiFi, charging stations, and comfortable seating near terminal gates</p>
                        <div class="flex items-center justify-between">
                            <span class="text-ocean-teal font-medium">$8/day</span>
                            <div class="flex items-center text-sm text-gray-500">
                                <i class="fas fa-star text-yellow-400 mr-1"></i>
                                <span>4.6 (89 reviews)</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Sailors socializing -->
                <!-- Group of diverse maritime crew members socializing at a waterfront restaurant with harbor views -->
                <div class="bg-white rounded-xl overflow-hidden shadow-sm border border-gray-100 hover:shadow-md transition-shadow" data-mock="true">
                    <img src="https://images.unsplash.com/photo-1414235077428-338989a2e8c0?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=400&h=250" alt="Maritime crew socializing at waterfront restaurant" class="w-full h-48 object-cover">
                    <div class="p-4">
                        <h4 class="font-semibold text-gray-900 mb-2">Captain's Table - Rotterdam</h4>
                        <p class="text-sm text-gray-600 mb-3">Popular maritime-themed restaurant with harbor views and crew discounts</p>
                        <div class="flex items-center justify-between">
                            <span class="text-ocean-teal font-medium">$$</span>
                            <div class="flex items-center text-sm text-gray-500">
                                <i class="fas fa-star text-yellow-400 mr-1"></i>
                                <span>4.7 (203 reviews)</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Shore leave activities -->
                <!-- Sailors enjoying local market and street food in an Asian port city -->
                <div class="bg-white rounded-xl overflow-hidden shadow-sm border border-gray-100 hover:shadow-md transition-shadow" data-mock="true">
                    <img src="https://images.unsplash.com/photo-1555396273-367ea4eb4db5?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=400&h=250" alt="Sailors exploring Asian street market" class="w-full h-48 object-cover">
                    <div class="p-4">
                        <h4 class="font-semibold text-gray-900 mb-2">Chinatown Food Tour - Singapore</h4>
                        <p class="text-sm text-gray-600 mb-3">Guided food tour through authentic hawker centers and traditional markets</p>
                        <div class="flex items-center justify-between">
                            <span class="text-ocean-teal font-medium">$25</span>
                            <div class="flex items-center text-sm text-gray-500">
                                <i class="fas fa-star text-yellow-400 mr-1"></i>
                                <span>4.9 (156 reviews)</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Port leisure experience -->
                <!-- Crew members using transportation hub with clear signage and maritime shuttle services -->
                <div class="bg-white rounded-xl overflow-hidden shadow-sm border border-gray-100 hover:shadow-md transition-shadow" data-mock="true">
                    <img src="https://images.unsplash.com/photo-1544620347-c4fd4a3d5957?ixlib=rb-4.0.3&ixid=MnwxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8&auto=format&fit=crop&w=400&h=250" alt="Port transportation hub and shuttle services" class="w-full h-48 object-cover">
                    <div class="p-4">
                        <h4 class="font-semibold text-gray-900 mb-2">Port Shuttle Service - Dubai</h4>
                        <p class="text-sm text-gray-600 mb-3">24/7 shuttle service between terminal and city center with crew discounts</p>
                        <div class="flex items-center justify-between">
                            <span class="text-ocean-teal font-medium">$5 one-way</span>
                            <div class="flex items-center text-sm text-gray-500">
                                <i class="fas fa-star text-yellow-400 mr-1"></i>
                                <span>4.5 (342 reviews)</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Maritime crew socializing -->
                <!-- Diverse group of ship officers and crew sharing drinks at a maritime-themed pub near the harbor -->
                <div class="bg-white rounded-xl overflow-hidden shadow-sm border border-gray-100 hover:shadow-md transition-shadow" data-mock="true">
                    <img src="https://pixabay.com/get/g08e1c866342e2b4fa85b379a0e0fb3dc2123452d3ee5e84a1f126e0fb1f7b00196386c33e50af71dc532fde3b4319f430582727c79a3d12a50e41af7a13f86ac_1280.jpg" alt="Maritime crew at harbor-side pub" class="w-full h-48 object-cover">
                    <div class="p-4">
                        <h4 class="font-semibold text-gray-900 mb-2">The Anchor Pub - Liverpool</h4>
                        <p class="text-sm text-gray-600 mb-3">Historic sailors' pub with maritime memorabilia and live music weekends</p>
                        <div class="flex items-center justify-between">
                            <span class="text-ocean-teal font-medium">$$</span>
                            <div class="flex items-center text-sm text-gray-500">
                                <i class="fas fa-star text-yellow-400 mr-1"></i>
                                <span>4.4 (178 reviews)</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- @END_COMPONENT: FeaturedExperiences -->

    <!-- @COMPONENT: QuickAccess [frequently needed services] -->
    <section class="py-8 px-4">
        <div class="max-w-7xl mx-auto">
            <h3 class="text-xl font-semibold text-gray-900 mb-6">Quick Access</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <!-- Emergency Services -->
                <div class="bg-red-50 border border-red-200 rounded-xl p-6">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-exclamation-triangle text-red-600 text-xl mr-3"></i>
                        <h4 class="font-semibold text-red-900">Emergency Services</h4>
                    </div>
                    <p class="text-sm text-red-700 mb-4">Quick access to local emergency numbers and nearest hospitals</p>
                    <button class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                        View Emergency Info
                    </button>
                </div>

                <!-- SIM Cards & Internet -->
                <div class="bg-blue-50 border border-blue-200 rounded-xl p-6">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-sim-card text-blue-600 text-xl mr-3"></i>
                        <h4 class="font-semibold text-blue-900">SIM Cards & Internet</h4>
                    </div>
                    <p class="text-sm text-blue-700 mb-4">Find the best local SIM cards and WiFi spots near your port</p>
                    <button class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                        Find SIM Cards
                    </button>
                </div>

                <!-- Currency Exchange -->
                <div class="bg-green-50 border border-green-200 rounded-xl p-6">
                    <div class="flex items-center mb-4">
                        <i class="fas fa-exchange-alt text-green-600 text-xl mr-3"></i>
                        <h4 class="font-semibold text-green-900">Currency Exchange</h4>
                    </div>
                    <p class="text-sm text-green-700 mb-4">Best exchange rates and ATM locations with crew-friendly fees</p>
                    <button class="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors">
                        Exchange Rates
                    </button>
                </div>
            </div>
        </div>
    </section>
    <!-- @END_COMPONENT: QuickAccess -->

    <!-- @COMPONENT: QAAQIntegration [connection to main app] -->
    <section class="py-8 px-4 bg-navy text-white">
        <div class="max-w-4xl mx-auto text-center">
            <h3 class="text-2xl font-bold mb-4">Need Technical Help?</h3>
            <p class="text-blue-200 mb-6">For machinery questions and technical troubleshooting, ask our SEMM database through QAAQ</p>
            
            <div class="bg-blue-900 bg-opacity-50 rounded-xl p-6 mb-6">
                <div class="flex items-center justify-center mb-4">
                    <i class="fas fa-cogs text-3xl text-blue-300 mr-4"></i>
                    <div class="text-left">
                        <h4 class="font-semibold">QAAQ Technical Support</h4>
                        <p class="text-sm text-blue-200">Marine engineering Q&A platform</p>
                    </div>
                </div>
                <p class="text-sm text-blue-200 mb-4">
                    "My Mitsubishi main engine is showing high exhaust temperature..."<br>
                    "Alfa Laval purifier keeps stopping - troubleshooting tips?"
                </p>
                <button class="bg-white text-navy px-6 py-2 rounded-lg font-medium hover:bg-gray-100 transition-colors">
                    Ask Technical Question
                </button>
            </div>
            
            <div class="flex items-center justify-center space-x-6 text-sm">
                <div class="flex items-center">
                    <i class="fas fa-check-circle text-green-400 mr-2"></i>
                    <span>2,847 Questions Answered</span>
                </div>
                <div class="flex items-center">
                    <i class="fas fa-users text-blue-300 mr-2"></i>
                    <span>1,203 Maritime Engineers</span>
                </div>
            </div>
        </div>
    </section>
    <!-- @END_COMPONENT: QAAQIntegration -->

    <!-- @COMPONENT: BottomNavigation [mobile navigation] -->
    <nav class="bg-white border-t border-gray-200 sticky bottom-0 z-50 md:hidden">
        <div class="grid grid-cols-5 h-16">
            <button class="flex flex-col items-center justify-center text-ocean-teal">
                <i class="fas fa-home text-lg mb-1"></i>
                <span class="text-xs">Home</span>
            </button>
            <button class="flex flex-col items-center justify-center text-gray-500">
                <i class="fas fa-map-marker-alt text-lg mb-1"></i>
                <span class="text-xs">Map</span>
            </button>
            <button class="flex flex-col items-center justify-center text-gray-500">
                <i class="fas fa-search text-lg mb-1"></i>
                <span class="text-xs">Search</span>
            </button>
            <button class="flex flex-col items-center justify-center text-gray-500">
                <i class="fas fa-bookmark text-lg mb-1"></i>
                <span class="text-xs">Saved</span>
            </button>
            <button class="flex flex-col items-center justify-center text-gray-500">
                <i class="fas fa-user text-lg mb-1"></i>
                <span class="text-xs">Profile</span>
            </button>
        </div>
    </nav>
    <!-- @END_COMPONENT: BottomNavigation -->

    <!-- @COMPONENT: FloatingActionButton [quick search access] -->
    <button class="fixed bottom-20 right-4 md:bottom-6 md:right-6 bg-ocean-teal hover:bg-ocean-light text-white w-14 h-14 rounded-full shadow-lg flex items-center justify-center z-40 transition-colors">
        <i class="fas fa-plus text-xl"></i>
    </button>
    <!-- @END_COMPONENT: FloatingActionButton -->

    <script>
        // TODO: Implement real map integration (Google Maps, Mapbox, etc.)
        // TODO: Implement search functionality with QAAQ API integration
        // TODO: Add geolocation services for user positioning
        // TODO: Implement real-time data fetching for nearby services
        // TODO: Add user authentication and profile management
        // TODO: Implement route planning and navigation features
        // TODO: Add review and rating system
        // TODO: Implement push notifications for relevant updates
        
        (function() {
            // Simple interactivity for the mockup
            const searchInput = document.querySelector('input[placeholder*="shore leave"]');
            const mapControls = document.querySelectorAll('.fas.fa-crosshairs, .fas.fa-layer-group');
            const radiusControls = document.querySelectorAll('.fas.fa-minus, .fas.fa-plus');
            
            // Search input focus effect
            if (searchInput) {
                searchInput.addEventListener('focus', function() {
                    this.parentElement.classList.add('ring-2', 'ring-ocean-teal');
                });
                
                searchInput.addEventListener('blur', function() {
                    this.parentElement.classList.remove('ring-2', 'ring-ocean-teal');
                });
            }
            
            // Map control hover effects
            mapControls.forEach(control => {
                control.addEventListener('click', function() {
                    // TODO: Implement actual map control functionality
                    this.parentElement.classList.add('bg-gray-100');
                    setTimeout(() => {
                        this.parentElement.classList.remove('bg-gray-100');
                    }, 150);
                });
            });
            
            // Radius control functionality
            const radiusDisplay = document.querySelector('span:contains("50km")');
            let currentRadius = 50;
            
            radiusControls.forEach(control => {
                control.addEventListener('click', function() {
                    if (this.classList.contains('fa-minus') && currentRadius > 10) {
                        currentRadius -= 10;
                    } else if (this.classList.contains('fa-plus') && currentRadius < 100) {
                        currentRadius += 10;
                    }
                    
                    if (radiusDisplay) {
                        radiusDisplay.textContent = currentRadius + 'km';
                    }
                    
                    // TODO: Update map radius based on new value
                });
            });
        })();
    </script>
</body>
</html>