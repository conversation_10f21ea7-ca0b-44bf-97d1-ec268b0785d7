<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>1234 Koi Hai? - Maritime Discovery Platform</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'maritime-navy': '#1e3a8a',
                        'ocean-teal': '#0891b2',
                        'light-teal': '#67e8f9',
                        'maritime-gray': '#64748b'
                    },
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif']
                    }
                }
            }
        };
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body class="font-inter bg-slate-50 text-slate-800">
    
    <!-- @COMPONENT: Header [Main navigation and branding] -->
    <header class="bg-white shadow-md sticky top-0 z-50">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <h1 class="text-2xl font-bold text-maritime-navy">1234 Koi Hai?</h1>
                        <p class="text-xs text-ocean-teal font-medium">Maritime Discovery</p>
                    </div>
                </div>
                <div class="hidden md:block">
                    <div class="ml-10 flex items-baseline space-x-4">
                        <a href="#" class="text-maritime-gray hover:text-maritime-navy px-3 py-2 rounded-md text-sm font-medium">Discover</a>
                        <a href="#" class="text-maritime-gray hover:text-maritime-navy px-3 py-2 rounded-md text-sm font-medium">CPSS Tree</a>
                        <a href="#" class="text-maritime-gray hover:text-maritime-navy px-3 py-2 rounded-md text-sm font-medium">Profile</a>
                    </div>
                </div>
                <div class="md:hidden">
                    <button class="inline-flex items-center justify-center p-2 rounded-md text-maritime-gray hover:text-maritime-navy focus:outline-none">
                        <i class="fas fa-bars text-xl"></i>
                    </button>
                </div>
            </div>
        </div>
    </header>
    <!-- @END_COMPONENT: Header -->

    <!-- @COMPONENT: QAAQSearchBar [AI-powered search interface] -->
    <div class="bg-gradient-to-r from-maritime-navy to-ocean-teal">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="text-center mb-6">
                <h2 class="text-3xl font-bold text-white mb-2">QAAQ AI Question Bar</h2>
                <p class="text-light-teal text-lg">Ask location-based questions about ports, shore leave, and services</p>
            </div>
            
            <!-- AI Search Interface -->
            <div class="max-w-4xl mx-auto">
                <div class="bg-white rounded-xl shadow-lg p-6 mb-4">
                    <div class="flex items-center space-x-4">
                        <div class="flex-1">
                            <input type="text" 
                                   placeholder="Ask AI: 'Best SIM card shops in Hamburg port?' or 'Cheap restaurants near Rotterdam terminal?'"
                                   class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-ocean-teal focus:border-transparent text-lg"
                                   data-bind="searchQuery"
                                   data-mock="true">
                        </div>
                        <button class="bg-ocean-teal hover:bg-maritime-navy text-white px-6 py-3 rounded-lg font-semibold transition-colors duration-200"
                                data-event="click:handleAISearch">
                            <i class="fas fa-search mr-2"></i>Ask AI
                        </button>
                    </div>
                </div>

                <!-- CPSS Breadcrumb Navigation -->
                <div class="bg-white/10 backdrop-blur-md rounded-lg p-4">
                    <div class="flex items-center space-x-2 text-white text-sm">
                        <span class="text-light-teal font-semibold">CPSS:</span>
                        <a href="#" class="hover:text-light-teal transition-colors">Country</a>
                        <i class="fas fa-chevron-right text-light-teal text-xs"></i>
                        <a href="#" class="hover:text-light-teal transition-colors">Port</a>
                        <i class="fas fa-chevron-right text-light-teal text-xs"></i>
                        <a href="#" class="hover:text-light-teal transition-colors">Shore Leave</a>
                        <i class="fas fa-chevron-right text-light-teal text-xs"></i>
                        <span class="text-light-teal">Service</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- @END_COMPONENT: QAAQSearchBar -->

    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        <!-- @COMPONENT: DiscoverySection [Officers and crew nearby discovery] -->
        <section class="mb-12">
            <div class="grid lg:grid-cols-2 gap-8">
                
                <!-- Officers Nearby -->
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-2xl font-bold text-maritime-navy flex items-center">
                            <i class="fas fa-users text-ocean-teal mr-3"></i>
                            Officers Nearby
                        </h3>
                        <div class="flex items-center space-x-2">
                            <span class="text-sm text-maritime-gray">Distance:</span>
                            <input type="range" min="1" max="50" value="10" class="w-20 accent-ocean-teal" data-bind="distanceRange">
                            <span class="text-sm font-semibold text-ocean-teal" data-bind="selectedDistance">10km</span>
                        </div>
                    </div>

                    <!-- @MAP: nearbyOfficers.map(officer => ( -->
                    <div class="space-y-4">
                        <div class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-ocean-teal transition-colors cursor-pointer" data-mock="true">
                            <div class="w-12 h-12 bg-gradient-to-br from-maritime-navy to-ocean-teal rounded-full flex items-center justify-center text-white font-bold text-lg mr-4">
                                CG
                            </div>
                            <div class="flex-1">
                                <h4 class="font-semibold text-maritime-navy" data-bind="officer.name">Capt. Gupta</h4>
                                <p class="text-sm text-maritime-gray" data-bind="officer.rank">Master Mariner</p>
                                <p class="text-sm text-ocean-teal font-medium" data-bind="officer.vessel">MV Ocean Explorer</p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm text-maritime-gray" data-bind="officer.distance">2.3 km</p>
                                <button class="mt-2 bg-ocean-teal text-white px-4 py-1 rounded-full text-sm hover:bg-maritime-navy transition-colors"
                                        data-event="click:sayHello">
                                    Say Hello
                                </button>
                            </div>
                        </div>

                        <div class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-ocean-teal transition-colors cursor-pointer" data-mock="true">
                            <div class="w-12 h-12 bg-gradient-to-br from-maritime-navy to-ocean-teal rounded-full flex items-center justify-center text-white font-bold text-lg mr-4">
                                AS
                            </div>
                            <div class="flex-1">
                                <h4 class="font-semibold text-maritime-navy">Arjun Singh</h4>
                                <p class="text-sm text-maritime-gray">Chief Engineer</p>
                                <p class="text-sm text-ocean-teal font-medium">MT Neptune Star</p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm text-maritime-gray">4.7 km</p>
                                <button class="mt-2 bg-ocean-teal text-white px-4 py-1 rounded-full text-sm hover:bg-maritime-navy transition-colors">
                                    Say Hello
                                </button>
                            </div>
                        </div>

                        <div class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-ocean-teal transition-colors cursor-pointer" data-mock="true">
                            <div class="w-12 h-12 bg-gradient-to-br from-maritime-navy to-ocean-teal rounded-full flex items-center justify-center text-white font-bold text-lg mr-4">
                                RK
                            </div>
                            <div class="flex-1">
                                <h4 class="font-semibold text-maritime-navy">Raj Kumar</h4>
                                <p class="text-sm text-maritime-gray">2nd Engineer</p>
                                <p class="text-sm text-ocean-teal font-medium">MV Baltic Carrier</p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm text-maritime-gray">6.1 km</p>
                                <button class="mt-2 bg-ocean-teal text-white px-4 py-1 rounded-full text-sm hover:bg-maritime-navy transition-colors">
                                    Say Hello
                                </button>
                            </div>
                        </div>
                    </div>
                    <!-- @END_MAP )) -->

                    <div class="mt-6 text-center">
                        <button class="text-ocean-teal hover:text-maritime-navy font-medium text-sm"
                                data-event="click:loadMoreOfficers">
                            View More Officers <i class="fas fa-chevron-down ml-1"></i>
                        </button>
                    </div>
                </div>

                <!-- Crew Nearby -->
                <div class="bg-white rounded-xl shadow-lg p-6">
                    <div class="flex items-center justify-between mb-6">
                        <h3 class="text-2xl font-bold text-maritime-navy flex items-center">
                            <i class="fas fa-anchor text-ocean-teal mr-3"></i>
                            Crew Nearby
                        </h3>
                        <div class="flex items-center space-x-2">
                            <span class="text-sm text-maritime-gray">Vessel:</span>
                            <select class="text-sm border border-gray-300 rounded px-2 py-1 focus:ring-2 focus:ring-ocean-teal" data-bind="vesselFilter">
                                <option>All Vessels</option>
                                <option>Container Ships</option>
                                <option>Tankers</option>
                                <option>Bulk Carriers</option>
                            </select>
                        </div>
                    </div>

                    <div class="space-y-4">
                        <div class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-ocean-teal transition-colors cursor-pointer" data-mock="true">
                            <div class="w-12 h-12 bg-gradient-to-br from-ocean-teal to-light-teal rounded-full flex items-center justify-center text-white font-bold text-lg mr-4">
                                VT
                            </div>
                            <div class="flex-1">
                                <h4 class="font-semibold text-maritime-navy">Vikram Thakur</h4>
                                <p class="text-sm text-maritime-gray">Bosun</p>
                                <p class="text-sm text-ocean-teal font-medium">MV Hamburg Express</p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm text-maritime-gray">1.8 km</p>
                                <button class="mt-2 bg-ocean-teal text-white px-4 py-1 rounded-full text-sm hover:bg-maritime-navy transition-colors">
                                    Say Hello
                                </button>
                            </div>
                        </div>

                        <div class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-ocean-teal transition-colors cursor-pointer" data-mock="true">
                            <div class="w-12 h-12 bg-gradient-to-br from-ocean-teal to-light-teal rounded-full flex items-center justify-center text-white font-bold text-lg mr-4">
                                MS
                            </div>
                            <div class="flex-1">
                                <h4 class="font-semibold text-maritime-navy">Maria Santos</h4>
                                <p class="text-sm text-maritime-gray">Radio Officer</p>
                                <p class="text-sm text-ocean-teal font-medium">MT Baltic Pride</p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm text-maritime-gray">3.2 km</p>
                                <button class="mt-2 bg-ocean-teal text-white px-4 py-1 rounded-full text-sm hover:bg-maritime-navy transition-colors">
                                    Say Hello
                                </button>
                            </div>
                        </div>

                        <div class="flex items-center p-4 border border-gray-200 rounded-lg hover:border-ocean-teal transition-colors cursor-pointer" data-mock="true">
                            <div class="w-12 h-12 bg-gradient-to-br from-ocean-teal to-light-teal rounded-full flex items-center justify-center text-white font-bold text-lg mr-4">
                                AL
                            </div>
                            <div class="flex-1">
                                <h4 class="font-semibold text-maritime-navy">Ahmed Ali</h4>
                                <p class="text-sm text-maritime-gray">Deck Cadet</p>
                                <p class="text-sm text-ocean-teal font-medium">MV Rotterdam Star</p>
                            </div>
                            <div class="text-right">
                                <p class="text-sm text-maritime-gray">5.5 km</p>
                                <button class="mt-2 bg-ocean-teal text-white px-4 py-1 rounded-full text-sm hover:bg-maritime-navy transition-colors">
                                    Say Hello
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="mt-6 text-center">
                        <button class="text-ocean-teal hover:text-maritime-navy font-medium text-sm">
                            View More Crew <i class="fas fa-chevron-down ml-1"></i>
                        </button>
                    </div>
                </div>
                
            </div>
        </section>
        <!-- @END_COMPONENT: DiscoverySection -->

        <!-- @COMPONENT: CPSSTreeSection [Country > Port > Shore leave > Service navigation] -->
        <section class="mb-12">
            <div class="bg-white rounded-xl shadow-lg p-8">
                <div class="text-center mb-8">
                    <h3 class="text-3xl font-bold text-maritime-navy mb-2">CPSS Tree - Port Discovery</h3>
                    <p class="text-maritime-gray text-lg">Browse by Country → Port → Shore Leave → Services</p>
                </div>

                <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                    
                    <!-- Countries -->
                    <div class="border border-gray-200 rounded-lg p-6 hover:border-ocean-teal transition-colors cursor-pointer">
                        <h4 class="text-lg font-semibold text-maritime-navy mb-4 flex items-center">
                            <i class="fas fa-globe text-ocean-teal mr-2"></i>
                            Countries
                        </h4>
                        <ul class="space-y-2">
                            <li><a href="#" class="text-maritime-gray hover:text-ocean-teal transition-colors">🇩🇪 Germany</a></li>
                            <li><a href="#" class="text-maritime-gray hover:text-ocean-teal transition-colors">🇳🇱 Netherlands</a></li>
                            <li><a href="#" class="text-maritime-gray hover:text-ocean-teal transition-colors">🇸🇬 Singapore</a></li>
                            <li><a href="#" class="text-maritime-gray hover:text-ocean-teal transition-colors">🇦🇪 UAE</a></li>
                            <li><a href="#" class="text-ocean-teal font-medium">View All →</a></li>
                        </ul>
                    </div>

                    <!-- Ports -->
                    <div class="border border-gray-200 rounded-lg p-6 hover:border-ocean-teal transition-colors cursor-pointer">
                        <h4 class="text-lg font-semibold text-maritime-navy mb-4 flex items-center">
                            <i class="fas fa-ship text-ocean-teal mr-2"></i>
                            Popular Ports
                        </h4>
                        <ul class="space-y-2">
                            <li><a href="#" class="text-maritime-gray hover:text-ocean-teal transition-colors">Hamburg</a></li>
                            <li><a href="#" class="text-maritime-gray hover:text-ocean-teal transition-colors">Rotterdam</a></li>
                            <li><a href="#" class="text-maritime-gray hover:text-ocean-teal transition-colors">Singapore</a></li>
                            <li><a href="#" class="text-maritime-gray hover:text-ocean-teal transition-colors">Dubai</a></li>
                            <li><a href="#" class="text-ocean-teal font-medium">View All →</a></li>
                        </ul>
                    </div>

                    <!-- Shore Leave Activities -->
                    <div class="border border-gray-200 rounded-lg p-6 hover:border-ocean-teal transition-colors cursor-pointer">
                        <h4 class="text-lg font-semibold text-maritime-navy mb-4 flex items-center">
                            <i class="fas fa-umbrella-beach text-ocean-teal mr-2"></i>
                            Shore Leave
                        </h4>
                        <ul class="space-y-2">
                            <li><a href="#" class="text-maritime-gray hover:text-ocean-teal transition-colors">Restaurants</a></li>
                            <li><a href="#" class="text-maritime-gray hover:text-ocean-teal transition-colors">Shopping</a></li>
                            <li><a href="#" class="text-maritime-gray hover:text-ocean-teal transition-colors">Entertainment</a></li>
                            <li><a href="#" class="text-maritime-gray hover:text-ocean-teal transition-colors">Transportation</a></li>
                            <li><a href="#" class="text-ocean-teal font-medium">View All →</a></li>
                        </ul>
                    </div>

                    <!-- Services -->
                    <div class="border border-gray-200 rounded-lg p-6 hover:border-ocean-teal transition-colors cursor-pointer">
                        <h4 class="text-lg font-semibold text-maritime-navy mb-4 flex items-center">
                            <i class="fas fa-concierge-bell text-ocean-teal mr-2"></i>
                            Services
                        </h4>
                        <ul class="space-y-2">
                            <li><a href="#" class="text-maritime-gray hover:text-ocean-teal transition-colors">SIM Cards</a></li>
                            <li><a href="#" class="text-maritime-gray hover:text-ocean-teal transition-colors">Internet Cafes</a></li>
                            <li><a href="#" class="text-maritime-gray hover:text-ocean-teal transition-colors">Laundry</a></li>
                            <li><a href="#" class="text-maritime-gray hover:text-ocean-teal transition-colors">Banking</a></li>
                            <li><a href="#" class="text-ocean-teal font-medium">View All →</a></li>
                        </ul>
                    </div>
                    
                </div>
            </div>
        </section>
        <!-- @END_COMPONENT: CPSSTreeSection -->

        <!-- @COMPONENT: FeaturedContent [Maritime imagery and content showcase] -->
        <section class="mb-12">
            <h3 class="text-2xl font-bold text-maritime-navy mb-8 text-center">Discover Maritime Life</h3>
            
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
                
                <!-- Maritime Officers Meeting -->
                <!-- Professional maritime officers having a casual meeting at a port terminal -->
                <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                    <img src="https://images.unsplash.com/photo-**********-669a67965ba0?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600" 
                         alt="Maritime officers meeting at port" 
                         class="w-full h-48 object-cover">
                    <div class="p-6">
                        <h4 class="text-lg font-semibold text-maritime-navy mb-2">Officer Meetups</h4>
                        <p class="text-maritime-gray text-sm">Connect with fellow maritime professionals during shore leave</p>
                        <div class="mt-4 flex items-center text-ocean-teal text-sm">
                            <i class="fas fa-users mr-2"></i>
                            <span>24 officers nearby</span>
                        </div>
                    </div>
                </div>

                <!-- Ship Crew at Port Cities -->
                <!-- Ship crew members exploring a bustling port city during their break -->
                <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                    <img src="https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600" 
                         alt="Ship crew exploring port city" 
                         class="w-full h-48 object-cover">
                    <div class="p-6">
                        <h4 class="text-lg font-semibold text-maritime-navy mb-2">Port City Exploration</h4>
                        <p class="text-maritime-gray text-sm">Discover the best spots crew members recommend in port cities</p>
                        <div class="mt-4 flex items-center text-ocean-teal text-sm">
                            <i class="fas fa-map-marker-alt mr-2"></i>
                            <span>Hamburg, Germany</span>
                        </div>
                    </div>
                </div>

                <!-- Sailors Shore Leave Activities -->
                <!-- Sailors enjoying recreational activities during their shore leave time -->
                <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                    <img src="https://images.unsplash.com/photo-1544551763-46a013bb70d5?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600" 
                         alt="Sailors enjoying shore leave activities" 
                         class="w-full h-48 object-cover">
                    <div class="p-6">
                        <h4 class="text-lg font-semibold text-maritime-navy mb-2">Shore Leave Fun</h4>
                        <p class="text-maritime-gray text-sm">Find activities and entertainment recommended by maritime professionals</p>
                        <div class="mt-4 flex items-center text-ocean-teal text-sm">
                            <i class="fas fa-star mr-2"></i>
                            <span>4.8/5 rating</span>
                        </div>
                    </div>
                </div>

                <!-- Port Vendor Services -->
                <!-- Local vendors and service providers catering to maritime professionals at port -->
                <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow md:col-span-2 lg:col-span-1">
                    <img src="https://images.unsplash.com/photo-1586864387967-d02ef85d93e8?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=600" 
                         alt="Port vendors serving maritime professionals" 
                         class="w-full h-48 object-cover">
                    <div class="p-6">
                        <h4 class="text-lg font-semibold text-maritime-navy mb-2">Trusted Vendors</h4>
                        <p class="text-maritime-gray text-sm">Verified service providers offering SIM cards, supplies, and more</p>
                        <div class="mt-4 flex items-center text-ocean-teal text-sm">
                            <i class="fas fa-certificate mr-2"></i>
                            <span>Verified partners</span>
                        </div>
                    </div>
                </div>

                <!-- Additional Maritime Content -->
                <!-- Maritime professionals discussing routes and destinations at a port cafe -->
                <div class="bg-white rounded-xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow md:col-span-2">
                    <img src="https://images.unsplash.com/photo-**********-b31f71025d54?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&h=400" 
                         alt="Maritime professionals networking at port" 
                         class="w-full h-48 object-cover">
                    <div class="p-6">
                        <h4 class="text-lg font-semibold text-maritime-navy mb-2">Professional Network</h4>
                        <p class="text-maritime-gray text-sm">Build connections with maritime professionals worldwide through our platform</p>
                        <div class="mt-4 flex items-center justify-between">
                            <div class="flex items-center text-ocean-teal text-sm">
                                <i class="fas fa-network-wired mr-2"></i>
                                <span>Global network</span>
                            </div>
                            <button class="bg-ocean-teal text-white px-4 py-2 rounded-lg text-sm hover:bg-maritime-navy transition-colors">
                                Join Now
                            </button>
                        </div>
                    </div>
                </div>
                
            </div>
        </section>
        <!-- @END_COMPONENT: FeaturedContent -->

        <!-- @COMPONENT: QAIntegration [Link to main QAAQ platform] -->
        <section class="mb-12">
            <div class="bg-gradient-to-r from-maritime-navy to-ocean-teal rounded-xl p-8 text-white">
                <div class="max-w-4xl mx-auto text-center">
                    <h3 class="text-3xl font-bold mb-4">Need Technical Help?</h3>
                    <p class="text-xl text-light-teal mb-6">Connect to QAAQ - The Maritime Engineering Knowledge Hub</p>
                    <div class="bg-white/10 backdrop-blur-md rounded-lg p-6 mb-6">
                        <p class="text-lg mb-4">"Ask about ship machinery, get expert answers from marine engineers worldwide"</p>
                        <div class="flex flex-wrap justify-center gap-4 text-sm">
                            <span class="bg-white/20 px-3 py-1 rounded-full">2,847+ Questions Answered</span>
                            <span class="bg-white/20 px-3 py-1 rounded-full">1,203+ Maritime Engineers</span>
                            <span class="bg-white/20 px-3 py-1 rounded-full">156+ Machine Types</span>
                        </div>
                    </div>
                    <a href="https://qaaqit.replit.app/" 
                       class="inline-flex items-center bg-white text-maritime-navy px-8 py-3 rounded-lg font-semibold hover:bg-light-teal transition-colors">
                        <i class="fas fa-external-link-alt mr-2"></i>
                        Visit QAAQ Platform
                    </a>
                </div>
            </div>
        </section>
        <!-- @END_COMPONENT: QAIntegration -->

    </main>

    <!-- @COMPONENT: Footer [Contact and additional links] -->
    <footer class="bg-maritime-navy text-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
            <div class="grid md:grid-cols-3 gap-8">
                <div>
                    <h4 class="text-xl font-bold mb-4">1234 Koi Hai?</h4>
                    <p class="text-light-teal mb-4">Maritime Discovery Platform connecting seafarers worldwide</p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-light-teal hover:text-white transition-colors">
                            <i class="fab fa-whatsapp text-xl"></i>
                        </a>
                        <a href="#" class="text-light-teal hover:text-white transition-colors">
                            <i class="fab fa-telegram text-xl"></i>
                        </a>
                        <a href="#" class="text-light-teal hover:text-white transition-colors">
                            <i class="fab fa-linkedin text-xl"></i>
                        </a>
                    </div>
                </div>
                <div>
                    <h5 class="font-semibold mb-4">Quick Links</h5>
                    <ul class="space-y-2 text-light-teal">
                        <li><a href="#" class="hover:text-white transition-colors">Discover Officers</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Find Crew</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">CPSS Tree</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">Port Services</a></li>
                    </ul>
                </div>
                <div>
                    <h5 class="font-semibold mb-4">Contact</h5>
                    <div class="text-light-teal space-y-2">
                        <p><i class="fas fa-phone mr-2"></i>+90 ************</p>
                        <p><i class="fas fa-envelope mr-2"></i><EMAIL></p>
                        <p class="text-sm mt-4">WhatsApp bot: +905363694997</p>
                        <p class="text-sm">Grand release: 1 Aug 2025</p>
                    </div>
                </div>
            </div>
            <div class="border-t border-ocean-teal mt-8 pt-8 text-center text-light-teal">
                <p>&copy; 2025 1234 Koi Hai? Maritime Discovery Platform. Connected to QAAQ Knowledge Hub.</p>
            </div>
        </div>
    </footer>
    <!-- @END_COMPONENT: Footer -->

    <script>
        // TODO: Implement business logic, API calls, and state management
        (function() {
            // Distance slider functionality
            const distanceSlider = document.querySelector('[data-bind="distanceRange"]');
            const distanceDisplay = document.querySelector('[data-bind="selectedDistance"]');
            
            if (distanceSlider && distanceDisplay) {
                distanceSlider.addEventListener('input', function() {
                    distanceDisplay.textContent = this.value + 'km';
                });
            }

            // Mobile menu toggle
            const mobileMenuButton = document.querySelector('.md\\:hidden button');
            if (mobileMenuButton) {
                mobileMenuButton.addEventListener('click', function() {
                    // TODO: Implement mobile menu toggle
                    console.log('Mobile menu toggle clicked');
                });
            }

            // Say Hello button interactions
            document.querySelectorAll('[data-event="click:sayHello"]').forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();
                    // TODO: Implement say hello functionality
                    this.textContent = 'Message Sent!';
                    this.classList.add('bg-green-500');
                    setTimeout(() => {
                        this.textContent = 'Say Hello';
                        this.classList.remove('bg-green-500');
                    }, 2000);
                });
            });

            // AI Search functionality
            const searchButton = document.querySelector('[data-event="click:handleAISearch"]');
            if (searchButton) {
                searchButton.addEventListener('click', function() {
                    // TODO: Implement AI search integration with QAAQ
                    console.log('AI search triggered');
                });
            }
        })();
    </script>

</body>
</html>