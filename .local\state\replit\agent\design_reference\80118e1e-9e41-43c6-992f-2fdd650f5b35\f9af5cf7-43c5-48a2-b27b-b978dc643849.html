<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>1234 Koi Hai? - Maritime Networking</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'navy': {
                            600: '#1e3a8a',
                            700: '#1e40af',
                            800: '#1e40af',
                            900: '#1e3a8a'
                        },
                        'ocean': {
                            400: '#67e8f9',
                            500: '#0891b2',
                            600: '#0e7490'
                        }
                    },
                    fontFamily: {
                        'sans': ['Inter', 'system-ui', 'sans-serif']
                    }
                }
            }
        };
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="bg-slate-50 font-sans">
    <!-- @COMPONENT: NavigationHeader -->
    <header class="bg-navy-600 text-white shadow-lg">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between items-center h-16">
                <div class="flex items-center space-x-3">
                    <i class="fas fa-anchor text-ocean-400 text-2xl"></i>
                    <div>
                        <h1 class="text-xl font-bold">1234 Koi Hai?</h1>
                        <p class="text-xs text-ocean-400">Maritime Networking</p>
                    </div>
                </div>
                <div class="flex items-center space-x-4">
                    <button class="text-ocean-400 hover:text-white transition-colors">
                        <i class="fas fa-bell text-lg"></i>
                    </button>
                    <div class="w-8 h-8 bg-ocean-500 rounded-full flex items-center justify-center">
                        <i class="fas fa-user text-white text-sm"></i>
                    </div>
                </div>
            </div>
        </div>
    </header>
    <!-- @END_COMPONENT: NavigationHeader -->

    <!-- @COMPONENT: MainContent -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div class="flex flex-col lg:flex-row gap-8">
            
            <!-- @COMPONENT: SidebarFilters -->
            <aside class="lg:w-80 space-y-6">
                <!-- Location Controls -->
                <div class="bg-white rounded-xl shadow-sm p-6 border border-slate-200">
                    <h3 class="text-lg font-semibold text-slate-800 mb-4 flex items-center">
                        <i class="fas fa-map-marker-alt text-ocean-500 mr-2"></i>
                        Location Range
                    </h3>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-slate-700 mb-2">Distance: <span id="distance-value" data-bind="distanceRange">50km</span></label>
                            <input type="range" min="1" max="5000" value="50" 
                                   class="w-full h-2 bg-slate-200 rounded-lg appearance-none cursor-pointer slider"
                                   id="distance-slider" data-event="input:updateDistance">
                            <div class="flex justify-between text-xs text-slate-500 mt-1">
                                <span>1km</span>
                                <span>5000km</span>
                            </div>
                        </div>
                        <div class="flex flex-wrap gap-2">
                            <button class="px-3 py-1 text-xs bg-ocean-100 text-ocean-600 rounded-full hover:bg-ocean-200 transition-colors" data-range="10">Port (10km)</button>
                            <button class="px-3 py-1 text-xs bg-ocean-100 text-ocean-600 rounded-full hover:bg-ocean-200 transition-colors" data-range="50">City (50km)</button>
                            <button class="px-3 py-1 text-xs bg-ocean-100 text-ocean-600 rounded-full hover:bg-ocean-200 transition-colors" data-range="500">Region (500km)</button>
                            <button class="px-3 py-1 text-xs bg-ocean-100 text-ocean-600 rounded-full hover:bg-ocean-200 transition-colors" data-range="5000">Country (5000km)</button>
                        </div>
                    </div>
                </div>

                <!-- Filter Options -->
                <div class="bg-white rounded-xl shadow-sm p-6 border border-slate-200">
                    <h3 class="text-lg font-semibold text-slate-800 mb-4 flex items-center">
                        <i class="fas fa-filter text-ocean-500 mr-2"></i>
                        Filters
                    </h3>
                    <div class="space-y-3">
                        <label class="flex items-center cursor-pointer">
                            <input type="checkbox" class="form-checkbox h-4 w-4 text-ocean-500 rounded" data-filter="officers">
                            <span class="ml-3 text-sm text-slate-700">Officers only</span>
                        </label>
                        <label class="flex items-center cursor-pointer">
                            <input type="checkbox" class="form-checkbox h-4 w-4 text-ocean-500 rounded" data-filter="crew">
                            <span class="ml-3 text-sm text-slate-700">Crew only</span>
                        </label>
                        <label class="flex items-center cursor-pointer">
                            <input type="checkbox" class="form-checkbox h-4 w-4 text-ocean-500 rounded" data-filter="nationality">
                            <span class="ml-3 text-sm text-slate-700">Same nationality</span>
                        </label>
                    </div>
                </div>

                <!-- QAAQ Integration -->
                <div class="bg-gradient-to-br from-navy-600 to-ocean-600 rounded-xl shadow-sm p-6 text-white">
                    <h3 class="text-lg font-semibold mb-2 flex items-center">
                        <i class="fas fa-brain text-ocean-400 mr-2"></i>
                        QAAQ AI Assistant
                    </h3>
                    <p class="text-sm text-ocean-100 mb-4">Get instant answers to maritime technical questions</p>
                    <div class="relative">
                        <input type="text" placeholder="Ask about engines, systems, equipment..." 
                               class="w-full px-4 py-2 rounded-lg bg-white/10 text-white placeholder-ocean-200 border border-white/20 focus:border-ocean-400 focus:outline-none">
                        <button class="absolute right-2 top-2 text-ocean-400 hover:text-white">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                    <a href="https://qaaqit.replit.app/" target="_blank" class="inline-block mt-3 text-xs text-ocean-400 hover:text-white transition-colors">
                        Visit QAAQ Platform →
                    </a>
                </div>
            </aside>
            <!-- @END_COMPONENT: SidebarFilters -->

            <!-- @COMPONENT: MainDiscoveryArea -->
            <main class="flex-1">
                <!-- Hero Section -->
                <div class="text-center mb-8">
                    <!-- Maritime-themed banner image -->
                    <!-- A group of diverse maritime professionals networking at a port with ships in background -->
                    <div class="relative h-48 rounded-2xl overflow-hidden mb-6" 
                         style="background-image: url('https://images.unsplash.com/photo-1578662996442-48f60103fc96?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&h=400'); background-size: cover; background-position: center;">
                        <div class="absolute inset-0 bg-navy-600/70 flex items-center justify-center">
                            <div class="text-center text-white">
                                <h2 class="text-4xl font-bold mb-2">Who's There?</h2>
                                <p class="text-xl text-ocean-400">Discover maritime professionals around you</p>
                            </div>
                        </div>
                    </div>

                    <!-- Main Discovery Button -->
                    <button class="bg-gradient-to-r from-ocean-500 to-ocean-600 hover:from-ocean-600 hover:to-ocean-700 text-white px-12 py-4 rounded-2xl text-xl font-semibold shadow-lg hover:shadow-xl transform hover:scale-105 transition-all duration-200 flex items-center justify-center mx-auto"
                            data-event="click:discoverSailors" data-implementation="Should trigger discovery with current filter settings">
                        <i class="fas fa-compass mr-3 text-2xl"></i>
                        Who's There?
                    </button>
                    <p class="text-slate-600 mt-3 text-sm">Find sailors, officers, and maritime professionals in your area</p>
                </div>

                <!-- @STATE: isLoading:boolean = false, sailors:array = [], showResults:boolean = false -->
                
                <!-- Loading State -->
                <div class="hidden" id="loading-state" data-bind="isLoading">
                    <div class="flex items-center justify-center py-12">
                        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-ocean-500"></div>
                        <span class="ml-3 text-slate-600">Discovering sailors nearby...</span>
                    </div>
                </div>

                <!-- Results Grid -->
                <!-- @MAP: sailors.map(sailor => ( -->
                <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6" id="results-grid">
                    <!-- Mock Profile Cards -->
                    <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-6 hover:shadow-md transition-shadow" data-mock="true">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-gradient-to-br from-navy-500 to-ocean-500 rounded-full flex items-center justify-center text-white font-semibold">
                                    PG
                                </div>
                                <div>
                                    <h4 class="font-semibold text-slate-800">Piyush Gupta</h4>
                                    <p class="text-sm text-slate-600">Chief Engineer</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <img src="https://flagcdn.com/w20/in.png" alt="India" class="w-5 h-3">
                                <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                            </div>
                        </div>
                        <div class="space-y-2 text-sm">
                            <div class="flex items-center text-slate-600">
                                <i class="fas fa-ship w-4 mr-2 text-ocean-500"></i>
                                <span>MT Solar Claire</span>
                            </div>
                            <div class="flex items-center text-slate-600">
                                <i class="fas fa-map-marker-alt w-4 mr-2 text-ocean-500"></i>
                                <span>2.3 km away</span>
                            </div>
                            <div class="flex items-center text-slate-600">
                                <i class="fas fa-anchor w-4 mr-2 text-ocean-500"></i>
                                <span>In Port - Mumbai</span>
                            </div>
                        </div>
                        <div class="mt-4 pt-4 border-t border-slate-100 flex space-x-2">
                            <button class="flex-1 bg-ocean-500 text-white py-2 px-4 rounded-lg text-sm font-medium hover:bg-ocean-600 transition-colors">
                                Connect
                            </button>
                            <button class="flex-1 border border-slate-300 text-slate-700 py-2 px-4 rounded-lg text-sm font-medium hover:bg-slate-50 transition-colors">
                                Message
                            </button>
                        </div>
                    </div>

                    <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-6 hover:shadow-md transition-shadow" data-mock="true">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-gradient-to-br from-navy-500 to-ocean-500 rounded-full flex items-center justify-center text-white font-semibold">
                                    RS
                                </div>
                                <div>
                                    <h4 class="font-semibold text-slate-800">Raj Singh</h4>
                                    <p class="text-sm text-slate-600">2nd Officer</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <img src="https://flagcdn.com/w20/in.png" alt="India" class="w-5 h-3">
                                <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                            </div>
                        </div>
                        <div class="space-y-2 text-sm">
                            <div class="flex items-center text-slate-600">
                                <i class="fas fa-ship w-4 mr-2 text-ocean-500"></i>
                                <span>MV Ocean Pride</span>
                            </div>
                            <div class="flex items-center text-slate-600">
                                <i class="fas fa-map-marker-alt w-4 mr-2 text-ocean-500"></i>
                                <span>5.7 km away</span>
                            </div>
                            <div class="flex items-center text-slate-600">
                                <i class="fas fa-anchor w-4 mr-2 text-ocean-500"></i>
                                <span>Anchored - Mumbai Bay</span>
                            </div>
                        </div>
                        <div class="mt-4 pt-4 border-t border-slate-100 flex space-x-2">
                            <button class="flex-1 bg-ocean-500 text-white py-2 px-4 rounded-lg text-sm font-medium hover:bg-ocean-600 transition-colors">
                                Connect
                            </button>
                            <button class="flex-1 border border-slate-300 text-slate-700 py-2 px-4 rounded-lg text-sm font-medium hover:bg-slate-50 transition-colors">
                                Message
                            </button>
                        </div>
                    </div>

                    <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-6 hover:shadow-md transition-shadow" data-mock="true">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-gradient-to-br from-navy-500 to-ocean-500 rounded-full flex items-center justify-center text-white font-semibold">
                                    MC
                                </div>
                                <div>
                                    <h4 class="font-semibold text-slate-800">Maria Cruz</h4>
                                    <p class="text-sm text-slate-600">Bosun</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <img src="https://flagcdn.com/w20/ph.png" alt="Philippines" class="w-5 h-3">
                                <div class="w-2 h-2 bg-yellow-400 rounded-full"></div>
                            </div>
                        </div>
                        <div class="space-y-2 text-sm">
                            <div class="flex items-center text-slate-600">
                                <i class="fas fa-ship w-4 mr-2 text-ocean-500"></i>
                                <span>MV Magsaysay</span>
                            </div>
                            <div class="flex items-center text-slate-600">
                                <i class="fas fa-map-marker-alt w-4 mr-2 text-ocean-500"></i>
                                <span>12.1 km away</span>
                            </div>
                            <div class="flex items-center text-slate-600">
                                <i class="fas fa-route w-4 mr-2 text-ocean-500"></i>
                                <span>Departing Tomorrow</span>
                            </div>
                        </div>
                        <div class="mt-4 pt-4 border-t border-slate-100 flex space-x-2">
                            <button class="flex-1 bg-ocean-500 text-white py-2 px-4 rounded-lg text-sm font-medium hover:bg-ocean-600 transition-colors">
                                Connect
                            </button>
                            <button class="flex-1 border border-slate-300 text-slate-700 py-2 px-4 rounded-lg text-sm font-medium hover:bg-slate-50 transition-colors">
                                Message
                            </button>
                        </div>
                    </div>

                    <!-- International sailors meeting - group profile card -->
                    <!-- An international crew meeting at a port cafe with flags from different countries -->
                    <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-6 hover:shadow-md transition-shadow" data-mock="true">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-gradient-to-br from-navy-500 to-ocean-500 rounded-full flex items-center justify-center text-white font-semibold">
                                    JA
                                </div>
                                <div>
                                    <h4 class="font-semibold text-slate-800">João Almeida</h4>
                                    <p class="text-sm text-slate-600">AB Seaman</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <img src="https://flagcdn.com/w20/br.png" alt="Brazil" class="w-5 h-3">
                                <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                            </div>
                        </div>
                        <div class="space-y-2 text-sm">
                            <div class="flex items-center text-slate-600">
                                <i class="fas fa-ship w-4 mr-2 text-ocean-500"></i>
                                <span>MV Santos Express</span>
                            </div>
                            <div class="flex items-center text-slate-600">
                                <i class="fas fa-map-marker-alt w-4 mr-2 text-ocean-500"></i>
                                <span>8.5 km away</span>
                            </div>
                            <div class="flex items-center text-slate-600">
                                <i class="fas fa-anchor w-4 mr-2 text-ocean-500"></i>
                                <span>Loading Cargo</span>
                            </div>
                        </div>
                        <div class="mt-4 pt-4 border-t border-slate-100 flex space-x-2">
                            <button class="flex-1 bg-ocean-500 text-white py-2 px-4 rounded-lg text-sm font-medium hover:bg-ocean-600 transition-colors">
                                Connect
                            </button>
                            <button class="flex-1 border border-slate-300 text-slate-700 py-2 px-4 rounded-lg text-sm font-medium hover:bg-slate-50 transition-colors">
                                Message
                            </button>
                        </div>
                    </div>

                    <!-- Port community gathering example -->
                    <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-6 hover:shadow-md transition-shadow" data-mock="true">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-gradient-to-br from-navy-500 to-ocean-500 rounded-full flex items-center justify-center text-white font-semibold">
                                    KL
                                </div>
                                <div>
                                    <h4 class="font-semibold text-slate-800">Kwame Larsen</h4>
                                    <p class="text-sm text-slate-600">3rd Engineer</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <img src="https://flagcdn.com/w20/gh.png" alt="Ghana" class="w-5 h-3">
                                <div class="w-2 h-2 bg-green-400 rounded-full"></div>
                            </div>
                        </div>
                        <div class="space-y-2 text-sm">
                            <div class="flex items-center text-slate-600">
                                <i class="fas fa-ship w-4 mr-2 text-ocean-500"></i>
                                <span>MV Accra Star</span>
                            </div>
                            <div class="flex items-center text-slate-600">
                                <i class="fas fa-map-marker-alt w-4 mr-2 text-ocean-500"></i>
                                <span>15.2 km away</span>
                            </div>
                            <div class="flex items-center text-slate-600">
                                <i class="fas fa-anchor w-4 mr-2 text-ocean-500"></i>
                                <span>Dry Dock - Repairs</span>
                            </div>
                        </div>
                        <div class="mt-4 pt-4 border-t border-slate-100 flex space-x-2">
                            <button class="flex-1 bg-ocean-500 text-white py-2 px-4 rounded-lg text-sm font-medium hover:bg-ocean-600 transition-colors">
                                Connect
                            </button>
                            <button class="flex-1 border border-slate-300 text-slate-700 py-2 px-4 rounded-lg text-sm font-medium hover:bg-slate-50 transition-colors">
                                Message
                            </button>
                        </div>
                    </div>

                    <!-- Maritime crew unity example -->
                    <div class="bg-white rounded-xl shadow-sm border border-slate-200 p-6 hover:shadow-md transition-shadow" data-mock="true">
                        <div class="flex items-start justify-between mb-4">
                            <div class="flex items-center space-x-3">
                                <div class="w-12 h-12 bg-gradient-to-br from-navy-500 to-ocean-500 rounded-full flex items-center justify-center text-white font-semibold">
                                    AL
                                </div>
                                <div>
                                    <h4 class="font-semibold text-slate-800">Anna Larsson</h4>
                                    <p class="text-sm text-slate-600">Chief Officer</p>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <img src="https://flagcdn.com/w20/se.png" alt="Sweden" class="w-5 h-3">
                                <div class="w-2 h-2 bg-red-400 rounded-full"></div>
                            </div>
                        </div>
                        <div class="space-y-2 text-sm">
                            <div class="flex items-center text-slate-600">
                                <i class="fas fa-ship w-4 mr-2 text-ocean-500"></i>
                                <span>MV Nordic Wind</span>
                            </div>
                            <div class="flex items-center text-slate-600">
                                <i class="fas fa-map-marker-alt w-4 mr-2 text-ocean-500"></i>
                                <span>25.8 km away</span>
                            </div>
                            <div class="flex items-center text-slate-600">
                                <i class="fas fa-route w-4 mr-2 text-ocean-500"></i>
                                <span>Arriving Tomorrow</span>
                            </div>
                        </div>
                        <div class="mt-4 pt-4 border-t border-slate-100 flex space-x-2">
                            <button class="flex-1 bg-ocean-500 text-white py-2 px-4 rounded-lg text-sm font-medium hover:bg-ocean-600 transition-colors">
                                Connect
                            </button>
                            <button class="flex-1 border border-slate-300 text-slate-700 py-2 px-4 rounded-lg text-sm font-medium hover:bg-slate-50 transition-colors">
                                Message
                            </button>
                        </div>
                    </div>
                </div>
                <!-- @END_MAP )) -->

                <!-- Empty State -->
                <div class="text-center py-12 hidden" id="empty-state">
                    <i class="fas fa-compass text-6xl text-slate-300 mb-4"></i>
                    <h3 class="text-xl font-semibold text-slate-700 mb-2">No sailors found</h3>
                    <p class="text-slate-500 mb-4">Try expanding your search range or adjusting filters</p>
                    <button class="text-ocean-500 hover:text-ocean-600 font-medium">Reset Filters</button>
                </div>
            </main>
            <!-- @END_COMPONENT: MainDiscoveryArea -->
        </div>
    </div>
    <!-- @END_COMPONENT: MainContent -->

    <!-- @COMPONENT: BottomNavigation (Mobile) -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-slate-200 px-4 py-2 lg:hidden">
        <div class="flex justify-around items-center">
            <button class="flex flex-col items-center space-y-1 text-ocean-500">
                <i class="fas fa-compass text-lg"></i>
                <span class="text-xs">Discover</span>
            </button>
            <button class="flex flex-col items-center space-y-1 text-slate-400">
                <i class="fas fa-comments text-lg"></i>
                <span class="text-xs">Messages</span>
            </button>
            <button class="flex flex-col items-center space-y-1 text-slate-400">
                <i class="fas fa-users text-lg"></i>
                <span class="text-xs">Groups</span>
            </button>
            <button class="flex flex-col items-center space-y-1 text-slate-400">
                <i class="fas fa-brain text-lg"></i>
                <span class="text-xs">QAAQ AI</span>
            </button>
        </div>
    </nav>
    <!-- @END_COMPONENT: BottomNavigation -->

    <script>
        (function() {
            // Distance slider functionality
            const distanceSlider = document.getElementById('distance-slider');
            const distanceValue = document.getElementById('distance-value');
            
            if (distanceSlider && distanceValue) {
                distanceSlider.addEventListener('input', function() {
                    const value = this.value;
                    let displayValue;
                    
                    if (value < 1000) {
                        displayValue = value + 'km';
                    } else {
                        displayValue = (value / 1000).toFixed(1) + 'k km';
                    }
                    
                    distanceValue.textContent = displayValue;
                });
            }

            // Range buttons functionality  
            const rangeButtons = document.querySelectorAll('[data-range]');
            rangeButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const range = this.dataset.range;
                    if (distanceSlider) {
                        distanceSlider.value = range;
                        distanceSlider.dispatchEvent(new Event('input'));
                    }
                });
            });

            // Discovery button functionality
            const discoveryButton = document.querySelector('[data-event="click:discoverSailors"]');
            if (discoveryButton) {
                discoveryButton.addEventListener('click', function() {
                    // TODO: Implement actual sailor discovery with API
                    console.log('Discovering sailors with current filters...');
                    
                    // Show loading state temporarily for demo
                    const loadingState = document.getElementById('loading-state');
                    const resultsGrid = document.getElementById('results-grid');
                    
                    if (loadingState) {
                        loadingState.classList.remove('hidden');
                    }
                    if (resultsGrid) {
                        resultsGrid.style.opacity = '0.5';
                    }
                    
                    setTimeout(() => {
                        if (loadingState) {
                            loadingState.classList.add('hidden');
                        }
                        if (resultsGrid) {
                            resultsGrid.style.opacity = '1';
                        }
                    }, 1500);
                });
            }

            // Filter change handlers
            const filterCheckboxes = document.querySelectorAll('[data-filter]');
            filterCheckboxes.forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    // TODO: Apply filters to sailor list
                    console.log('Filter changed:', this.dataset.filter, this.checked);
                });
            });

            // QAAQ AI input handler
            const qaaqInput = document.querySelector('input[placeholder="Ask about engines, systems, equipment..."]');
            if (qaaqInput) {
                qaaqInput.addEventListener('keypress', function(e) {
                    if (e.key === 'Enter') {
                        // TODO: Send query to QAAQ AI system
                        console.log('QAAQ Query:', this.value);
                    }
                });
            }
        })();
    </script>

    <style>
        .slider::-webkit-slider-thumb {
            appearance: none;
            height: 20px;
            width: 20px;
            border-radius: 50%;
            background: #0891b2;
            cursor: pointer;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .slider::-moz-range-thumb {
            height: 20px;
            width: 20px;
            border-radius: 50%;
            background: #0891b2;
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .form-checkbox:checked {
            background-color: #0891b2;
            border-color: #0891b2;
        }
    </style>
</body>
</html>