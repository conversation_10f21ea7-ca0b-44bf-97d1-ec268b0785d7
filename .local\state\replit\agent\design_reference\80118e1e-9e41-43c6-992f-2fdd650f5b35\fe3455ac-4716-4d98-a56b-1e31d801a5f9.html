<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QaaqConnect - Maritime Shore Leave Companion</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'navy': '#1e3a8a',
                        'ocean-teal': '#0891b2',
                        'light-teal': '#67e8f9',
                        'maritime-grey': '#6b7280'
                    },
                    fontFamily: {
                        'inter': ['Inter', 'sans-serif']
                    }
                }
            }
        };
    </script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body class="font-inter bg-slate-50 min-h-screen">
    <!-- Header -->
    <header class="bg-navy text-white px-4 py-3 shadow-lg relative">
        <div class="flex items-center justify-between max-w-md mx-auto">
            <div class="flex items-center space-x-2">
                <i class="fas fa-anchor text-ocean-teal text-xl"></i>
                <h1 class="text-lg font-semibold">QaaqConnect</h1>
            </div>
            <div class="flex items-center space-x-3">
                <button class="relative">
                    <i class="fas fa-bell text-lg"></i>
                    <span class="absolute -top-1 -right-1 bg-red-500 text-xs rounded-full w-4 h-4 flex items-center justify-center text-white">3</span>
                </button>
                <button class="bg-ocean-teal rounded-full w-8 h-8 flex items-center justify-center">
                    <i class="fas fa-user text-sm"></i>
                </button>
            </div>
        </div>
    </header>

    <!-- Main Content -->
    <main class="max-w-md mx-auto bg-white min-h-screen">
        <!-- Search Section -->
        <section class="relative">
            <!-- Grey Map Background -->
            <div id="greyMap" class="h-64 bg-gradient-to-br from-slate-200 to-slate-300 relative overflow-hidden transition-all duration-1000">
                <div class="absolute inset-0 opacity-30">
                    <div class="w-full h-full bg-gradient-to-r from-slate-300 via-slate-200 to-slate-300"></div>
                    <!-- Subtle map-like patterns -->
                    <div class="absolute top-8 left-12 w-16 h-8 bg-slate-400 rounded opacity-40"></div>
                    <div class="absolute top-16 right-8 w-12 h-12 bg-slate-400 rounded-full opacity-30"></div>
                    <div class="absolute bottom-12 left-8 w-20 h-4 bg-slate-400 rounded opacity-35"></div>
                    <div class="absolute bottom-20 right-12 w-8 h-16 bg-slate-400 rounded opacity-25"></div>
                </div>
            </div>

            <!-- Colorful Map (Hidden Initially) -->
            <div id="colorfulMap" class="h-64 bg-gradient-to-br from-ocean-teal to-blue-500 relative overflow-hidden transition-all duration-1000 absolute top-0 left-0 w-full opacity-0">
                <!-- Colorful maritime city background -->
                <div class="absolute inset-0" style="background-image: url('https://pixabay.com/get/gc7e4edd6e4bd00406b5ee44842270d4e701c62ff649de94ba906e2985677d103ec15fd3cfb9036604f83de2c2e728a47c4ef541e05e807b3e71d21060a52650e_1280.jpg'); background-size: cover; background-position: center; opacity: 0.8;"></div>
                <div class="absolute inset-0 bg-gradient-to-t from-navy/50 to-transparent"></div>
                
                <!-- Map pins -->
                <div class="absolute top-12 left-16">
                    <i class="fas fa-map-marker-alt text-red-500 text-2xl drop-shadow-lg animate-bounce"></i>
                </div>
                <div class="absolute top-20 right-12">
                    <i class="fas fa-map-marker-alt text-yellow-400 text-xl drop-shadow-lg"></i>
                </div>
                <div class="absolute bottom-16 left-8">
                    <i class="fas fa-map-marker-alt text-green-400 text-lg drop-shadow-lg"></i>
                </div>
            </div>

            <!-- Zoom Controls -->
            <div class="absolute top-4 right-4 flex flex-col space-y-1">
                <button class="bg-white/90 backdrop-blur-sm w-8 h-8 rounded flex items-center justify-center shadow-md hover:bg-white transition-colors">
                    <i class="fas fa-plus text-sm text-navy"></i>
                </button>
                <button class="bg-white/90 backdrop-blur-sm w-8 h-8 rounded flex items-center justify-center shadow-md hover:bg-white transition-colors">
                    <i class="fas fa-minus text-sm text-navy"></i>
                </button>
            </div>

            <!-- Filter Button -->
            <div class="absolute top-4 left-4">
                <button class="bg-white/90 backdrop-blur-sm px-3 py-2 rounded-lg flex items-center space-x-2 shadow-md hover:bg-white transition-colors">
                    <i class="fas fa-filter text-sm text-navy"></i>
                    <i class="fas fa-chevron-down text-xs text-maritime-grey"></i>
                </button>
            </div>

            <!-- Search Prompt Overlay -->
            <div class="absolute inset-0 flex items-center justify-center">
                <div class="text-center px-6">
                    <div id="searchPrompt" class="text-white text-lg font-medium mb-6 drop-shadow-lg min-h-[3rem] flex items-center justify-center">
                        Let's plan your next shore leave! 🏖️
                    </div>
                    <!-- Prominent Action Button -->
                    <button id="activateBtn" class="bg-ocean-teal hover:bg-teal-600 text-white font-semibold px-8 py-4 rounded-full text-lg shadow-xl transform hover:scale-105 transition-all duration-200 flex items-center space-x-2 mx-auto">
                        <i class="fas fa-search"></i>
                        <span>1234 koi hai</span>
                    </button>
                </div>
            </div>
        </section>

        <!-- Quick Categories -->
        <section class="px-4 py-6">
            <h2 class="text-lg font-semibold text-navy mb-4">Quick Find</h2>
            <div class="grid grid-cols-4 gap-3">
                <button class="bg-gradient-to-br from-blue-50 to-blue-100 p-3 rounded-xl text-center hover:from-blue-100 hover:to-blue-200 transition-colors">
                    <i class="fas fa-utensils text-ocean-teal text-xl mb-2"></i>
                    <div class="text-xs font-medium text-navy">Food</div>
                </button>
                <button class="bg-gradient-to-br from-green-50 to-green-100 p-3 rounded-xl text-center hover:from-green-100 hover:to-green-200 transition-colors">
                    <i class="fas fa-wifi text-green-600 text-xl mb-2"></i>
                    <div class="text-xs font-medium text-navy">WiFi</div>
                </button>
                <button class="bg-gradient-to-br from-purple-50 to-purple-100 p-3 rounded-xl text-center hover:from-purple-100 hover:to-purple-200 transition-colors">
                    <i class="fas fa-cocktail text-purple-600 text-xl mb-2"></i>
                    <div class="text-xs font-medium text-navy">Nightlife</div>
                </button>
                <button class="bg-gradient-to-br from-red-50 to-red-100 p-3 rounded-xl text-center hover:from-red-100 hover:to-red-200 transition-colors">
                    <i class="fas fa-dumbbell text-red-600 text-xl mb-2"></i>
                    <div class="text-xs font-medium text-navy">Fitness</div>
                </button>
            </div>
        </section>

        <!-- Recent Questions -->
        <section class="px-4 py-2">
            <div class="flex items-center justify-between mb-4">
                <h2 class="text-lg font-semibold text-navy">Recent Questions</h2>
                <button class="text-ocean-teal text-sm font-medium">View All</button>
            </div>

            <!-- Question Card 1 -->
            <div class="bg-white border border-slate-200 rounded-xl p-4 mb-4 shadow-sm">
                <div class="flex items-start space-x-3">
                    <!-- A group of sailors exploring a busy city street market -->
                    <img src="https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&h=100" alt="Sailors exploring city market" class="w-12 h-12 rounded-full object-cover">
                    <div class="flex-1">
                        <div class="flex items-center space-x-2 mb-2">
                            <span class="font-medium text-navy">Captain_Alex</span>
                            <span class="text-xs text-maritime-grey">2h ago</span>
                            <span class="bg-blue-100 text-blue-700 text-xs px-2 py-1 rounded-full">Hamburg</span>
                        </div>
                        <p class="text-sm text-slate-700 mb-3">Best local restaurants near the port? Looking for authentic German food that won't break the budget 🍺</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <button class="flex items-center space-x-1 text-maritime-grey hover:text-ocean-teal transition-colors">
                                    <span class="text-lg">🦆</span>
                                    <span class="text-sm">12</span>
                                </button>
                                <button class="flex items-center space-x-1 text-maritime-grey hover:text-ocean-teal transition-colors">
                                    <i class="fas fa-comment text-sm"></i>
                                    <span class="text-sm">5</span>
                                </button>
                                <button class="flex items-center space-x-1 text-maritime-grey hover:text-ocean-teal transition-colors">
                                    <i class="fas fa-share text-sm"></i>
                                </button>
                            </div>
                            <div class="text-xs text-maritime-grey">CPSS: Hamburg Sailors</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Question Card 2 -->
            <div class="bg-white border border-slate-200 rounded-xl p-4 mb-4 shadow-sm">
                <div class="flex items-start space-x-3">
                    <!-- Maritime crew socializing at a waterfront cafe -->
                    <img src="https://images.unsplash.com/photo-1541888946425-d81bb19240f5?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&h=100" alt="Maritime crew socializing at waterfront cafe" class="w-12 h-12 rounded-full object-cover">
                    <div class="flex-1">
                        <div class="flex items-center space-x-2 mb-2">
                            <span class="font-medium text-navy">ChiefMate_Sarah</span>
                            <span class="text-xs text-maritime-grey">4h ago</span>
                            <span class="bg-green-100 text-green-700 text-xs px-2 py-1 rounded-full">Singapore</span>
                        </div>
                        <p class="text-sm text-slate-700 mb-3">Anyone know where to get a good SIM card with data plan near Marina Bay? Need it for 3 days 📱</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <button class="flex items-center space-x-1 text-maritime-grey hover:text-ocean-teal transition-colors">
                                    <span class="text-lg">🦆</span>
                                    <span class="text-sm">8</span>
                                </button>
                                <button class="flex items-center space-x-1 text-maritime-grey hover:text-ocean-teal transition-colors">
                                    <i class="fas fa-comment text-sm"></i>
                                    <span class="text-sm">3</span>
                                </button>
                                <button class="flex items-center space-x-1 text-maritime-grey hover:text-ocean-teal transition-colors">
                                    <i class="fas fa-share text-sm"></i>
                                </button>
                            </div>
                            <div class="text-xs text-maritime-grey">CPSS: Singapore Port</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Question Card 3 -->
            <div class="bg-white border border-slate-200 rounded-xl p-4 mb-6 shadow-sm">
                <div class="flex items-start space-x-3">
                    <!-- Sailors enjoying shore leave activities at a beach -->
                    <img src="https://images.unsplash.com/photo-1544551763-46a013bb70d5?ixlib=rb-4.0.3&auto=format&fit=crop&w=100&h=100" alt="Sailors enjoying beach activities during shore leave" class="w-12 h-12 rounded-full object-cover">
                    <div class="flex-1">
                        <div class="flex items-center space-x-2 mb-2">
                            <span class="font-medium text-navy">Engineer_Mike</span>
                            <span class="text-xs text-maritime-grey">6h ago</span>
                            <span class="bg-orange-100 text-orange-700 text-xs px-2 py-1 rounded-full">Miami</span>
                        </div>
                        <p class="text-sm text-slate-700 mb-3">Party tonight? Looking for a good spot to meet fellow sailors and have some drinks 🎉🍹</p>
                        <div class="flex items-center justify-between">
                            <div class="flex items-center space-x-4">
                                <button class="flex items-center space-x-1 text-maritime-grey hover:text-ocean-teal transition-colors">
                                    <span class="text-lg">🦆</span>
                                    <span class="text-sm">15</span>
                                </button>
                                <button class="flex items-center space-x-1 text-maritime-grey hover:text-ocean-teal transition-colors">
                                    <i class="fas fa-comment text-sm"></i>
                                    <span class="text-sm">9</span>
                                </button>
                                <button class="flex items-center space-x-1 text-maritime-grey hover:text-ocean-teal transition-colors">
                                    <i class="fas fa-share text-sm"></i>
                                </button>
                            </div>
                            <div class="text-xs text-maritime-grey">CPSS: Miami Crew</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- QAAQ Bot Response Example -->
        <section class="px-4 py-2 mb-20">
            <div class="bg-gradient-to-r from-ocean-teal/10 to-blue-50 border border-ocean-teal/20 rounded-xl p-4">
                <div class="flex items-start space-x-3">
                    <div class="bg-ocean-teal rounded-full w-10 h-10 flex items-center justify-center flex-shrink-0">
                        <i class="fas fa-robot text-white"></i>
                    </div>
                    <div class="flex-1">
                        <div class="flex items-center space-x-2 mb-2">
                            <span class="font-semibold text-navy">QAAQ Assistant</span>
                            <span class="bg-ocean-teal text-white text-xs px-2 py-1 rounded-full">AI</span>
                        </div>
                        <p class="text-sm text-slate-700 leading-relaxed">
                            <span class="font-medium text-ocean-teal">"QAAQ hai na. That is a nice question..."</span><br><br>
                            For Hamburg port area, I recommend these budget-friendly authentic spots:
                            <br>• Zur Letzten Instanz - Historic restaurant, 10min walk from port
                            <br>• Fischmarkt Hamburg - Sunday morning fish market with food stalls
                            <br>• Block House - Local chain with great steaks, sailor-friendly prices
                            <br><br>
                            <span class="text-xs text-maritime-grey">💡 Safety tip: Keep your documents secure and inform ship security of your plans.</span>
                        </p>
                        <div class="flex items-center space-x-4 mt-3">
                            <button class="flex items-center space-x-1 text-maritime-grey hover:text-ocean-teal transition-colors">
                                <span class="text-lg">🦆</span>
                                <span class="text-sm">24</span>
                            </button>
                            <button class="text-ocean-teal text-sm font-medium">Show more details</button>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Bottom Navigation -->
    <nav class="fixed bottom-0 left-0 right-0 bg-white border-t border-slate-200 px-4 py-2">
        <div class="max-w-md mx-auto">
            <div class="flex items-center justify-around">
                <button class="flex flex-col items-center space-y-1 text-ocean-teal">
                    <i class="fas fa-map-marked-alt text-lg"></i>
                    <span class="text-xs font-medium">Explore</span>
                </button>
                <button class="flex flex-col items-center space-y-1 text-maritime-grey hover:text-ocean-teal transition-colors">
                    <i class="fas fa-users text-lg"></i>
                    <span class="text-xs">Groups</span>
                </button>
                <button class="flex flex-col items-center space-y-1 text-maritime-grey hover:text-ocean-teal transition-colors">
                    <i class="fas fa-question-circle text-lg"></i>
                    <span class="text-xs">Ask</span>
                </button>
                <button class="flex flex-col items-center space-y-1 text-maritime-grey hover:text-ocean-teal transition-colors">
                    <i class="fas fa-bookmark text-lg"></i>
                    <span class="text-xs">Saved</span>
                </button>
                <button class="flex flex-col items-center space-y-1 text-maritime-grey hover:text-ocean-teal transition-colors">
                    <i class="fas fa-user-circle text-lg"></i>
                    <span class="text-xs">Profile</span>
                </button>
            </div>
        </div>
    </nav>

    <!-- Floating Action Button -->
    <button class="fixed bottom-20 right-4 bg-ocean-teal hover:bg-teal-600 text-white w-14 h-14 rounded-full shadow-xl flex items-center justify-center transition-all duration-200 hover:scale-110">
        <i class="fas fa-plus text-xl"></i>
    </button>

    <script>
        (function() {
            // Rotating search prompts
            const prompts = [
                "Let's plan your next shore leave! 🏖️",
                "Hungry? Find the best local food 🍕", 
                "Need a SIM card or WiFi? Ask away 📱",
                "Where's the party tonight? 🎉",
                "Looking for a gym or sports? 💪",
                "Shopping time? Best deals nearby 🛍️",
                "Safe taxi or transport tips? 🚕",
                "Meet fellow sailors for coffee? ☕"
            ];

            let currentPromptIndex = 0;
            const promptElement = document.getElementById('searchPrompt');

            function rotatePrompt() {
                promptElement.style.opacity = '0';
                setTimeout(() => {
                    currentPromptIndex = (currentPromptIndex + 1) % prompts.length;
                    promptElement.textContent = prompts[currentPromptIndex];
                    promptElement.style.opacity = '1';
                }, 300);
            }

            // Rotate prompts every 3 seconds
            setInterval(rotatePrompt, 3000);

            // Map activation
            const activateBtn = document.getElementById('activateBtn');
            const greyMap = document.getElementById('greyMap');
            const colorfulMap = document.getElementById('colorfulMap');

            activateBtn.addEventListener('click', function() {
                // Fade out grey map and fade in colorful map
                greyMap.style.opacity = '0';
                colorfulMap.style.opacity = '1';
                
                // Change button text
                activateBtn.innerHTML = '<i class="fas fa-search"></i><span>Ask QAAQ anything...</span>';
                activateBtn.classList.add('animate-pulse');
                
                // TODO: Implement search functionality and location-based content loading
                // TODO: Connect to QAAQ API for bot responses
                // TODO: Implement real-time messaging and community features
            });

            // Add smooth scroll behavior for mobile
            document.documentElement.style.scrollBehavior = 'smooth';

            // Add touch feedback for mobile buttons
            const buttons = document.querySelectorAll('button');
            buttons.forEach(button => {
                button.addEventListener('touchstart', function() {
                    this.style.transform = 'scale(0.95)';
                });
                button.addEventListener('touchend', function() {
                    setTimeout(() => {
                        this.style.transform = 'scale(1)';
                    }, 100);
                });
            });
        })();
    </script>
</body>
</html>