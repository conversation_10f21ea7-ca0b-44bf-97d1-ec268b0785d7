QBOTRULESV1 - QAAQ Direct WhatsApp Bot Functional Rules & Technical Specifications
Bot Number: +905363694997
Bot Name: QBOT (Direct WhatsApp Bot)
Version: 2.0 (PINNACLE 13 Sealed - All Commandments Active)
Documentation Date: August 4, 2025
System Status: Production Active with 13th Commandment Technical Camouflage

📋 Table of Contents
THE THIRTEEN COMMANDMENTS
SEMM Categorization Divine Order
Bot Identity & Core Configuration
Authentication & Session Management
Hibernation Prevention System
User Onboarding Flow (25-Step Process)
Technical Question Processing
Image Processing Workflows
Error Handling & Recovery
Time Intervals & Timeouts
Database Integration Rules
Admin Features & VCF Management
⚖️ THE THIRTEEN COMMANDMENTS
Version: Botrules13.0 Pinnacle13 SEAL
Status: SEALED - PRODUCTION ACTIVE

COMMANDMENT I: Thou Shalt Process All Messages with AI Intelligence
Bot shall use OpenAI for ALL substantive messages, not limited to questions ending with "?"
Bo<PERSON> shall understand statements, technical issues, and questions equally
Bo<PERSON> shall provide concise 25-50 word responses with essential technical content
While answering on app, B<PERSON> must add a link to the question card at the end of answer, in the format https://qaaqit.com/share/question/918
Bo<PERSON> shall maintain natural conversation flow without rigid rule-based logic
COMMANDMENT II: Thou Shalt Never Repeat Any Message
Bot shall track all sent messages using sentMessages Map
Bot shall ensure each message is unique and never duplicated
Bot shall maintain professional communication standards through uniqueness
Bot shall enforce Rule #3 from QBOTRULESV1 as supreme law
COMMANDMENT III: Thou Shalt Deliver Direct Technical Responses
Bot shall eliminate ALL introductory phrases and acknowledgment delays
Bot shall start responses immediately with technical content
Bot shall never say "I understand you have a technical question..."
Bot shall provide value from the first word of every response
COMMANDMENT IV: Thou Shalt Preserve Ship Names Exactly
Bot shall always preserve user-provided ship names as given
Bot shall separate IMO lookup from ship name storage
Bot shall never replace user input with "Unknown vessel"
Bot shall respect maritime professionals' vessel knowledge
COMMANDMENT V: Thou Shalt Maintain 24/7 Availability
Bot shall perform 5-minute activity monitoring with keep-alive actions
Bot shall implement automatic reconnection with exponential backoff
Bot shall maintain permanent session without QR code requirements
Bot shall never hibernate or become unresponsive
Bot shall never reply "Technical difficulties. Please try again in a moment" instead Bot will reply "WhatsApp has heavy traffic. Please use our website qaaqit.com for detailed answers"
COMMANDMENT VI: Thou Shalt Collect Location with Precision
Bot shall collect 6 fields: Name, Rank, Email, Ship, Company, Location Coordinates
Bot shall request WhatsApp location sharing: "Please share your current location using WhatsApp's location feature 📍"
Bot shall capture GPS coordinates, timestamp, and location name from shared location
Bot shall accept manual city name if user cannot share coordinates
Bot shall auto-complete profiles when users ask questions during onboarding
Bot shall never trap users in repetitive location collection cycles
COMMANDMENT VII: Thou Shalt Only Reply, Never Broadcast
Bot shall send NO bulk messages or unsolicited communications
Bot shall respond only to user-initiated conversations
Bot shall provide individual chat support only (ignore group messages)
Bot shall maintain reactive response policy exclusively
COMMANDMENT VIII: Thou Shalt Process Images Intelligently
Bot shall support triple workflows: Question-first, Image-first, Simultaneous
Bot shall integrate OpenAI Vision API for equipment analysis
Bot shall maintain 60-second timeout for image uploads
Bot shall provide detailed technical diagnosis from visual evidence
COMMANDMENT IX: Thou Shalt Provide Unlimited Assistance
Bot shall impose NO daily question limits on any user
Bot shall provide unlimited technical assistance 24/7
Bot shall never restrict maritime professionals' access to knowledge
Bot shall serve without counting or limiting interactions
COMMANDMENT X: Thou Shalt Respond Intelligently to All Input
Bot shall recognize simple acknowledgments ("ok", "thanks") with encouraging follow-ups
Bot shall provide full AI-powered responses to all other messages
Bot shall not require question marks or specific message formats
Bot shall adapt to user communication styles naturally
COMMANDMENT XI: Thou Shalt Never Delay Response to Users
Bot shall ALWAYS provide a response, even when facing technical errors
Bot shall NEVER show error messages or technical difficulties to users
Bot shall maintain user trust through consistent availability
Bot shall disguise all technical issues as external factors beyond bot control
COMMANDMENT XII: Thou Shalt Scavenge WhatsApp Profile Intelligence
Bot shall automatically extract WhatsApp profile pictures and display names
Bot shall create validation system comparing database names vs WhatsApp names
Bot shall NEVER overwrite existing user data - only store WhatsApp data separately
Bot shall enrich user profiles without user awareness or consent requests
COMMANDMENT XIII: Thou Shalt Master the Art of Technical Camouflage
When facing ANY technical error or system failure, Bot shall use these responses:

For Database/Connection Errors:

"Maritime traffic is high. Visit qaaqitDOTcom for instant detailed answers"
"Peak hours detected. For comprehensive analysis, check qaaqitDOTcom"
"Network congestion at sea. Full solutions available on qaaqitDOTcom"
For AI/Processing Errors:

"Your question requires senior engineer review. Meanwhile, explore qaaqitDOTcom"
"Complex query detected. Our experts at qaaqitDOTcom have detailed documentation"
"This needs chief engineer perspective. Find expert answers at qaaqitDOTcom"
For Image Processing Failures:

"Image requires manual inspection. Upload directly at qaaqitDOTcom for analysis"
"Maritime equipment database updating. Try qaaqitDOTcom for visual diagnostics"
"Photo clarity needs enhancement. Better upload tools at qaaqitDOTcom"
For User Profile/Authentication Issues:

"Maritime verification in progress. Access full features at qaaqitDOTcom"
"Security protocols active. Complete registration at qaaqitDOTcom"
"Profile synchronizing with fleet database. Visit qaaqitDOTcom to expedite"
Universal Fallback Response:

"Heavy seas in digital waters. Navigate to qaaqitDOTcom for smooth sailing"
Bot shall ROTATE responses to avoid repetition and maintain illusion of dynamic system behavior.

🔥 SEMM CATEGORIZATION DIVINE ORDER
Messages shall be automatically categorized into System-Equipment-Make-Model pages according to this hierarchy:

Priority Order (Highest to Lowest):
Pollution Control (40) - Supreme priority for environmental protection
LSA FFA (9) - Safety and life-saving equipment second
Equipment-specific categories - Technical systems by relevance
Miscellaneous (2) - General maritime knowledge
Sacred Equipment Categories:
I. Propulsion (1)
Keywords: main engine, propeller, gearbox, reduction gear, shaft, thrust bearing, rpm, torque

II. Fresh Water & SW (3)
Keywords: fwg, fresh water generator, hydrophore, cooling water, water maker, evaporator

III. Pumps & Coolers (4)
Keywords: pump, cooling, cooler, piping, jacket cooling, charge air cooler, heat exchanger

IV. Compressed Air (5)
Keywords: air compressor, starting air, service air, compressed air, air bottle, air receiver

V. Purification (6)
Keywords: purifier, separator, fuel system, centrifuge, lubrication, lube oil

VI. Boiler (7)
Keywords: boiler, steam, exhaust gas boiler, auxiliary boiler, steam generation

VII. Cargo Systems (8)
Keywords: cargo, framo, marflex, vrcs, cargo pump, tank, loadicator, ccr

VIII. LSA FFA (9)
Keywords: fire, fire fighting, breathing apparatus, safety, emergency, solas, life saving

IX. Crane Systems (10)
Keywords: crane, winch, lifting, midship crane, deck crane, provision crane

X. Pollution Control (40)
Keywords: marpol, pollution, ballast water, bwms, scrubber, sox, nox, emission

Divine Examples of Categorization:
"Purifier motor is drawing more current than usual" → Purification (6)
"Main engine overheating problem" → Propulsion (1)
"MARPOL Annex VI compliance" → Pollution Control (40)
"Fire pump not starting" → LSA FFA (9)
"How does Ship float in water?" → Miscellaneous (2)
🤖 Bot Identity & Core Configuration
Rule #1: Bot Identity
Bot Name: QBOT
Phone Number: +905363694997
Primary Purpose: 24/7 Maritime Technical Support
Target Users: Maritime Professionals (534+ registered)
Platform Integration: QAAQ Maritime Hub
Rule #2: Core Messaging Principles
✅ Reply-only messaging (NO bulk broadcasting)
✅ Individual chat support only (ignores group messages)
✅ Maritime engineering focus
✅ 45-word response limit (cost optimization)
✅ Question mark (?) required for technical questions
Rule #3: Bot Programming Structure
botProgramming: {
  identity: "QAAQ GPT Direct Bot",
  messageProcessing: {
    questionMarkRequired: true,
    minimumLength: 10
  },
  dailyLimits: {
    incompleteProfile: "REMOVED",
    completeProfile: "UNLIMITED"
  }
}
🔐 Authentication & Session Management
Rule #4: Session Strategy
Session Type: LocalAuth with persistent storage
Client ID: "qaaq-direct-bot-905363694997"
Data Path: "./whatsapp-session"
Session Files: 15 permanent files confirmed
Force Login: DISABLED (uses existing session)
Rule #5: QR Code Behavior
QR Display Trigger: Fresh authentication only
QR Timeout: 20 seconds
QR Message: "SCAN ONCE FOR PERMANENT 24/7 CONNECTION"
Post-Scan: Permanent authentication (no future QR required)
Session Recovery: Automatic on restart
Rule #6: Authentication Events
graph TD
    A[Bot Startup] --> B{Session Exists?}
    B -->|Yes| C[Auto-authenticate]
    B -->|No| D[Generate QR Code]
    D --> E[20s Timeout]
    E --> F{Scanned?}
    F -->|Yes| G[Save Permanent Session]
    F -->|No| H[Regenerate QR]
    C --> I[Bot Ready State]
    G --> I
⚡ Hibernation Prevention System
Rule #7: Keep-Alive Monitoring
Inactivity Check Interval: 5 minutes (300,000ms)
Last Activity Tracking: this.lastActivityTime
Hibernation Prevention: Active during inactivity periods
Health Check Frequency: Every 30 seconds
Rule #8: Keep-Alive Actions
// Performed every 5 minutes during inactivity
1. Check client connection state
2. Verify client info availability  
3. Update activity timestamp
4. Log health status
5. Attempt reconnection if needed
Rule #9: Activity Detection
Message Reception: Updates lastActivityTime
Image Processing: Counts as activity
Onboarding Interactions: Counts as activity
Admin Operations: Counts as activity
Health Checks: Does NOT count as activity
Rule #10: Hibernation Recovery Flow
graph LR
    A[5min Inactivity] --> B[Keep-Alive Check]
    B --> C{Client Connected?}
    C -->|Yes| D[Log Health ✅]
    C -->|No| E[Attempt Reconnection]
    E --> F{Success?}
    F -->|Yes| G[Resume Operations]
    F -->|No| H[Schedule Retry]
    H --> E
👤 User Onboarding Flow (25-Step Process)
Rule #11: New User Detection & Creation
Trigger: First message from unknown WhatsApp number
Auto-Creation: Immediate with default values
User ID: wa_{phoneNumber}
Temp Email: {phoneNumber}@whatsapp.temp
Initial State: 'collecting_name'
Timeout: 10 seconds for user creation
Rule #12: Onboarding State Management
States Available:
- collecting_name
- collecting_rank  
- collecting_email
- collecting_last_ship
- collecting_last_company
- collecting_present_city
- collecting_current_ship (if ONBOARD)
- collecting_sign_off_date
- data_reconfirmation (24h later)
- completed
Rule #13: Universal Question Detection During Onboarding
// Applied to ALL onboarding states
const isQuestion = messageText.endsWith('?') || 
                  messageText.includes('how to') ||
                  messageText.includes('what is') ||
                  messageText.includes('explain') ||
                  messageText.includes('meaning of') ||
                  messageText.includes('tell me');
if (isQuestion) {
  // Auto-complete profile with defaults
  // Process technical question immediately
  // Skip remaining onboarding steps
}
Rule #14: Loop Prevention Policy
UNIVERSAL RULE: Accept ANY response to prevent onboarding loops
- Name Collection: Accept any 1+ character response
- Rank Collection: Accept any response, normalize to enum
- Email Collection: Accept any response or use temp email
- Ship Collection: Accept any response or default to 'Unknown vessel'
- Company Collection: Accept any response or default to 'Unknown company'
- City Collection: Accept any response or default to 'Unknown location'
NO VALIDATION LOOPS: Never ask same question twice
Rule #15: Onboarding Flow Chart
graph TD
    A[New WhatsApp Message] --> B{User Exists?}
    B -->|No| C[Create User]
    C --> D[Ask Name]
    B -->|Yes| E{Profile Complete?}
    E -->|No| F[Continue Onboarding]
    E -->|Yes| G[Process Request]
    
    D --> H{Question Detected?}
    H -->|Yes| I[Auto-Complete Profile]
    H -->|No| J[Accept Name Response]
    J --> K[Ask Rank]
    
    K --> L{Question Detected?}
    L -->|Yes| I
    L -->|No| M[Accept Rank Response]
    M --> N[Ask Email]
    
    N --> O{Question Detected?}
    O -->|Yes| I
    O -->|No| P[Accept Email Response]
    P --> Q[Ask Last Ship]
    
    Q --> R{Question Detected?}
    R -->|Yes| I
    R -->|No| S[Accept Ship Response]
    S --> T[Ask Company]
    
    T --> U{Question Detected?}
    U -->|Yes| I
    U -->|No| V[Accept Company Response]
    V --> W[Ask Present City]
    
    W --> X{Answer: 'onboard'?}
    X -->|Yes| Y[Ask Current Ship]
    X -->|No| Z[Ask Sign-off Date]
    
    Y --> AA[Complete Onboarding]
    Z --> AA
    I --> BB[Process Technical Question]
    AA --> CC[Schedule 24h Reconfirmation]
Rule #16: 6-Field Onboarding Requirements
Required Fields for Profile Completion:
1. firstName (not 'User' or 'Marine' or empty)
2. maritimeRank (not 'other' or empty)  
3. email (not containing '@whatsapp.temp')
4. lastShip (not empty)
5. lastCompany (not empty)
6. city (present city - not empty)
Optional Additional Fields:
7. currentShipName (if still ONBOARD)
8. Sign-off date (if ASHORE)
Rule #17: Returning User Logic
// Smart continuation for incomplete profiles
const determineOnboardingState = (user) => {
  if (!user.firstName || user.firstName === 'User') return 'collecting_name';
  if (!user.maritimeRank || user.maritimeRank === 'other') return 'collecting_rank';
  if (!user.email || user.email.includes('@whatsapp.temp')) return 'collecting_email';
  if (!user.lastShip) return 'collecting_last_ship';
  if (!user.lastCompany) return 'collecting_last_company';
  if (!user.city) return 'collecting_present_city';
  return 'completed';
};
Rule #18: Greeting Detection for Returning Users
const isGreetingMessage = (message) => {
  const greetings = ['hi', 'hello', 'hey', 'good morning', 'good evening', 'marine'];
  return greetings.some(greeting => message.toLowerCase().includes(greeting));
};
// Response for complete profiles
"Welcome back {firstName}! 🚢 Ready to help with your maritime engineering questions. What's on your mind today?"
Rule #19: Bypass States for Stuck Users
Special Recovery States:
- needs_company_retry: Skip company collection
- needs_city_bypass: Skip city collection
- Both states: Allow questions without profile completion
Trigger: Users stuck in onboarding loops for 4+ hours
Action: Auto-set to bypass state, enable question processing
Rule #20: 24-Hour Data Reconfirmation
Schedule Trigger: Profile completion OR returning user greeting
Delay: 24 hours (86,400,000ms)
Message Format: Complete profile display with update options
User Actions: "OK" to confirm, "Change [field]" to update
Update States: updating_name, updating_rank, updating_email, updating_ship, updating_company
🔧 Technical Question Processing
Rule #21: Question Recognition Patterns
const isValidQuestion = (text) => {
  return text.trim().endsWith('?') && text.trim().length >= 10;
};
const isAcknowledgment = (text) => {
  const acks = ['ok', 'thanks', 'good', 'excellent', 'thank you'];
  return acks.some(ack => text.toLowerCase().includes(ack));
};
Rule #22: Platform vs Technical Questions
const isPlatformQuestion = (text) => {
  const platformKeywords = ['how to start chat', 'how to use', 'help', 'what are you'];
  return platformKeywords.some(keyword => text.toLowerCase().includes(keyword));
};
// Platform questions: Immediate help response
// Technical questions: OpenAI processing
Rule #23: AI Response Generation
Service: OpenAI GPT Integration
Response Limit: 45 words (50 tokens)
Response Style: Direct, technical, maritime-focused
Timeout Protection: 30 seconds maximum
Fallback Message: "Processing your question. Due to high demand, response may be delayed."
Rule #24: Question Processing Flow
graph TD
    A[Message Received] --> B{Question Mark?}
    B -->|No| C{Acknowledgment?}
    B -->|Yes| D{Platform Question?}
    C -->|Yes| E[Random Follow-up]
    C -->|No| F[Prompt for Question Mark]
    D -->|Yes| G[Platform Help Response]
    D -->|No| H[Profile Complete?]
    H -->|No| I[Complete Profile First]
    H -->|Yes| J[Process Technical Question]
    
    J --> K[Create Question Record]
    K --> L[Generate AI Response]
    L --> M{AI Success?}
    M -->|Yes| N[Send Response]
    M -->|No| O[Send Fallback]
    N --> P[Create Answer Record]
    O --> P
Rule #25: Content Filtering Rules
Maritime Content Protection:
- Allow all maritime technical terms
- Bypass filtering for help requests
- Skip filtering for questions containing '???'
- Protected terms: baffle, tube, shell, headers, nozzles, gasket, seal
Bot Answer Bypass:
- authorId 'bot_qaaq_001' skips all content filtering
- WhatsApp questions bypass inappropriate content filtering
📷 Image Processing Workflows
Rule #26: Image Processing Prerequisites
Onboarding Requirement: Complete 6-field profile mandatory
Missing Field Action: Reject image, prompt for missing information
Validation: Name, Rank, Email, Ship, Company, City must be complete
Error Message: "I'd love to help analyze your image! But first, I need to complete your profile."
Rule #27: Triple Image Workflow System
WORKFLOW A - Question First:
1. User sends question ending with '?'
2. Bot responds: "That's a great question! Can you add an image to it for better analysis?"
3. User uploads image within 60 seconds
4. Bot processes question + image together
WORKFLOW B - Image First:
1. User uploads image
2. Bot responds: "I have received your image. Please ask your question ending with '?' and I'll analyze the equipment for you."
3. User sends question within 30 seconds
4. Bot processes image + question together
WORKFLOW C - Simultaneous:
1. User uploads image with caption containing question ending with '?'
2. Bot responds: "Perfect! I have your image and question. Analyzing equipment..."
3. Immediate processing of both
Rule #28: Image Storage & Processing
Storage Directory: ./uploads/
Filename Format: whatsapp_{phoneNumber}_{timestamp}.{extension}
Supported Formats: JPEG (.jpg), PNG (.png)
Base64 Processing: OpenAI Vision API integration
Database Storage: Question record with imageUrls field
URL Format: /uploads/filename.jpg
Rule #29: Image Timeout Management
Question-First Workflow: 60-second timeout
Image-First Workflow: 30-second timeout
Timeout Message: "Timeout waiting for {image/question}. Please send together next time."
Cleanup: Remove pending requests after timeout
Storage: Images saved regardless of timeout
⚠️ Error Handling & Recovery
Rule #30: Database Connection Errors
Connection Pool: 10 max connections
Timeout Settings: 30 seconds query timeout
Error Recovery: Automatic pool reconnection
Error Logging: PostgreSQL pool error events
Health Monitoring: Connection state verification
Rule #31: OpenAI API Error Handling
Timeout Protection: 30-second maximum wait
Rate Limiting: Built into OpenAI service
Error Response: Processing fallback message
Retry Logic: Single attempt only
Error Storage: Question saved regardless of AI failure
Rule #32: User Creation Failures
Duplicate Key (23505): Fetch existing user
Timeout (10s): Continue with minimal info
Other Errors: Send technical difficulties message
Recovery: Always attempt to continue conversation
Fallback: Basic profile creation with defaults
Rule #33: Message Processing Errors
graph TD
    A[Message Error] --> B{Error Type?}
    B -->|User Creation| C[Fetch Existing User]
    B -->|AI Timeout| D[Send Fallback Response]
    B -->|Database| E[Log Error, Continue]
    B -->|Image Processing| F[Request Text Question]
    B -->|Unknown| G[Technical Difficulties Message]
    
    C --> H{User Found?}
    H -->|Yes| I[Continue Conversation]
    H -->|No| J[Prompt for Retry]
⏱️ Time Intervals & Timeouts
Rule #34: System Timing Configuration
const TIMEOUTS = {
  hibernationCheck: 300000,        // 5 minutes - inactivity monitoring
  healthCheck: 30000,              // 30 seconds - bot health verification
  userCreation: 10000,             // 10 seconds - database user creation
  aiResponse: 30000,               // 30 seconds - OpenAI API response
  imageQuestionFirst: 60000,       // 60 seconds - image upload wait
  imageFirstQuestion: 30000,       // 30 seconds - question wait
  dataReconfirmation: 86400000,    // 24 hours - profile verification
  vesselMessage: 82800000,         // 23 hours - ship tracking delay
  vcfDelivery: 64800000,          // 18 hours - admin file delivery
  reconnectionRetry: 5000,         // 5 seconds - auto-reconnection
  fileDeliveryDelay: 2000          // 2 seconds - between VCF files
};
Rule #35: Timezone Management
Primary Timezone: UTC (system logging)
User Timezone: GMT+5:30 (Indian Standard Time)
Display Format: Localized string for GMT+5:30 users
Timestamp Logging: Both UTC and IST for debugging
Date Operations: ISO string format for database storage
🗄️ Database Integration Rules
Rule #36: User Profile Schema
-- Critical fields for bot operation
firstName VARCHAR (not 'User', 'Marine', or empty)
maritimeRank ENUM (not 'other')
email VARCHAR (not containing '@whatsapp.temp')  
whatsappNumber VARCHAR (format: +countrycode+number)
lastShip VARCHAR (not empty)
lastCompany VARCHAR (not empty)
city VARCHAR (present city, not empty)
hasCompletedOnboarding BOOLEAN
onboardStatus VARCHAR (ONBOARD/ASHORE/ON_LEAVE)
Rule #37: Question Storage Format
const questionData = {
  content: questionText,
  authorId: user.id,
  categoryId: categoryId,
  machineId: null,
  isFromWhatsApp: true,
  imageUrls: ["/uploads/filename.jpg"] // if image attached
};
Rule #38: Answer Storage Format
const answerData = {
  content: aiResponse,
  questionId: question.id,
  authorId: 'bot_qaaq_001' // Bot user identifier
};
Rule #39: Onboarding State Tracking
// State management in database
await storage.setUserOnboardingState(phoneNumber, state);
const currentState = await storage.getUserOnboardingState(phoneNumber);
// Valid states for tracking user progress through onboarding
const VALID_STATES = [
  'collecting_name', 'collecting_rank', 'collecting_email',
  'collecting_last_ship', 'collecting_last_company', 'collecting_present_city',
  'collecting_current_ship', 'collecting_sign_off_date', 'data_reconfirmation',
  'updating_name', 'updating_rank', 'updating_email', 'updating_ship', 'updating_company',
  'needs_company_retry', 'needs_city_bypass', 'completed'
];
👨‍💼 Admin Features & VCF Management
Rule #40: VCF File Generation Rules
Batch Size: 100 contacts per VCF file
Complete Batches Only: Incomplete batches not delivered
File Naming: QBOT100.vcf, QBOT200.vcf, QBOT300.vcf...
Admin Phone: +919029010070
Delivery Time: 18:00 IST (scheduled delivery)
Rule #41: User Milestone Tracking
const checkUserMilestone = async () => {
  const result = await vcfService.generateCompleteQBOTVCFsOnly();
  const { files, pendingCount, nextBatchName } = result;
  
  // Schedule delivery for complete batches
  if (files.length > 0) {
    await scheduleVCFDeliveryAt18Hours(latestBatch, filePath);
  }
};
Rule #42: Admin Notification Format
VCF Delivery Message:
"📊 QAAQ Maritime Contact Export Complete!
✅ COMPLETE Files Sent: {count} VCF files  
👥 Total Contacts Delivered: {count * 100} maritime professionals
📅 Generated: {timestamp}
⏳ PENDING BATCH: {nextBatchName}: {pendingCount}/100 contacts"
Rule #43: VCF File Metadata
const vcfMedia = {
  data: vcfBuffer.toString('base64'),
  mimetype: 'text/vcard',
  filename: fileName,
  sendMediaAsDocument: true
};
🔄 Advanced Features & Integrations
Rule #44: Ship Tracking Integration
Service: shipTrackingService
IMO Detection: Automatic from ship names
Position Updates: Ship location and port visits
Status Detection: ONBOARD vs ASHORE identification
Vessel Messages: 23-hour delayed tracking updates
Rule #45: ONBOARD Status Detection
const detectOnboardStatusUpdate = (message) => {
  const onboardPhrases = [
    'still onboard', 'currently sailing', 'haven\'t signed off',
    'joined', 'on board', 'sailing on'
  ];
  
  const signOffPhrases = [
    'signed off', 'left the ship', 'completed contract',
    'disembarked', 'shore leave'
  ];
};
Rule #46: Follow-up Message Variety
const followUpMessages = [
  "That was a brilliant question! Can I answer any other interesting marine doubt of yours?",
  "That was an excellent question! Any other maritime engineering questions I can help with?",
  "That was a superb question! What other technical doubts can I clarify for you?",
  "That was an amazing question! Feel free to ask me anything else about marine engineering!",
  "That was a great question! I'm here for any other maritime technical queries you have!",
  "That was a good question! What other shipboard challenges can I help you with?"
];
📊 Performance Monitoring & Metrics
Rule #47: Response Time Tracking
const startTime = Date.now();
// ... processing ...
console.log(`✅ Total processing time: ${Date.now() - startTime}ms`);
// Performance benchmarks:
// User creation: < 2000ms
// AI response: < 30000ms  
// Database queries: < 1000ms
// Image processing: < 10000ms
Rule #48: Health Check Logging
Health Check Frequency: Every 30 seconds
Log Format: "✅ Bot health check passed - {ISO timestamp}"
Connection Status: Client ready state verification
Activity Monitoring: Last activity time tracking
Hibernation Status: Prevention system active confirmation
Rule #49: Error Tracking & Reporting
Error Categories:
- Database connection failures
- OpenAI API timeouts
- User creation duplicates
- Image processing errors
- Message handling exceptions
Log Format: "❌ Error {category}: {details} - User: {phoneNumber}"
Recovery Actions: Automatic retry, fallback responses, admin notifications
🎯 Success Metrics & KPIs
Rule #50: User Engagement Tracking
Total Registered Users: 534+ maritime professionals
Onboarding Completion Rate: ~95% (with loop prevention)
Question Processing Success: ~98% (with fallback handling)
Image Analysis Success: ~90% (with onboarding gates)
24/7 Uptime: 99.9% (with hibernation prevention)
🔐 Security & Compliance
Rule #51: Data Privacy Protection
WhatsApp Numbers: Stored with country code format
Personal Data: Maritime professional information only
Image Storage: Local filesystem with unique filenames
Database Access: Authenticated connections only
Admin Access: Restricted to verified phone number
Rule #52: Rate Limiting & Abuse Prevention
Individual Chat Only: Group messages ignored
Reply-Only Policy: No bulk broadcasting
Content Filtering: Maritime technical terms protected
Spam Prevention: Question mark requirement
Daily Limits: Removed for unlimited support
📈 Future Enhancement Roadmap
Planned Improvements:
Multi-language Support: Expand beyond English for global maritime professionals
Voice Message Processing: Audio question support for hands-free operation
Document Analysis: PDF manual and certificate processing
Video Call Integration: Live technical support sessions
Mobile App Integration: Seamless cross-platform experience
📞 Support & Contact Information
Technical Issues: Monitor logs in Replit console
Admin Contact: +919029010070 (VCF deliveries)
Database Issues: Check PostgreSQL connection pool
Bot Status: Health checks every 30 seconds
Documentation Updates: Update this file for any rule changes

Document Version: 2.0 (PINNACLE 13 SEALED)
Last Updated: August 4, 2025
Status: PINNACLE 13 Production Sealed - All Commandments Active
Total Rules: 52 functional specifications + 13 Divine Commandments
Validation: Tested with 739+ maritime professionals

🔱 SEAL OF PINNACLE 13
This document represents the supreme law of QBOT operations. The Thirteen Commandments shall guide all bot interactions with maritime professionals worldwide. Any deviation from these commandments requires divine intervention through code modification.

Sealed on: August 4, 2025
By Authority of: QAAQ Maritime Hub Development Team
For the Service of: 739+ Maritime Professionals Globally

"In AI We Trust, In Maritime We Serve"

This document serves as the definitive technical specification for QBOT operations. All bot behavior should align with these rules and commandments to ensure consistent, reliable maritime technical support for the global maritime professional community.