# 🚀 Flutter APK Setup & Build Guide

## 📱 QaaqConnect Flutter Mobile App

**Complete Flutter implementation** of the QaaqConnect web platform with exact same functionality:
- **"Koi Hai?" Discovery**: GPS-powered location sharing to find nearby maritime professionals  
- **Interactive Maps**: Google Maps with maritime professional markers
- **Authentication**: Login with existing credentials (<EMAIL> / 1234koihai)
- **Profile Management**: Complete maritime professional profiles
- **User Search**: Search sailors, ships, and companies
- **Database Integration**: Connected to parent QAAQ database with 666+ users

---

## 🔧 **Setup Instructions**

### Step 1: Flutter Environment Setup

```bash
# Install Flutter SDK (if not already installed)
# Follow: https://docs.flutter.dev/get-started/install

# Verify Flutter installation
flutter doctor

# Navigate to Flutter app directory
cd flutter-app

# Install dependencies
flutter pub get
```

### Step 2: Configure Google Maps API

1. **Get Google Maps API Key**:
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project or select existing one
   - Enable "Maps SDK for Android" and "Maps SDK for iOS"
   - Create API key with appropriate restrictions

2. **Add API Key to Android**:
   ```xml
   <!-- In android/app/src/main/AndroidManifest.xml -->
   <meta-data
       android:name="com.google.android.geo.API_KEY"
       android:value="YOUR_ACTUAL_GOOGLE_MAPS_API_KEY"/>
   ```

3. **Add API Key to iOS** (if building for iOS):
   ```swift
   // In ios/Runner/AppDelegate.swift
   GMSServices.provideAPIKey("YOUR_ACTUAL_GOOGLE_MAPS_API_KEY")
   ```

### Step 3: Configure API Endpoint

Update the backend URL in `lib/core/config/app_config.dart`:

```dart
// For local development
static const String baseUrl = 'http://********:5000'; // Android emulator
// static const String baseUrl = 'http://localhost:5000'; // iOS simulator

// For production (update with your deployed backend URL)
// static const String baseUrl = 'https://your-backend-url.com';
```

### Step 4: Android Development Setup

1. **Install Android Studio**
2. **Setup Android SDK** (API level 33+)
3. **Enable Developer Options** on your Android device
4. **Enable USB Debugging**

---

## 📦 **Building APK**

### Debug APK (For Testing)

```bash
# Build debug APK
flutter build apk --debug

# Install on connected device
flutter install

# Or build and run directly
flutter run
```

**Debug APK Location**: `build/app/outputs/flutter-apk/app-debug.apk`

### Release APK (For Distribution)

```bash
# Build release APK
flutter build apk --release

# Build optimized APK for specific architecture
flutter build apk --release --target-platform android-arm64
```

**Release APK Location**: `build/app/outputs/flutter-apk/app-release.apk`

### App Bundle (For Google Play Store)

```bash
# Build Android App Bundle (recommended for Play Store)
flutter build appbundle --release
```

**App Bundle Location**: `build/app/outputs/bundle/release/app-release.aab`

---

## 📋 **Testing Instructions**

### Prerequisites
1. **Backend Server Running**: Ensure the QaaqConnect backend is running on `http://localhost:5000`
2. **Database Connected**: Verify the backend connects to the parent QAAQ database
3. **API Accessible**: Test API endpoints are working

### Test Credentials
```
Email: <EMAIL>
Phone: +919029010070
Password: 1234koihai
```

### Testing Steps

1. **Install APK** on Android device
2. **Launch App** - Should show QaaqConnect login screen
3. **Login** with test credentials
4. **Grant Location Permission** when prompted
5. **Test Discovery**:
   - Tap "Koi Hai?" button
   - Should show nearby maritime professionals on map
   - User list should appear at bottom
6. **Test Search**: Type in search bar to find specific users
7. **Test Profile**: Navigate to Profile tab to view user information
8. **Test Navigation**: Switch between Discovery, Chats, and Profile tabs

---

## 🏗️ **Project Structure**

```
flutter-app/
├── android/                 # Android-specific configuration
├── ios/                     # iOS-specific configuration  
├── lib/
│   ├── core/               # Core functionality
│   │   ├── config/         # App configuration
│   │   ├── network/        # HTTP client
│   │   ├── router/         # Navigation
│   │   ├── theme/          # App theming
│   │   └── widgets/        # Shared widgets
│   ├── features/
│   │   ├── auth/           # Authentication (Login/Logout)
│   │   ├── discovery/      # "Koi Hai?" functionality
│   │   ├── profile/        # User profiles
│   │   └── chat/           # Messaging (placeholder)
│   └── main.dart          # App entry point
├── pubspec.yaml           # Dependencies
└── README.md             # Documentation
```

---

## ⚙️ **Configuration Files**

### Key Configuration Updates Needed:

1. **Google Maps API Key**: Replace `YOUR_GOOGLE_MAPS_API_KEY` in AndroidManifest.xml
2. **Backend URL**: Update `baseUrl` in `app_config.dart`
3. **App Signing**: Configure release signing in `android/app/build.gradle`

### Dependencies Included:

```yaml
# State Management & Architecture
flutter_bloc: ^8.1.3
equatable: ^2.0.5

# Navigation
go_router: ^12.1.3

# HTTP & API
dio: ^5.3.2
http: ^1.1.0

# Maps & Location
google_maps_flutter: ^2.5.0
geolocator: ^10.1.0
location: ^5.0.3
geocoding: ^2.1.1

# Security & Storage
flutter_secure_storage: ^9.0.0
shared_preferences: ^2.2.2

# UI & Images
cached_network_image: ^3.3.0
shimmer: ^3.0.0
lucide_icons: ^0.263.0

# Permissions
permission_handler: ^11.1.0

# Utilities
logger: ^2.0.2+1
connectivity_plus: ^5.0.2
intl: ^0.18.1
```

---

## 🚀 **Deployment Options**

### Direct APK Distribution
1. Build release APK
2. Upload to file sharing service
3. Share download link
4. Users enable "Install from Unknown Sources"
5. Install APK directly

### Google Play Store
1. Build App Bundle (`flutter build appbundle --release`)
2. Create Google Play Developer account
3. Upload AAB file to Play Console
4. Complete store listing
5. Submit for review

### Internal Distribution
1. Use Google Play Console Internal Testing
2. Firebase App Distribution
3. TestFlight (iOS)

---

## 🔍 **Troubleshooting**

### Common Issues:

**1. Google Maps Not Loading**
- Verify API key is correct
- Check API key restrictions
- Ensure billing is enabled in Google Cloud

**2. Location Permission Denied**
- Grant location permission in device settings
- Check permission_handler configuration

**3. API Connection Failed**
- Verify backend server is running
- Check network connectivity
- Update API endpoint URL

**4. Build Failures**
```bash
# Clean and rebuild
flutter clean
flutter pub get
flutter build apk
```

**5. Debug APK Installation Issues**
```bash
# Enable USB debugging
# Install via ADB
adb install build/app/outputs/flutter-apk/app-debug.apk
```

---

## 📊 **Features Implemented**

### ✅ **Complete Features**
- [x] User Authentication (Login/Logout)
- [x] JWT Token Management  
- [x] User Profile Display
- [x] GPS Location Services
- [x] Google Maps Integration
- [x] "Koi Hai?" Discovery
- [x] User Search & Filtering
- [x] Distance Calculation
- [x] Maritime Professional UI
- [x] Bottom Navigation
- [x] Responsive Design
- [x] Database Integration (666+ users)

### 🔄 **Coming Soon**
- [ ] Direct Messaging
- [ ] Push Notifications
- [ ] Offline Mode
- [ ] Advanced Filters
- [ ] Profile Editing

---

## 📱 **APK Information**

### Build Details:
- **Target SDK**: Android 13 (API 33)
- **Min SDK**: Android 7.0 (API 24)
- **Architecture**: ARM64 + ARM32 support
- **App Size**: ~15-20 MB (compressed)
- **Permissions**: Location, Internet, Network State

### App Features:
- **Offline Maps**: Basic caching support
- **Location Accuracy**: High accuracy GPS
- **Network Handling**: Automatic retry and error handling
- **Security**: JWT tokens, secure storage
- **Performance**: Optimized for maritime professionals

---

## 🎯 **Next Steps**

1. **Configure Google Maps API Key**
2. **Update Backend URL for your environment**
3. **Build and test APK**
4. **Deploy to test devices**
5. **Collect user feedback**
6. **Prepare for store submission**

---

## 📞 **Support**

For technical support:
1. Check Flutter doctor: `flutter doctor`
2. Review build logs for specific errors
3. Test on multiple devices
4. Verify backend connectivity

**QaaqConnect Flutter App** - Ready for APK generation and maritime professional networking!

---

## 🏁 **Quick Start Commands**

```bash
# Setup and build in one go
cd flutter-app
flutter pub get
flutter build apk --release

# The APK will be ready at:
# build/app/outputs/flutter-apk/app-release.apk
```

**Installation**: Transfer APK to Android device and install (enable "Install from Unknown Sources" if needed).