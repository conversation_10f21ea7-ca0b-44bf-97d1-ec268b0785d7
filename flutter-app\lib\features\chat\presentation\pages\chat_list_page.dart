import 'package:flutter/material.dart';
import 'package:lucide_icons/lucide_icons.dart';

class ChatListPage extends StatelessWidget {
  const ChatListPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Chats'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              LucideIcons.messageCircle,
              size: 64,
              color: Theme.of(context).colorScheme.primary.withOpacity(0.6),
            ),
            const SizedBox(height: 16),
            Text(
              'No Chats Yet',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              'Start discovering maritime professionals\nto begin conversations',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onBackground.withOpacity(0.7),
              ),
              textAlign: TextAlign.center,
            ),
            const Sized<PERSON>ox(height: 24),
            ElevatedButton.icon(
              onPressed: () {
                // Navigate to discovery page
                DefaultTabController.of(context)?.animateTo(0);
              },
              icon: const Icon(LucideIcons.compass),
              label: const Text('Discover Users'),
            ),
          ],
        ),
      ),
    );
  }
}