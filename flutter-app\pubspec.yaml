name: qaaq_connect
description: QaaqConnect - Maritime professional discovery platform featuring "Koi Hai?" GPS-powered location sharing, direct messaging, and professional networking for seafarers.
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.10.0"

dependencies:
  flutter:
    sdk: flutter
  
  # HTTP & API
  http: ^1.1.0
  dio: ^5.3.2
  
  # State Management
  flutter_bloc: ^8.1.3
  equatable: ^2.0.5
  
  # Navigation
  go_router: ^12.1.3
  
  # UI Components
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0
  shimmer: ^3.0.0
  
  # Location & Maps
  google_maps_flutter: ^2.5.0
  location: ^5.0.3
  geolocator: ^10.1.0
  geocoding: ^2.1.1
  
  # Authentication & Storage
  flutter_secure_storage: ^9.0.0
  shared_preferences: ^2.2.2
  jwt_decoder: ^2.0.1
  
  # Utils
  intl: ^0.18.1
  logger: ^2.0.2+1
  connectivity_plus: ^5.0.2
  permission_handler: ^11.1.0
  
  # Icons & Assets
  cupertino_icons: ^1.0.6
  lucide_icons: ^0.263.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.0
  build_runner: ^2.4.7

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
    - assets/logo/
  
  fonts:
    - family: Inter
      fonts:
        - asset: assets/fonts/Inter-Regular.ttf
        - asset: assets/fonts/Inter-Medium.ttf
          weight: 500
        - asset: assets/fonts/Inter-SemiBold.ttf
          weight: 600
        - asset: assets/fonts/Inter-Bold.ttf
          weight: 700