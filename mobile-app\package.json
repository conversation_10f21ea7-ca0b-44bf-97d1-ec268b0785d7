{"name": "qaaqconnect-mobile", "version": "2.0.0", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "dev": "expo start --dev-client", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "submit:android": "eas submit --platform android", "submit:ios": "eas submit --platform ios"}, "dependencies": {"@expo/vector-icons": "^14.0.3", "@react-navigation/bottom-tabs": "^6.6.1", "@react-navigation/native": "^6.1.18", "@react-navigation/stack": "^6.4.1", "@tanstack/react-query": "^5.60.5", "expo": "^53.0.20", "expo-camera": "~14.0.0", "expo-location": "~16.1.0", "expo-status-bar": "~1.11.1", "react": "18.2.0", "react-native": "0.73.6", "react-native-gesture-handler": "~2.14.0", "react-native-maps": "1.10.0", "react-native-paper": "^5.12.5", "react-native-safe-area-context": "4.8.2", "react-native-screens": "~3.29.0", "react-native-svg": "14.1.0", "react-native-vector-icons": "^10.2.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.45", "@types/react-native": "^0.73.0", "typescript": "^5.1.3"}, "private": true}