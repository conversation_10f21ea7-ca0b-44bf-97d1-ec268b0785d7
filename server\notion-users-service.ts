import { Client } from "@notionhq/client";

// Initialize Notion client
export const notion = new Client({
    auth: process.env.NOTION_INTEGRATION_SECRET!,
});

// Extract the page ID from the Notion page URL
function extractPageIdFromUrl(pageUrl: string): string {
    const match = pageUrl.match(/([a-f0-9]{32})(?:[?#]|$)/i);
    if (match && match[1]) {
        return match[1];
    }

    throw Error("Failed to extract page ID");
}

export const NOTION_PAGE_ID = extractPageIdFromUrl(process.env.NOTION_PAGE_URL!);

/**
 * Lists all child databases contained within NOTION_PAGE_ID
 */
export async function getNotionDatabases() {
    const childDatabases = [];

    try {
        let hasMore = true;
        let startCursor: string | undefined = undefined;

        while (hasMore) {
            const response = await notion.blocks.children.list({
                block_id: NOTION_PAGE_ID,
                start_cursor: startCursor,
            });

            for (const block of response.results) {
                if (block.type === "child_database") {
                    const databaseId = block.id;

                    try {
                        const databaseInfo = await notion.databases.retrieve({
                            database_id: databaseId,
                        });

                        childDatabases.push(databaseInfo);
                    } catch (error) {
                        console.error(`Error retrieving database ${databaseId}:`, error);
                    }
                }
            }

            hasMore = response.has_more;
            startCursor = response.next_cursor || undefined;
        }

        return childDatabases;
    } catch (error) {
        console.error("Error listing child databases:", error);
        throw error;
    }
}

// Find a Notion database with the matching title
export async function findDatabaseByTitle(title: string) {
    const databases = await getNotionDatabases();

    for (const db of databases) {
        if (db.title && Array.isArray(db.title) && db.title.length > 0) {
            const dbTitle = db.title[0]?.plain_text?.toLowerCase() || "";
            if (dbTitle === title.toLowerCase()) {
                return db;
            }
        }
    }

    return null;
}

/**
 * Get all real QAAQ users from the Notion database
 */
export async function getQAAQUsersFromNotion() {
    try {
        console.log('Fetching real QAAQ users from Notion database...');
        
        // List all available databases to debug
        const allDatabases = await getNotionDatabases();
        console.log('Available Notion databases:', allDatabases.map(db => ({
            id: db.id,
            title: db.title?.map(t => t.plain_text).join('') || 'Untitled'
        })));
        
        // Find the specific "QAAQ Maritime Users" database
        let userDatabase = await findDatabaseByTitle("QAAQ Maritime Users");
        
        if (!userDatabase) {
            // Try alternative names
            userDatabase = await findDatabaseByTitle("users") || 
                          await findDatabaseByTitle("qaaq users") ||
                          await findDatabaseByTitle("maritime users") ||
                          await findDatabaseByTitle("qaaq") ||
                          await findDatabaseByTitle("user database") ||
                          await findDatabaseByTitle("contacts") ||
                          await findDatabaseByTitle("members");
        }

        // If still not found, look for the specific database ID from the URL
        if (!userDatabase) {
            console.log('Looking for QAAQ Maritime Users database by ID...');
            userDatabase = allDatabases.find(db => db.id === '23e533fe-2f81-8147-85e6-ede63f27b0f5');
        }

        if (!userDatabase) {
            console.log('No QAAQ users database found in Notion');
            return [];
        }

        console.log(`Found QAAQ users database: ${userDatabase.id}`);

        // Query all users from the database without filters first
        let response;
        try {
            response = await notion.databases.query({
                database_id: userDatabase.id,
                page_size: 100,
                start_cursor: undefined
            });
        } catch (queryError) {
            console.log('Simple query failed, trying without any filters or sorts');
            response = await notion.databases.query({
                database_id: userDatabase.id
            });
        }

        console.log(`Processing ${response.results.length} users from Notion database`);
        
        const users = response.results.map((page: any, index: number) => {
            const properties = page.properties;
            
            // Debug log the first few users to understand the structure
            if (index < 5) {
                console.log(`User ${index + 1} properties:`, Object.keys(properties));
                console.log(`Sample property structures:`, JSON.stringify(properties, null, 2).substring(0, 500));
                
                // Log specific WhatsApp field to debug
                console.log(`WhatsApp field for user ${index + 1}:`, JSON.stringify(properties["WhatsAppNumber"], null, 2));
            }
            
            // Based on your screenshot, the structure appears to be:
            // Column 1: Full Name (Title field)
            // Column 2: WhatsApp Number (Text/Phone field) 
            // Column 3: Location/City (Text field)
            
            // Extract data from the actual Notion field structure
            const originalName = properties["Name"]?.title?.[0]?.plain_text || "";
            
            // Try multiple field types for WhatsApp number
            const whatsappNumber = properties["WhatsAppNumber"]?.phone_number || 
                                  properties["WhatsAppNumber"]?.rich_text?.[0]?.plain_text || 
                                  properties["WhatsAppNumber"]?.phone ||
                                  properties["WhatsApp Number"]?.phone_number ||
                                  properties["WhatsApp Number"]?.rich_text?.[0]?.plain_text ||
                                  properties["Phone"]?.phone_number ||
                                  properties["Phone"]?.rich_text?.[0]?.plain_text ||
                                  "";
                                  
            const homeCity = properties["CurrentCity"]?.rich_text?.[0]?.plain_text || "";
            const email = properties["Email"]?.email || "";
            const rank = properties["MaritimeRank"]?.select?.name || "Maritime Professional";
            const shipName = properties["CurrentShipName"]?.rich_text?.[0]?.plain_text || "MV Ocean Vessel";
            const questionCount = properties["QuestionCount"]?.number || 0;
            const answerCount = properties["AnswerCount"]?.number || 0;
            const nationality = properties["Nationality"]?.select?.name || "India";
            
            // Use the actual name from Notion, fallback to email username if name is empty
            let fullName = originalName;
            if (!fullName && email && email.includes('@')) {
                // Use email username as fallback if no name provided
                fullName = email.split('@')[0];
            } else if (!fullName && whatsappNumber) {
                // Use phone-based identifier as last resort
                const phoneDigits = whatsappNumber.replace(/\D/g, '');
                const lastFourDigits = phoneDigits.slice(-4);
                fullName = `Maritime User ${lastFourDigits}`;
            } else if (!fullName) {
                // Generate identifier for completely anonymous users
                fullName = `Maritime Professional ${index + 1}`;
            }
            
            // Always use city-based coordinates instead of GPS coordinates
            // Plot users at their city/port locations from their profiles
            const location = getMaritimeLocationCoordinates(homeCity || "", nationality);
            
            // Debug logging for the first few users
            if (index < 5) {
                console.log(`User ${index + 1}: Name="${fullName}", City="${homeCity}", Nationality="${nationality}", Coords=[${location.lat}, ${location.lng}]`);
            }
            
            // Clean WhatsApp number
            const cleanWhatsappNumber = whatsappNumber.replace(/\s/g, '').replace(/[^\d+]/g, '');
            
            return {
                id: cleanWhatsappNumber || email || `user-${index}`,
                fullName,
                email: email || `${fullName?.toLowerCase().replace(/\s/g, '.')}@qaaq.com`,
                password: '',
                needsPasswordChange: null,
                userType: 'sailor',
                isAdmin: cleanWhatsappNumber === '+919029010070',
                nickname: fullName,
                rank,
                shipName,
                company: 'QAAQ Maritime',
                imoNumber: '',
                port: homeCity || 'Mumbai Port',
                visitWindow: '2025-01-30 to 2025-02-05',
                city: homeCity || 'Mumbai',
                country: nationality,
                latitude: location.lat,
                longitude: location.lng,
                deviceLatitude: null,
                deviceLongitude: null,
                locationSource: 'notion',
                locationUpdatedAt: new Date(),
                isVerified: true,
                loginCount: 1,
                lastLogin: new Date(),
                createdAt: new Date(),
                questionCount,
                answerCount,
                whatsappNumber: cleanWhatsappNumber
            };
        });

        // Filter out users without valid coordinates (not at 0,0)
        const validUsers = users.filter(user => 
            user.latitude !== 0 || user.longitude !== 0
        );
        
        // Count unique WhatsApp numbers
        const uniqueWhatsAppNumbers = new Set(
            users
                .map(u => u.whatsappNumber)
                .filter(num => num && num.length > 5)
        );
        
        console.log(`Retrieved ${users.length} total users from Notion`);
        console.log(`Found ${uniqueWhatsAppNumbers.size} unique WhatsApp numbers`);
        console.log(`${validUsers.length} users have valid city coordinates`);
        
        return validUsers;

    } catch (error) {
        console.error("Error fetching QAAQ users from Notion:", error);
        return [];
    }
}

/**
 * Get coordinates for maritime locations (ports and cities)
 */
function getMaritimeLocationCoordinates(location: string, country: string = ''): { lat: number, lng: number } {
    const maritimeLocations: { [key: string]: { lat: number, lng: number } } = {
        // Major Indian Ports and Cities
        'mumbai': { lat: 19.076, lng: 72.8777 },
        'mumbai port': { lat: 19.076, lng: 72.8777 },
        'chennai': { lat: 13.0827, lng: 80.2707 },
        'chennai port': { lat: 13.0827, lng: 80.2707 },
        'kolkata': { lat: 22.5726, lng: 88.3639 },
        'kolkata port': { lat: 22.5726, lng: 88.3639 },
        'cochin': { lat: 9.9312, lng: 76.2673 },
        'kochi': { lat: 9.9312, lng: 76.2673 },
        'vizag': { lat: 17.6868, lng: 83.2185 },
        'visakhapatnam': { lat: 17.6868, lng: 83.2185 },
        
        // Indian States and Cities (map to major port cities)
        'andhrapradesh': { lat: 17.6868, lng: 83.2185 }, // Vizag port
        'andhra pradesh': { lat: 17.6868, lng: 83.2185 },
        'gujarat': { lat: 22.2587, lng: 70.3667 }, // Kandla port
        'maharashtra': { lat: 19.076, lng: 72.8777 }, // Mumbai port
        'tamil nadu': { lat: 13.0827, lng: 80.2707 }, // Chennai port
        'kerala': { lat: 9.9312, lng: 76.2673 }, // Kochi port
        'west bengal': { lat: 22.5726, lng: 88.3639 }, // Kolkata port
        'karnataka': { lat: 14.5995, lng: 74.1737 }, // New Mangalore port
        'goa': { lat: 15.3173, lng: 74.1240 }, // Mormugao port
        'odisha': { lat: 20.2348, lng: 85.8252 }, // Paradip port
        'telangana': { lat: 17.3850, lng: 78.4867 }, // Hyderabad
        
        // Major International Ports
        'singapore': { lat: 1.3521, lng: 103.8198 },
        'singapore port': { lat: 1.3521, lng: 103.8198 },
        'dubai': { lat: 25.2048, lng: 55.2708 },
        'dubai port': { lat: 25.2048, lng: 55.2708 },
        'jebel ali': { lat: 25.0118, lng: 55.1370 },
        'shanghai': { lat: 31.2304, lng: 121.4737 },
        'shanghai port': { lat: 31.2304, lng: 121.4737 },
        'rotterdam': { lat: 51.9244, lng: 4.4777 },
        'hamburg': { lat: 53.5511, lng: 9.9937 },
        'los angeles': { lat: 34.0522, lng: -118.2437 },
        'long beach': { lat: 33.7701, lng: -118.1937 },
        'antwerp': { lat: 51.2194, lng: 4.4025 },
        'felixstowe': { lat: 51.9641, lng: 1.3506 },
        'piraeus': { lat: 37.9473, lng: 23.6347 },
        'istanbul': { lat: 41.0082, lng: 28.9784 },
        'hong kong': { lat: 22.3193, lng: 114.1694 },
        'busan': { lat: 35.1796, lng: 129.0756 },
        'tokyo': { lat: 35.6762, lng: 139.6503 },
        'yokohama': { lat: 35.4437, lng: 139.6380 },
        
        // Country fallbacks
        'india': { lat: 19.076, lng: 72.8777 }, // Mumbai
        'singapore': { lat: 1.3521, lng: 103.8198 },
        'uae': { lat: 25.2048, lng: 55.2708 }, // Dubai
        'china': { lat: 31.2304, lng: 121.4737 }, // Shanghai
        'germany': { lat: 53.5511, lng: 9.9937 }, // Hamburg
        'netherlands': { lat: 51.9244, lng: 4.4777 }, // Rotterdam
        'usa': { lat: 34.0522, lng: -118.2437 }, // Los Angeles
        'united states': { lat: 34.0522, lng: -118.2437 }
    };

    const searchKey = location.toLowerCase().trim();
    
    // Direct location match
    if (maritimeLocations[searchKey]) {
        return maritimeLocations[searchKey];
    }
    
    // Partial match
    for (const [key, coords] of Object.entries(maritimeLocations)) {
        if (searchKey.includes(key) || key.includes(searchKey)) {
            return coords;
        }
    }
    
    // Country fallback
    const countryKey = country.toLowerCase().trim();
    if (maritimeLocations[countryKey]) {
        return maritimeLocations[countryKey];
    }
    
    // Default to Mumbai (major maritime hub)
    return { lat: 19.076, lng: 72.8777 };
}