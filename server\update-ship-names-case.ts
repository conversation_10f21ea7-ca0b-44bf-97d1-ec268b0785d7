import { db } from "./db";
import { users } from "@shared/schema";
import { eq } from "drizzle-orm";

const shipNameUpdates = [
  { email: "<EMAIL>", newShipName: "MV Singapore Star" },
  { email: "<EMAIL>", newShipName: "MV Rotterdam Express" },
  { email: "officer.rod<PERSON><PERSON><PERSON>@qaaq.com", newShipName: "MV Panama Bridge" },
  { email: "<EMAIL>", newShipName: "MV Dubai Pearl" },
  { email: "<EMAIL>", newShipName: "MV Shanghai Dragon" },
  { email: "<EMAIL>", newShipName: "MV Hamburg Trader" },
  { email: "<EMAIL>", newShipName: "MV Mumbai Queen" }
];

async function updateShipNamesCase() {
  console.log("Starting to update ship names to proper case...");
  
  try {
    for (const update of shipNameUpdates) {
      await db
        .update(users)
        .set({ shipName: update.newShipName })
        .where(eq(users.email, update.email));
      console.log(`Updated ship name for ${update.email}: ${update.newShipName}`);
    }
    
    console.log("Ship name case updates completed successfully!");
  } catch (error) {
    console.error("Error updating ship names:", error);
  }
}

// Run the update function
updateShipNamesCase().then(() => process.exit(0));