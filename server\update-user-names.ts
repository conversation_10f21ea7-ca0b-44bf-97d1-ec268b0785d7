import { db } from "./db";
import { users } from "@shared/schema";
import { eq } from "drizzle-orm";

const nameUpdates = [
  { email: "<EMAIL>", newName: "<PERSON>" },
  { email: "<EMAIL>", newName: "<PERSON>" },
  { email: "officer.rod<PERSON><PERSON><PERSON>@qaaq.com", newName: "<PERSON>" },
  { email: "<EMAIL>", newName: "<PERSON>" },
  { email: "<EMAIL>", newName: "<PERSON>" },
  { email: "<EMAIL>", newName: "<PERSON>" },
  { email: "<EMAIL>", newName: "<PERSON>" },
  { email: "<EMAIL>", newName: "Yu<PERSON>" }
];

async function updateUserNames() {
  console.log("Starting to update user names...");
  
  try {
    for (const update of nameUpdates) {
      await db
        .update(users)
        .set({ fullName: update.newName })
        .where(eq(users.email, update.email));
      console.log(`Updated name for ${update.email} to ${update.newName}`);
    }
    
    console.log("Name updates completed successfully!");
  } catch (error) {
    console.error("Error updating user names:", error);
  }
}

// Run the update function
updateUserNames().then(() => process.exit(0));